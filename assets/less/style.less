* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  border-style: solid;
  border-width: 0;
  border-color: var(--un-default-border-color, #e5e7eb);
}

:root {
  --primary: #f94c30;
  /* 主色调 - 亮橙 */
  --secondary: #2a2a35;
  /* 深灰色 - 主要背景 */
  --light: #ffffff;
  /* 白色 - 文本 */
  --dark: #1e1e26;
  /* 更深的背景色 - 基于 secondary */
  --gray: #3f3f4a;
  /* 中间调灰色 - 基于 secondary */
  --light-gray: #b0b0c0;
  /* 浅灰色 - 次要文本 */
}

html {
  overflow-x: hidden;
}

body {
  background-color: var(--dark);
  color: var(--light-gray);
  overflow-x: hidden;
}
body {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  overflow-x: hidden;
}

// #__nuxt {
//   height: 100%;
// }

ul,
li {
  list-style: none;
}

a {
  color: inherit;
  text-decoration: none;
  &:active,
  &:visited {
    color: inherit;
  }
}

:root {
  --chat-user-width: 70%;
}

input:-webkit-autofill {
  filter: none; /* needed for firefox! */
  box-shadow: 0 0 0 100px #353541 inset;
  transition: background-color 5000s ease-in-out 0s !important;
  -webkit-text-fill-color: #fff;
}

#wx-login {
  iframe {
    width: 100%;
    height: 100%;
  }
}
