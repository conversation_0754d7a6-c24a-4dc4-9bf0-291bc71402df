// @bg: #353541;
// .ant-modal {
//   .ant-modal-header,
//   .ant-modal-content {
//     background-color: #2a2a35;
//   }
// }

// .ant-tooltip {
//   --antd-arrow-background-color: #2a2a35;
//   .ant-tooltip-inner {
//     background-color: #2a2a35;
//   }
// }

// .ant-input {
//   background-color: @bg;
// }
// .ant-select {
//   .ant-select-selector {
//     background-color: @bg !important;
//   }
// }

// .ant-picker {
//   background-color: @bg;
// }
// .ant-picker-dropdown {
//   .ant-picker-panel-container {
//     background-color: #2a2a35;
//   }
// }

// .ant-input-number-input {
//   background-color: #1a1a22;
//   .ant-input-number {
//     .ant-input-number-affix-wrapper {
//       background-color: #1a1a22 !important;
//     }
//   }
// }
