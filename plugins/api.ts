export default defineNuxtPlugin((app) => {
  const baseURL = useRuntimeConfig().public.VITE_API_BASE
  // const mallUrl = useRuntimeConfig().public.apiBase + '/mall'
  const _fetch = $fetch.create({
    baseURL,
    onRequest(ctx) {
      const token = useCookie(TOKEN)
      if (token.value) {
        ctx.options.headers.set('Authorization', token.value)
      }
    },
    onResponse(ctx) {
      const _data = ctx.response._data
      if (_data?.code != '00000' && _data?.code != 'ok') {
        if (import.meta.client) {
          message.error(_data.msg || _data.message)
        }
        if (_data?.code == 'A00004') {
          userStore().clearUser()
          return Promise.reject()
        }
        return Promise.reject(_data)
      }
    },
  })

  return {
    provide: {
      _fetch,
    },
  }
})
