import { userStore } from '~/store/user'
import { companyStore } from '~/store/company'
import { getMerchantByMerchantId } from '~/api/mall-manage/index'

const authRoutes = []

const getCompany = async (userMobile) => {
  const res = await http('/mall/shop/userSpace/getOneByCond', {
    method: 'get',
    params: {
      userMobile
    }
  })
  return res
}

export default defineNuxtRouteMiddleware(async (to) => {
  if (import.meta.server) return
  const company = companyStore()
  if (company.company.shopCompanyId && (to.name as string).startsWith('workSpace-my-space')) {
    return navigateTo({ 
      path: '/workSpace/company-space/selection-library',
      query: {
        companyId: company.company.shopCompanyId
      }
    })
  }
  if (!company.company.shopCompanyId && (to.name as string).startsWith('workSpace-company-space')) {
    console.log('企业ID不存在,无法进入企业空间')
    return navigateTo({ path: '/' })
  }
})
