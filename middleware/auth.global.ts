import { MemberLevel, userStore } from '~/store/user'
import { companyStore } from '~/store/company'
import { authMenuStore } from '~/store/authMenu'
import { getMerchantByMerchantId } from '~/api/mall-manage/index'

const authRoutes = [
  'my-info',
  'my-security',
  'my-favourite',
  'my-invite',
  'my-score',
  'my-member',
  'message-system',
  'message-interaction',
  'message-customer-service',
  'ans-price',
  'switch',
  'message-center',
]

// TODO 普通会员没有的菜单权限 跳转404
const normalMemberMenu = ['/workSpace']

const getUserInfo = async () => {
  const res = await http('/mall/p/user/userInfo')
  return res
}

const getCompany = async (userMobile) => {
  const res = await http('/mall/shop/userSpace/getOneByCond', {
    method: 'get',
    params: {
      userMobile,
    },
  })
  return res
}

const getAuthMenu = async () => {
  const res = await http(`/mall/p/sys/enterpriseMenu/auth`, {
    method: 'get',
  })
  return res
}

export default defineNuxtRouteMiddleware(async (to, from) => {
  // console.log('mmm', useCookie(BBC_TOKEN).value)
  // if (import.meta.server) return
  const token = useCookie(TOKEN, {
    domain: useRuntimeConfig().public.VITE_DOMAIN,
  })
  async function third_auth() {
    const _token = to.query.token
    if (to.name == 'code') return false
    if (_token) {
      token.value = _token as string
      return true
    }
    return false
  }
  const _auth = await third_auth()
  if (_auth) {
    delete to.query.token
    return navigateTo({ ...to })
  }
  const user = userStore()
  const company = companyStore()
  const authMenu = authMenuStore()
  if (token.value) {
    if (!user.user.userId) {
      try {
        const res = await getUserInfo()
        user.setUser(res.data)
        if (user.user.userMobile && company.company.shopCompanyId === undefined) {
          // 非普通会员登录检查是否在企业空间
          const res = await getCompany(user.user.userMobile)
          await useMall(res, async (ret) => {
            if (ret && ret.merchantId) {
              // 查询企业
              const res = await getMerchantByMerchantId({ merchantId: ret.merchantId })
              await useMall(res, (ret) => {
                const _ret = JSON.parse(ret)
                company.setCompany({ ..._ret })
              })
            } else {
              company.setCompany({
                shopCompanyId: 0, // 个人空间
              })
            }
          })
        }
        if (company.company.shopCompanyId && !authMenu.authMenu.loadedMenu) {
          const res = await getAuthMenu()
          await useMall(res, async (ret) => {
            authMenu.setAuthMenu({
              ...ret,
              loadedMenu: true,
            })
          })
        }
      } catch (error) {
        user.clearUser()
        console.log('%c Line:84 🥛 error', 'color:#EA3A48', error)
      }
    }

    if (from.path.includes('/login') && to.path == '/') {
      if (import.meta.client && company.isPersonal) {
        Guide.show(() => {
          navigateTo('/enterpriseCenter/create')
        })
      }
    }
    if (user.user.userId) {
      if (to.path.includes('/login')) {
        return navigateTo({ path: '/' })
      }
    }
  }
  if (authRoutes.includes(to.name as string) || to.path.startsWith('/workSpace') || to.path.startsWith('/userCenter')) {
    if (!token.value || !user.user.userId) {
      if (import.meta.client) {
        message.destroy()
        message.error('登录状态已过期,跳转到登录页面...')
      }
      return navigateTo({
        path: '/login',
        query: {
          type: 'LOGIN',
          redirect: to.fullPath,
        },
      })
    }
  }
})
