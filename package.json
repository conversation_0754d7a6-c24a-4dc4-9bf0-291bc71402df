{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build:prod": "nuxt build --dotenv prod", "build:dev": "nuxt build --dotenv dev", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@ant-design-vue/nuxt": "1.4.6", "@ant-design/icons-vue": "^7.0.1", "@microsoft/fetch-event-source": "^2.0.1", "@vueuse/core": "^12.0.0", "@vueuse/nuxt": "12.0.0", "ant-design-vue": ">=4", "aos": "^2.3.4", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "js-file-download": "^0.4.12", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "md5": "^2.3.0", "mitt": "^3.0.1", "nuxt": "^3.14.1592", "vue": "latest", "vue-router": "latest"}, "devDependencies": {"@iconify-json/ant-design": "^1.2.3", "@iconify-json/fa-brands": "^1.2.1", "@iconify-json/fa-solid": "^1.2.1", "@iconify/utils": "^2.3.0", "@pinia/nuxt": "^0.9.0", "@types/crypto-js": "^4.2.2", "@types/markdown-it": "^14.1.2", "@unocss/nuxt": "^0.65.1", "js-base64": "^3.7.7", "less": "^4.2.1", "overlayscrollbars": "^2.10.1", "overlayscrollbars-vue": "^0.5.9", "pinia": "^2.3.0", "unocss": "^0.65.1", "vite": "^6.0.6"}, "packageManager": "pnpm@9.14.2+sha512.6e2baf77d06b9362294152c851c4f278ede37ab1eba3a55fda317a4a17b209f4dbb973fb250a77abc463a341fcb1f17f17cfa24091c4eb319cda0d9b84278387"}