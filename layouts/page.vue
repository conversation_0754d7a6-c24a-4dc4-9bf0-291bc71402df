<template>
  <header :class="{ scrolled: isScrolled }">
    <div class="container">
      <nav class="navbar">
        <div class="logo cursor-pointer" @click="navigateTo('/')">
          <img src="~/assets/images/logo_rect_white.png" alt="logo" />
        </div>
        <div class="nav-links">
          <!-- <div
            class="nav-item dropdown"
            @mouseenter="showDropdown('solutions')"
            @mouseleave="hideDropdown('solutions')"
          >
            <a href="javascript:void(0)" class="nav-link">解决方案</a>
            <div class="dropdown-content" :class="{ show: activeDropdown === 'solutions' }">
              <nuxt-link to="/rd-base" class="dropdown-link">装备研发标准库系统</nuxt-link>
            </div>
          </div>

          <div
            class="nav-item dropdown"
            @mouseenter="showDropdown('downloads')"
            @mouseleave="hideDropdown('downloads')"
          >
            <a href="javascript:void(0)" class="nav-link">下载中心</a>
            <div class="dropdown-content" :class="{ show: activeDropdown === 'downloads' }">
              <nuxt-link to="/plugin" class="dropdown-link">SolidWorks插件</nuxt-link>
            </div>
          </div> -->
          <nuxt-link to="/rd-base" class="nav-link">解决方案</nuxt-link>
          <nuxt-link to="/plugin" class="nav-link">下载中心</nuxt-link>

          <nuxt-link to="/supplier" class="nav-link">供应商合作</nuxt-link>
          <a href="javascript:void(0)" class="nav-link" @click="openInNewTab(bomai)">零部件库</a>
        </div>

        <div class="nav-buttons">
          <nuxt-link class="btn btn-link content-center" v-if="company.shopCompanyId == 0" :to="`${bomai}/brand-join`">
            成为供应商
          </nuxt-link>
          <div class="btn btn-link content-center" @click.prevent="manageBrand" v-if="isSupplier">
            <a>供应商后台</a>
          </div>

          <!-- 未登录状态显示 -->
          <div v-if="!isLogin" class="login-buttons">
            <nuxt-link class="btn btn-outline" to="/login">登录</nuxt-link>
            <nuxt-link class="btn btn-primary" to="/register">免费注册</nuxt-link>
          </div>

          <!-- 已登录状态显示 -->
          <div v-else class="user-menu">
            <div class="notification-icon" @click="navigateTo('/message-center')">
              <i class="fas i-fa-bell"></i>
              <span class="notification-badge" v-if="unreadCount">{{ unreadCount }}</span>
            </div>
            <div class="user-dropdown">
              <div class="user-info" @click="toggleUserDropdown">
                <div class="avatar">
                  <img :src="useImage(useObs(user.pic))" alt="用户头像" />
                </div>
                <div class="user-name-container max-w-150px">
                  <span class="username">{{ user.nickName }}</span>
                  <span v-if="company.merchantName" class="company-name truncate" :title="company.merchantName">
                    {{ company.merchantName }}
                  </span>
                </div>
                <i class="fas i-fa-chevron-down"></i>
              </div>
              <div class="dropdown-menu" :class="{ show: isUserDropdownOpen }">
                <nuxt-link to="/userCenter" class="dropdown-item">
                  <i class="fas i-fa-user"></i>
                  个人中心
                </nuxt-link>
                <div class="dropdown-divider"></div>
                <a href="#" class="dropdown-item" @click.prevent="handleLogout">
                  <i class="fas i-fa-sign-out-alt"></i>
                  退出登录
                </a>
              </div>
            </div>
          </div>
        </div>
        <button class="mobile-nav-toggle" @click="toggleMobileNav" aria-label="Toggle navigation">
          <span class="hamburger-icon"></span>
        </button>
      </nav>
    </div>
    <div class="mobile-menu" :class="{ 'is-open': isMobileNavOpen }">
      <div class="mobile-menu-links">
        <div class="mobile-dropdown">
          <span class="mobile-dropdown-title">解决方案</span>
          <div class="mobile-dropdown-items">
            <nuxt-link to="/rd-base" @click="closeMobileNav">装备研发标准库系统</nuxt-link>
          </div>
        </div>
        <div class="mobile-dropdown">
          <span class="mobile-dropdown-title">下载中心</span>
          <div class="mobile-dropdown-items">
            <nuxt-link to="/plugin" @click="closeMobileNav">SolidWorks插件</nuxt-link>
          </div>
        </div>
        <nuxt-link to="/supplier" @click="closeMobileNav">供应商合作</nuxt-link>
        <a href="javascript:void(0)" @click="openInNewTab(bomai)">零部件库</a>
      </div>
      <div class="mobile-menu-buttons">
        <a href="#" class="btn btn-outline">登录</a>
        <a href="#" class="btn btn-primary">免费注册</a>
      </div>
    </div>
  </header>
  <slot />
  <footer id="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-info">
          <div class="footer-logo">
            <img src="~/assets/images/logo_rect_white.png" alt="logo" />
          </div>
          <p class="footer-desc">
            我们致力于为自动化装备制造行业提供智能化、高效率的供应链解决方案，帮助企业降低成本，提升竞争力。
          </p>
          <!-- <div class="footer-social">
            <a href="#" class="social-icon"><i class="fab fa-weixin"></i></a>
            <a href="#" class="social-icon"><i class="fab fa-weibo"></i></a>
            <a href="#" class="social-icon"><i class="fab fa-linkedin"></i></a>
            <a href="#" class="social-icon"><i class="fab fa-youtube"></i></a>
          </div> -->
        </div>
        <div class="footer-links">
          <h4>协议政策</h4>
          <ul>
            <li>
              <nuxt-link :to="{ path: '/agreement', query: { key: 'termOfService' } }" target="_blank">
                用户协议
              </nuxt-link>
            </li>
            <li>
              <nuxt-link :to="{ path: '/agreement', query: { key: 'privacyPolicy' } }" target="_blank">
                隐私政策
              </nuxt-link>
            </li>
            <li>
              <nuxt-link :to="{ path: '/agreement', query: { key: 'disclaimer' } }" target="_blank">免责声明</nuxt-link>
            </li>
          </ul>
        </div>
        <!-- <div class="footer-links">
          <h4>关于我们</h4>
          <ul>
            <li><a href="#">公司简介</a></li>
            <li><a href="#">发展历程</a></li>
            <li><a href="#">团队介绍</a></li>
            <li><a href="#">新闻动态</a></li>
            <li><a href="#">加入我们</a></li>
          </ul>
        </div> -->
        <div class="footer-links">
          <h4>联系方式</h4>
          <div style="display: flex; gap: 20px; align-items: flex-start">
            <ul style="flex: 1">
              <!-- <li>
                <a href="#">
                  <i class="fas i-fa-phone-alt"></i>
                  ************
                </a>
              </li> -->
              <li>
                <a href="#">
                  <i class="fas i-fa-envelope"></i>
                  <EMAIL>
                </a>
              </li>
              <li>
                <a href="#">
                  <i class="fas i-fa-map-marker-alt"></i>
                  江苏省苏州市工业园区启泰路66号国泰新点软件研发楼509A
                </a>
              </li>
            </ul>
            <div style="flex-shrink: 0">
              <img src="~/assets/images/qrcode.jpg" alt="扫码关注我们" style="width: 100px; height: auto" />
            </div>
          </div>
        </div>
      </div>
      <div class="copyright">
        <p>
          Copyright©2025 研选工场（苏州）网络有限公司 |
          <a href="https://beian.miit.gov.cn/">苏ICP备2024149956号</a>
        </p>
      </div>
    </div>
  </footer>
</template>

<script setup>
import AOS from 'aos'
import 'aos/dist/aos.css'

const isLogin = useLoginState()
const user = computed(() => userStore().user)
const company = computed(() => companyStore().company)
const unreadCount = computed(() => usemessageStore().msgCount)

const { logout } = useLogout()

const bomai = useRuntimeConfig().public.VITE_BOMAI_URL

// Mobile Nav State
const isMobileNavOpen = ref(false)
// Header Scroll State
const isScrolled = ref(false)

// User dropdown state
const isUserDropdownOpen = ref(false)

// Navigation dropdown state
const activeDropdown = ref(null)

const handleLogout = () => {
  logout()
  isUserDropdownOpen.value = false
}

const toggleMobileNav = () => {
  isMobileNavOpen.value = !isMobileNavOpen.value
  document.body.style.overflow = isMobileNavOpen.value ? 'hidden' : ''
}

const closeMobileNav = () => {
  isMobileNavOpen.value = false
  document.body.style.overflow = ''
}

const toggleUserDropdown = () => {
  isUserDropdownOpen.value = !isUserDropdownOpen.value
}

const closeUserDropdown = (event) => {
  if (isUserDropdownOpen.value && !event.target.closest('.user-dropdown')) {
    isUserDropdownOpen.value = false
  }
}

const handleScroll = () => {
  isScrolled.value = window.scrollY > 0
}

onMounted(() => {
  AOS.init({
    duration: 800,
    once: false,
    offset: 100,
    delay: 100,
  })
  window.addEventListener('scroll', handleScroll)
  window.addEventListener('click', closeUserDropdown)
  handleScroll()
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
  window.removeEventListener('click', closeUserDropdown)
})

const { manageBrand } = useManageBrand(company)

const isSupplier = computed(() => {
  return company.value.type == 2 || company.value.type == 3
})

const navigateTo = (path) => {
  window.location.href = path
}

// Navigation dropdown methods
const showDropdown = (dropdownName) => {
  activeDropdown.value = dropdownName
}

const hideDropdown = (dropdownName) => {
  if (activeDropdown.value === dropdownName) {
    activeDropdown.value = null
  }
}

const openInNewTab = (url) => {
  window.open(url, '_blank')
}
</script>

<style lang="less" scoped>
/* 导航栏 */
.container {
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;
  padding: 0 80px;
}
header {
  position: fixed;
  width: 100%;
  z-index: 1000;
  top: 0;
  transition: background-color 0.3s ease-in-out, box-shadow 0.3s ease-in-out, border-bottom 0.3s ease-in-out;

  &.scrolled {
    background-color: rgba(30, 30, 38, 0.8);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
  }

  .logo {
    width: 200px;
    height: 32px;
    display: flex;
    align-items: center;

    img {
      max-width: 100%;
      max-height: 100%;
      object-fit: cover;
    }
  }

  .nav-links {
    display: flex;
    align-items: center;
    gap: 40px;

    .nav-item {
      position: relative;
    }

    .nav-link {
      color: var(--light);
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s ease;
      padding: 10px 0;
      display: block;

      &:hover {
        color: var(--primary);
      }
    }

    .dropdown {
      .dropdown-content {
        position: absolute;
        top: 100%;
        left: 0;
        background-color: var(--secondary);
        min-width: 200px;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        opacity: 0;
        transform: translateY(-10px);
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        margin-top: 10px;

        &.show {
          opacity: 1;
          transform: translateY(0);
          visibility: visible;
        }

        &::before {
          content: '';
          position: absolute;
          top: -6px;
          left: 20px;
          width: 12px;
          height: 12px;
          background-color: var(--secondary);
          transform: rotate(45deg);
          border-left: 1px solid rgba(255, 255, 255, 0.1);
          border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .dropdown-link {
          display: block;
          padding: 12px 16px;
          color: var(--light);
          text-decoration: none;
          transition: background-color 0.3s ease;
          border-radius: 6px;
          margin: 4px;

          &:hover {
            background-color: rgba(249, 76, 48, 0.1);
            color: var(--primary);
          }
        }
      }
    }
  }

  .nav-buttons {
    display: flex;
    gap: 20px;

    .login-buttons {
      display: flex;
      align-items: center;
      gap: 20px;
    }
  }
}

.btn {
  padding: 10px 24px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  text-decoration: none;
  display: inline-block;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
  z-index: 1;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transition: left 0.3s ease-in-out;
    z-index: -1;
  }

  &:hover::before {
    left: 0;
  }
}

.btn-primary {
  background-color: var(--primary);
  color: var(--light);
  border-color: var(--primary);

  &:hover {
    background-color: #e03a20;
    border-color: #e03a20;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(249, 76, 48, 0.4);
  }
}

.btn-outline {
  background-color: transparent;
  color: var(--light);
  border: 1px solid var(--light-gray);

  &:hover {
    background-color: var(--primary);
    color: var(--light);
    border-color: var(--primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(249, 76, 48, 0.4);
  }
}

.btn-link {
  background-color: transparent;
  color: var(--light);
  border: none;
  padding: 10px 15px;
  line-height: 1.5;

  &:hover {
    color: var(--primary);
  }
}

/* 用户菜单样式 */
.user-menu {
  display: flex;
  align-items: center;
  gap: 20px;
}

.notification-icon {
  position: relative;
  cursor: pointer;

  i {
    font-size: 20px;
    color: var(--light);
    transition: color 0.3s;
  }

  &:hover i {
    color: var(--primary);
  }

  .notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--primary);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }
}

.user-dropdown {
  position: relative;

  .user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: background-color 0.3s;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    .user-name-container {
      display: flex;
      flex-direction: column;

      .username {
        color: var(--light);
        font-weight: 500;
      }

      .company-name {
        color: var(--light-gray);
        line-height: 1.2;
      }
    }

    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      border: 2px solid var(--primary);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    i {
      color: var(--light-gray);
      font-size: 12px;
      transition: transform 0.3s;
    }
  }

  &:hover .user-info i {
    color: var(--primary);
  }

  .dropdown-menu.show ~ .user-info i,
  .user-info:hover i {
    transform: rotate(180deg);
  }

  .dropdown-menu {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background-color: var(--secondary);
    border-radius: 8px;
    min-width: 200px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0;
    transform: translateY(-10px);
    visibility: hidden;
    transition: all 0.3s;
    z-index: 1001;

    &.show {
      opacity: 1;
      transform: translateY(0);
      visibility: visible;
    }

    &::before {
      content: '';
      position: absolute;
      top: -6px;
      right: 20px;
      width: 12px;
      height: 12px;
      background-color: var(--secondary);
      transform: rotate(45deg);
      border-left: 1px solid rgba(255, 255, 255, 0.1);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .dropdown-item {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 12px 16px;
      color: var(--light);
      text-decoration: none;
      transition: background-color 0.3s;

      i {
        width: 16px;
        color: var(--light-gray);
      }

      &:hover {
        background-color: rgba(249, 76, 48, 0.1);

        i {
          color: var(--primary);
        }
      }

      &:first-child {
        border-radius: 8px 8px 0 0;
      }

      &:last-child {
        border-radius: 0 0 8px 8px;
      }
    }

    .dropdown-divider {
      height: 1px;
      background-color: rgba(255, 255, 255, 0.1);
      margin: 6px 0;
    }
  }
}

/* Mobile Nav Toggle Button */
.mobile-nav-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 10px;
  z-index: 1001;
  position: relative;
}

.hamburger-icon {
  display: block;
  width: 24px;
  height: 2px;
  background-color: var(--light);
  position: relative;
  transition: background-color 0.3s ease-in-out;
}

.hamburger-icon::before,
.hamburger-icon::after {
  content: '';
  position: absolute;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--light);
  transition: transform 0.3s ease-in-out, top 0.3s ease-in-out;
}

.hamburger-icon::before {
  top: -7px;
}

.hamburger-icon::after {
  top: 7px;
}

/* Mobile Menu Styles */
.mobile-menu {
  position: fixed;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100vh;
  background-color: var(--secondary);
  transition: left 0.3s ease-in-out;
  z-index: 999;
  display: flex;
  flex-direction: column;
  padding-top: 80px;

  &.is-open {
    left: 0;
  }

  .mobile-menu-links {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    > a {
      color: var(--light);
      text-decoration: none;
      font-size: 18px;
      font-weight: 500;
      padding: 15px 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .mobile-dropdown {
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding: 15px 0;

      .mobile-dropdown-title {
        color: var(--light);
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 10px;
        display: block;
      }

      .mobile-dropdown-items {
        padding-left: 20px;

        a {
          color: var(--light-gray);
          text-decoration: none;
          font-size: 16px;
          display: block;
          padding: 8px 0;
          transition: color 0.3s;

          &:hover {
            color: var(--primary);
          }
        }
      }
    }
  }

  .mobile-menu-buttons {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
}

/* Responsive Design */
@media (max-width: 992px) {
  .nav-links {
    display: none;
  }

  .mobile-nav-toggle {
    display: block;
  }

  .user-menu {
    .user-name-container {
      display: none;
    }

    .user-info i {
      display: none;
    }
  }
}

@media (max-width: 768px) {
  .notification-icon {
    i {
      font-size: 18px;
    }

    .notification-badge {
      width: 16px;
      height: 16px;
      font-size: 10px;
    }
  }

  .user-dropdown .avatar {
    width: 32px;
    height: 32px;
  }
}

@media (max-width: 480px) {
  .user-menu {
    gap: 12px;
  }
}

/* 页脚 */
footer {
  background-color: var(--dark);
  color: var(--light-gray);
  padding: 80px 0 30px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);

  .footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 2fr;
    gap: 60px;
    margin-bottom: 50px;
  }

  .footer-info {
    max-width: 400px;
  }

  .footer-logo {
    width: 200px;
    height: 32px;
    margin-bottom: 20px;

    img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
  }

  .footer-desc {
    font-size: 15px;
    line-height: 1.6;
    color: var(--light-gray);
    margin-bottom: 24px;
  }

  .footer-social {
    display: flex;
    gap: 16px;
  }

  .social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--gray);
    transition: all 0.3s;

    i {
      color: var(--light);
      font-size: 18px;
    }

    &:hover {
      background-color: var(--primary);
      transform: scale(1.1);
    }
  }

  .footer-links {
    h4 {
      font-size: 18px;
      margin-bottom: 24px;
      color: var(--light);
    }

    ul {
      list-style: none;
    }

    li {
      margin-bottom: 12px;
    }

    a {
      color: var(--light-gray);
      text-decoration: none;
      transition: color 0.3s;
      font-size: 15px;

      &:hover {
        color: var(--primary);
      }

      i {
        // Style icons within links
        margin-right: 8px;
      }
    }

    img {
      max-width: 100px;
      height: auto;
      margin-bottom: 10px;
    }
  }

  .copyright {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.5);
    font-size: 14px;
  }
}

@media (max-width: 992px) {
  header .navbar {
    padding: 15px 0;
  }

  header .logo {
    width: 160px;
    /* Smaller logo */
  }

  header .nav-links {
    display: none;
    /* Hide desktop links */
  }

  header .nav-buttons {
    gap: 10px;

    /* Hide desktop buttons if mobile menu is open */
    /* This selector might need adjustment depending on final structure */
    .mobile-menu.is-open ~ .container & {
      // Check if this selector works correctly
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.3s;
    }
  }

  /* Show hamburger only on smaller screens */
  .mobile-nav-toggle {
    display: block;
  }

  .hero {
    padding-top: 73px;
    text-align: center;
  }

  .hero .hero-content {
    flex-direction: column;
    /* Stack hero content */
    text-align: center;
  }

  .hero .hero-text {
    padding-right: 0;
    margin-bottom: 40px;
  }

  .hero .hero-title {
    font-size: 38px;
  }

  .hero .hero-subtitle {
    font-size: 18px;
  }

  .section-title h2 {
    font-size: 30px;
  }

  .section-title p {
    font-size: 16px;
  }

  .features {
    padding: 80px 0;
  }

  .core-sections {
    padding: 80px 0;
  }

  .register {
    padding: 80px 0;
  }

  .partners {
    padding: 60px 0;
  }

  .cta {
    padding: 80px 0;
  }

  footer {
    padding: 60px 0 20px;
  }

  .features .feature-grid,
  .core-sections .core-grid,
  .ai-assistants .ai-partners-container {
    grid-template-columns: 1fr;
    /* Single column for smaller devices */
    gap: 30px;
  }

  .register .register-container {
    flex-direction: column;
    /* Stack register content */
  }

  .register .register-image {
    margin-bottom: 40px;
    text-align: center;
    /* Center image if needed */
  }

  .register .register-content {
    text-align: center;
    /* Center text */
  }

  .register .register-points {
    display: inline-block;
    /* Center points container */
    text-align: left;
    /* Align text left within container */
  }

  .register .register-buttons {
    justify-content: center;
  }

  .cta .cta-title {
    font-size: 34px;
  }

  .cta .cta-subtitle {
    font-size: 16px;
  }

  footer .footer-content {
    grid-template-columns: 1fr;
    /* Stack footer columns */
    text-align: center;
  }

  footer .footer-info {
    max-width: none;
    /* Remove max-width */
    margin-bottom: 40px;
  }

  footer .footer-logo {
    margin-left: auto;
    margin-right: auto;
  }

  footer .footer-social {
    justify-content: center;
    /* Center social icons */
  }

  footer .footer-links {
    margin-bottom: 30px;
  }

  footer .footer-links ul {
    padding-left: 0;
    /* Remove default padding */
  }

  /* END OF REMOVALS FROM 992px media query */
}

@media (max-width: 768px) {
  .container {
    padding: 0 20px;
    /* Further reduce padding for mobile */
  }

  header .nav-buttons .btn {
    padding: 8px 16px;
    /* Smaller buttons */
    font-size: 14px;
  }

  .hero {
    padding-top: 68px;
    min-height: 80vh;
  }

  .hero .hero-image {
    display: none;
    /* Hide hero image on mobile */
  }

  .hero .hero-title {
    font-size: 32px;
  }

  .hero .hero-subtitle {
    font-size: 16px;
  }

  .hero .hero-buttons {
    flex-direction: column;
    align-items: center;
    /* Center buttons horizontally when stacked */
    gap: 15px;
    /* Add gap like CTA buttons */
  }

  .hero .hero-buttons .btn {
    padding: 10px 50px;
    font-size: 15px;
    width: 80%;
    /* Match CTA button width */
    max-width: 250px;
    /* Match CTA button width */
  }

  .features .feature-card,
  .core-sections .core-card {
    padding: 30px 20px;
  }

  .features .feature-title,
  .core-sections .core-title {
    font-size: 20px;
  }

  .features .feature-desc,
  .core-sections .core-desc,
  .register .register-desc {
    font-size: 15px;
  }

  .register .register-title {
    font-size: 30px;
  }

  .register .register-point p {
    font-size: 16px;
  }

  .partners .partners-logo {
    gap: 30px;
  }

  .partners .partner-item {
    max-width: 100px;
    /* Smaller partner logos */
  }

  .cta .cta-title {
    font-size: 28px;
  }

  .cta .cta-subtitle {
    font-size: 15px;
  }

  .cta .cta-buttons {
    flex-direction: column;
    gap: 15px;
    align-items: center;
  }

  .cta .cta-buttons .btn {
    width: 80%;
    max-width: 250px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
    /* Reduce side padding on very small screens */
  }

  header .logo {
    width: 130px;
  }

  header .nav-buttons .btn:first-child {
    display: none;
    /* Hide Login on very small screens maybe? */
  }

  .hero .hero-title {
    font-size: 28px;
  }

  .hero .hero-subtitle {
    font-size: 14px;
  }

  .section-title h2 {
    font-size: 26px;
  }

  .section-title p {
    font-size: 14px;
  }

  footer .footer-logo {
    font-size: 20px;
  }

  footer .footer-desc,
  footer .footer-links a {
    font-size: 14px;
  }

  footer .copyright p {
    font-size: 12px;
  }
}

/* Mobile Nav Toggle Button */
.mobile-nav-toggle {
  display: none;
  /* Hidden by default, shown in media query */
  background: none;
  border: none;
  cursor: pointer;
  padding: 10px;
  z-index: 1001;
  /* Above mobile menu */
  position: relative;
  /* Needed for z-index */
}

.hamburger-icon {
  display: block;
  width: 24px;
  height: 2px;
  background-color: var(--light);
  position: relative;
  transition: background-color 0.3s ease-in-out;
}

.hamburger-icon::before,
.hamburger-icon::after {
  content: '';
  position: absolute;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--light);
  transition: transform 0.3s ease-in-out, top 0.3s ease-in-out;
}

.hamburger-icon::before {
  top: -7px;
}

.hamburger-icon::after {
  top: 7px;
}

/* Mobile Menu Styles */
.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  /* Changed from right */
  width: 100%;
  height: 100%;
  background-color: rgba(30, 30, 38, 0.98);
  /* Dark background */
  backdrop-filter: blur(10px);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transform: translateX(-100%);
  /* Start off-screen to the left */
  transition: transform 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  /* Smooth slide */
  padding-top: 80px;
  /* Space for potential header overlap */
  box-sizing: border-box;
  /* Include padding in height/width */
  overflow-x: hidden;
}

.mobile-menu.is-open {
  transform: translateX(0);
}

/* Style for the close (X) state of the hamburger when menu is open */
.mobile-menu.is-open~header.scrolled .mobile-nav-toggle .hamburger-icon,
/* Check scrolled state */
.mobile-menu.is-open~header:not(.scrolled) .mobile-nav-toggle .hamburger-icon {
  /* Check non-scrolled state */
  background-color: transparent;
  /* Middle line disappears */
}

.mobile-menu.is-open ~ header.scrolled .mobile-nav-toggle .hamburger-icon::before,
.mobile-menu.is-open ~ header:not(.scrolled) .mobile-nav-toggle .hamburger-icon::before {
  transform: rotate(45deg);
  top: 0;
}

.mobile-menu.is-open ~ header.scrolled .mobile-nav-toggle .hamburger-icon::after,
.mobile-menu.is-open ~ header:not(.scrolled) .mobile-nav-toggle .hamburger-icon::after {
  transform: rotate(-45deg);
  top: 0;
}

.mobile-menu-links {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
  margin-bottom: 40px;
}

.mobile-menu-links a {
  color: var(--light);
  font-size: 22px;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.mobile-menu-links a:hover {
  color: var(--primary);
}

.mobile-menu-buttons {
  display: flex;
  flex-direction: column;
  /* Stack buttons */
  align-items: center;
  gap: 20px;
  width: 80%;
  max-width: 300px;
  /* Limit button width */
}

.mobile-menu-buttons .btn {
  width: 100%;
  text-align: center;
  padding: 12px 24px;
}

/* 插件区 */
.plugin {
  padding: 100px 0;
  background-color: var(--secondary);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .plugin-container {
    display: flex;
    align-items: center;
    gap: 60px;
    margin-top: 60px;
  }

  .plugin-image {
    flex: 1;
    max-width: 50%;
    margin-right: 100px;

    img {
      width: 100%;
      border-radius: 10px;
    }
  }

  .plugin-content {
    flex: 1;
  }

  .plugin-title {
    font-size: 32px;
    color: var(--light);
    margin-bottom: 24px;
  }

  .plugin-desc {
    font-size: 17px;
    color: var(--light-gray);
    line-height: 1.7;
    margin-bottom: 40px;
  }

  .plugin-features {
    margin-bottom: 40px;
  }

  .plugin-feature {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;

    i {
      color: var(--primary);
      font-size: 24px;
      margin-right: 20px;
      min-width: 24px;
    }

    h4 {
      font-size: 20px;
      color: var(--light);
      margin-bottom: 8px;
    }

    p {
      font-size: 16px;
      color: var(--light-gray);
      line-height: 1.5;
    }
  }

  .plugin-buttons {
    display: flex;
    gap: 20px;
  }
}

/* 用户菜单样式 */
.user-menu {
  display: flex;
  align-items: center;
  gap: 20px;
}

.notification-icon {
  position: relative;
  cursor: pointer;

  i {
    font-size: 20px;
    color: var(--light);
    transition: color 0.3s;
  }

  &:hover i {
    color: var(--primary);
  }

  .notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--primary);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }
}

.user-dropdown {
  position: relative;

  .user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: background-color 0.3s;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    .user-name-container {
      display: flex;
      flex-direction: column;

      .username {
        color: var(--light);
        font-weight: 500;
      }

      .company-name {
        color: var(--light-gray);
        line-height: 1.2;
      }
    }

    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      border: 2px solid var(--primary);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    i {
      color: var(--light-gray);
      font-size: 12px;
      transition: transform 0.3s;
    }
  }

  &:hover .user-info i {
    color: var(--primary);
  }

  .dropdown-menu.show ~ .user-info i,
  .user-info:hover i {
    transform: rotate(180deg);
  }

  .dropdown-menu {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background-color: var(--secondary);
    border-radius: 8px;
    min-width: 200px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0;
    transform: translateY(-10px);
    visibility: hidden;
    transition: all 0.3s;
    z-index: 1001;

    &.show {
      opacity: 1;
      transform: translateY(0);
      visibility: visible;
    }

    &::before {
      content: '';
      position: absolute;
      top: -6px;
      right: 20px;
      width: 12px;
      height: 12px;
      background-color: var(--secondary);
      transform: rotate(45deg);
      border-left: 1px solid rgba(255, 255, 255, 0.1);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .dropdown-item {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 12px 16px;
      color: var(--light);
      text-decoration: none;
      transition: background-color 0.3s;

      i {
        width: 16px;
        color: var(--light-gray);
      }

      &:hover {
        background-color: rgba(249, 76, 48, 0.1);

        i {
          color: var(--primary);
        }
      }

      &:first-child {
        border-radius: 8px 8px 0 0;
      }

      &:last-child {
        border-radius: 0 0 8px 8px;
      }
    }

    .dropdown-divider {
      height: 1px;
      background-color: rgba(255, 255, 255, 0.1);
      margin: 6px 0;
    }
  }
}

/* 响应式用户菜单 */
@media (max-width: 992px) {
  .user-menu {
    .user-name-container {
      display: none;
    }

    .user-info i {
      display: none;
    }
  }

  /* 移动端联系方式布局改为垂直排列 */
  footer .footer-links:last-child > div {
    flex-direction: column;
    align-items: center;

    ul {
      width: 100%;
      text-align: center;
    }

    div {
      margin-top: 10px;
      width: 100%;
      text-align: center;
    }
  }
}

@media (max-width: 768px) {
  .notification-icon {
    i {
      font-size: 18px;
    }

    .notification-badge {
      width: 16px;
      height: 16px;
      font-size: 10px;
    }
  }

  .user-dropdown .avatar {
    width: 32px;
    height: 32px;
  }
}

@media (max-width: 480px) {
  .user-menu {
    gap: 12px;
  }
}

/* Responsive styles for plugin section */
@media (max-width: 1200px) {
  .plugin .plugin-title {
    font-size: 28px;
  }
}

@media (max-width: 992px) {
  .plugin {
    padding: 80px 0;
  }

  .plugin .plugin-container {
    flex-direction: column;
    gap: 40px;
  }

  .plugin .plugin-image {
    max-width: 100%;
  }

  .plugin .plugin-content {
    text-align: center;
  }

  .plugin .plugin-feature {
    text-align: left;
  }

  .plugin .plugin-buttons {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .plugin .plugin-title {
    font-size: 26px;
  }

  .plugin .plugin-desc {
    font-size: 16px;
  }

  .plugin .plugin-feature h4 {
    font-size: 18px;
  }

  .plugin .plugin-feature p {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .plugin .plugin-title {
    font-size: 24px;
  }

  .plugin .plugin-buttons {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .plugin .plugin-buttons .btn {
    width: 80%;
    max-width: 250px;
  }
}
</style>
