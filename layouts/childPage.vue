<template>
  <div
    min-h-100vh
    pt-20
    pb-4
    flex
    flex-col
    items-center
    mx-auto
    class="bg-[url('~/assets/images/bg.jpg')] bg-cover"
    overflow-y-auto
  >
    <header
      w-full
      max-w-202
      h-20
      font-size-8
      color-white
      text-center
      relative
      :class="{ 'max-w-362': size == 'large' }"
    >
      <span
        class="i-a-arrow-left-outlined"
        absolute
        left-1
        top-1
        link
        transition-colors
        @click="goBack"
        v-if="showBack"
      ></span>
      <h4>{{ title }}</h4>
    </header>
    <slot />
    <side-widget></side-widget>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
defineProps({
  showBack: {
    type: Boolean,
    default: true,
  },
  size: {
    type: [Number, String],
  },
  title: {
    type: String,
  },
})

const router = useRouter()

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1) // 返回上一页
  } else {
    router.push('/') // 返回首页
  }
}
</script>

<style scoped>
/* 这里可以添加自定义样式 */
</style>
