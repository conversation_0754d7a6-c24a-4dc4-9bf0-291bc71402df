<template>
  <nuxt-layout name="child-page" title="消息中心">
    <div class="w-full max-w-150 px-1">
      <!-- <a-tabs centered v-model:active-key="currentType">
        <a-tab-pane v-for="item in tabList" :key="item.type">
          <template #tab>
            <div>
              <span>{{ item.name }}</span>
              <a-badge absolute right--6 :count="msgStore.msgCount"></a-badge>
            </div>
          </template>
</a-tab-pane>
</a-tabs> -->

      <a-spin :spinning="loading">
        <div class="divide-textBg divide-y bg-primaryBg">
          <div class="text-right py-2 px-6" v-if="page.total">
            <a-button type="primary" size="small" mr-2 @click="() => setReaded()">全部已读</a-button>
            <a-button type="primary" size="small" @click="removeAll">清空已读</a-button>
          </div>
          <div v-for="item in messageList" :key="item.logId" class="flex items-center pl-4 pr-6 py-2 cursor-pointer"
            @click="read(item)">
            <Data :data="convertMessage(item.message)">
              <template v-slot="{ data }">
                <div class="w-2 h-2 rounded-2 bg-primary mr-2" :class="{
                  'opacity-0': item.status == 1,
                }"></div>
                <div>
                  <a-avatar></a-avatar>
                </div>
                <div class="ml-4 flex-1">
                  <div class="text-white">{{ data.title }}</div>
                  <div class="mt-2px text-(12px #AEAEAE) msg">
                    <span v-html="data.message"></span>
                    <!-- <span class="link text-primary" @click="useDetail(item)">查看详情</span> -->
                  </div>
                </div>
              </template>
            </Data>
          </div>
          <div class="text-right py-2 px-6" v-if="page.total">
            <a-pagination size="small" v-model:current="page.current" v-model:page-size="page.size" :total="page.total"
              @change="changePage"></a-pagination>
          </div>

          <div v-if="!page.total" class="text-(center white) py-2">暂无消息</div>
        </div>
      </a-spin>
    </div>
  </nuxt-layout>
</template>

<script setup>
import { getCompanyStatus } from '~/api/company'

const tabList = [
  // {
  //   name: '品牌馆',
  //   type: 'brand',
  // },
  {
    name: '爆买商城',
    type: 'shop',
  },
  // {
  //   name: '系统消息',
  //   type: 'system',
  // },
]

const msgStore = usemessageStore()
const currentType = ref('shop')
const user = computed(() => userStore().user)
const messageList = ref([])
const getMessageList = async () => {
  const [err, res] = await try_http('/mall/p/myNotifyLog/unReadCountList', {
    params: {
      ...pick(page, ['current', 'size']),
      userMobile: user.value.userMobile,
      remindId: user.value.userId,
    },
  })
  if (!err) {
    messageList.value = res.data.records
    page.total = res.data.total
  }
}
const { page, run, loading } = usepage(getMessageList)
onMounted(() => {
  run()
})

const convertMessage = (msg) => {
  try {
    return JSON.parse(msg)
  } catch (e) {
    return {}
  }
}

const changePage = (current, size) => {
  page.current = current
  page.size = size
  run()
}

const bomai = useRuntimeConfig().public.VITE_BOMAI_URL
const companyStoreObj = companyStore()
const { company } = storeToRefs(companyStoreObj)

// 设为已读
const read = async (row) => {
  console.log('%c Line:123 🥛 row', 'color:#5B6910', row)
  if (row.status != 1) {
    setReaded(row.logId)
  }
  if (row.paramContent) {
    try {
      let { infoType, applyType, merchantId } = JSON.parse(row.paramContent)
      infoType = infoType || applyType
      const { status } = await getCompanyStatus(merchantId)
      if (status == 3 || status == 1) return
      if (infoType == 8 && status == 80) {
        window.open(`${bomai}/brand-join/finance?companyId=${merchantId}`, '_blank')
      } else if (infoType == 7 && status == 70) {
        window.open(`${bomai}/brand-join/operation?companyId=${merchantId}`, '_blank')
      } else if (infoType == 6 && status == 60) {
        navigateTo('/enterpriseCenter/commercial?companyId=' + merchantId)
      } else if (infoType == 5 && status == 50) {
        navigateTo('/enterpriseCenter/enterprise?companyId=' + merchantId)
      } else if (infoType == 2 && status == 20) {
        window.open(`${bomai}/brand-join/settle?companyId=${merchantId}&type=edit`, '_blank')
      } else if (infoType == 3 && status == 30) {
        navigateTo('/enterpriseCenter/device-join?companyId=' + merchantId)
      } else if (infoType == 4 && status == 40) {
        window.open(`${bomai}/brand-join/settle?companyId=${merchantId}&type=edit`, '_blank')
      } else if (infoType == 1 && status == 10) {
        navigateTo('/enterpriseCenter/device-join?companyId=' + merchantId)
      }
    } catch (error) { }
  }
}

const setReaded = async (id) => {
  let body = {}
  if (id) {
    body.logIds = [id]
  }
  const [err, res] = await try_http('/mall/p/myNotifyLog/batchRead', {
    method: 'post',
    body,
  })
  if (!err) {
    msgStore.updatemsgCount()
    run()
  }
}

const removeAll = () => {
  Modal.confirm({
    title: '提示',
    content: '确定要删除全部通知吗？',
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      const [err, res] = await try_http('/mall/p/myNotifyLog/batchDelete', {
        method: 'post',
        body: {},
      })
      if (!err) {
        page.current = 1
        run()
      }
    },
  })
}
</script>

<style lang="less" scoped>
.msg {
  :deep(a) {
    color: theme('colors.primary');
  }
}
</style>
