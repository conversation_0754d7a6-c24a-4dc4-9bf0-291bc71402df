<template>
  <NuxtLayout name="child-page" title="职业信息">
    <div w-full max-w-150>
      <a-form ref="formRef" :label-col="{ span: 24 }" :model="formState" :rules="rules">
        <a-form-item label="真实姓名">
          <a-input
            :maxlength="32"
            placeholder="请输入真实姓名"
            v-model:value="formState.realName"
            size="large"
          ></a-input>
        </a-form-item>

        <a-form-item label="身份" name="identity">
          <a-select w-full placeholder="请选择身份" v-model:value="formState.identity" size="large">
            <a-select-option v-for="(item, i) in identites" :key="i" :value="item">
              {{ item }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="公司">
          <!-- <a-input -->
          <!--   :maxlength="50" -->
          <!--   placeholder="请输入公司名称" -->
          <!--   v-model:value="form.company" -->
          <!-- ></a-input> -->
          <Bussiness v-model="formState.company" />
        </a-form-item>

        <a-form-item label="职位">
          <a-input :maxlength="50" laceholder="请输入职位" v-model:value="formState.position" size="large"></a-input>
        </a-form-item>

        <!-- <a-form-item label="工作证照" text-center>
          <a-upload-dragger
            inline-block
            w-full
            h-40
            line-height-30
            v-model:file-list="fileList"
            name="avatar"
            list-type="picture-card"
            :show-upload-list="false"
            action=""
            :before-upload="beforeUpload"
            :customRequest="uploadImage"
          >
            <img v-if="formState.workPermit" :src="formState.workPermit" alt="avatar" w-full h-40 object-contain />
            <div v-else>
              <plus-outlined></plus-outlined>
            </div>
          </a-upload-dragger>
          <div color-subText>上传名片、工牌等相关工作证明文件，限2MB以内的jpg、jpeg、png文件</div>
        </a-form-item> -->

        <a-form-item text-center name="workPermit" label="工作证照">
          <img-upload
            name="promotionalImg"
            :value="formState.workPermit"
            :beforeUpload="beforeUpload"
            @change="(v) => (formState.workPermit = v)"
          />
          <div color-subText>上传名片、工牌等相关工作证明文件，限2MB以内的jpg、jpeg、png文件</div>
        </a-form-item>

        <a-button w-full type="primary" size="large" @click="update">确定</a-button>
      </a-form>
    </div>
  </NuxtLayout>
</template>

<script lang="ts" setup>
import {} from 'vue'
import { cloneDeep } from 'lodash-es'
import { userUpdate } from '~/api/user'
import { uploadFile } from '~/api/common'

const user = userStore()
const fileList = ref([])

const formRef = ref()
const formState = ref(cloneDeep(user.user) || {})
const rules = ref({
  // identity: [{ required: true, message: '请选择身份' }],
})

const identites = ['工程师', '采购', '供应商', '其他']
const beforeUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('您只能上传jpg、jpeg和png格式的图片')
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过2MB')
  }
  return isJpgOrPng && isLt2M
}

const uploadImage = (data) => {
  uploadFile(data.file, 'WorkPermit')
    .then((res) => {
      useRes(res, () => {
        formState.value.workPermit = res.data
      })
    })
    .catch((err) => {})
}

const reset = () => {
  formState.value = cloneDeep(user.user)
}

const { refreshUser } = useUser()
const update = () => {
  formRef.value?.validate().then(() => {
    const data = pick(formState.value, ['realName', 'identity', 'position', 'workPermit', 'company'])
    userUpdate(data).then((res) => {
      useMall(res, () => {
        message.success('个人资料已修改')
        refreshUser(() => {
          reset()
        })
      })
    })
  })
}
</script>

<style scoped></style>
