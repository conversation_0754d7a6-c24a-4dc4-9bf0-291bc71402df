<template>
  <a-modal v-model:open="open" :title="formState.userMobile ? '修改手机号' : '绑定手机号'" bg-primaryBg  destroy-on-close>
    <a-form
      ref="phoneFormRef"
      :rules="phoneRules"
      :model="phoneData"
      :label-col="{span: 4}"
      :wrapper-col="{ span: 24 }"
      autocomplete="off"
    >
      <a-form-item name="phone" label="手机号">
        <a-input v-model:value="phoneData.phone" placeholder="请输入手机号" />
      </a-form-item>

      <a-form-item name="captcha" label="验证码">
        <a-input-group compact>
          <a-input v-model:value="phoneData.captcha" placeholder="请输入验证码" style="width: calc(100% - 120px)" />
          <a-button
            w-30
            type="primary"
            :loading="loginCaptchaLoading"
            :disabled="loginCaptchaDisabled"
            @click="onLoginCaptchaClick"
          >
            {{ loginCaptchaDisabled ? `重新获取(${loginCaptchaTMinus})` : '获取验证码' }}
          </a-button>
        </a-input-group>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button key="back" @click="open = false">取消</a-button>
      <a-button
        key="submit"
        type="primary"
        :loading="setPhoneLoading"
        @click="handleSetPhone(!!formState.userMobile ? 2 : 1)"
      >
        确认
      </a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import {} from 'vue'
import { defineProps, defineExpose } from 'vue'

const open = ref(false)
const formState = ref({})
const isFirstBind = computed(() => !formState.value.userMobile && !formState.value.userMail)


const setPhoneLoading = ref<boolean>(false)
const loginCaptchaLoading = ref<boolean>(false)
const loginCaptchaDisabled = ref<boolean>(false)
const loginCaptchaTMinus = ref<number>(60)
  const phoneData = reactive({
  phone: '',
  captcha: '',
})
const phoneRules: Record<string, Rule[]> = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      pattern: MOBILE_REG.reg,
      message: MOBILE_REG.msg,
      whitespace: true,
      trigger: 'blur',
    },
  ],
  captcha: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
}
const phoneFormRef: any = ref(null)

const openModal = () => {
  open.value = true;
}

const onLoginCaptchaClick = () => {
  phoneFormRef.value.validateFields(['phone']).then(() => {
    loginCaptchaLoading.value = true
    http('/mall/p/user/sendVerifyCode', {
      method: 'put',
      body: {
        eventType: 'UPDATE_PHONE',
        mobile: phoneData.phone,
      },
    })
      .then((res) => {
        useMall(res, () => {
          message.success('验证码已发送')
          loginCaptchaDisabled.value = true
          loginTimer = setInterval(() => {
            loginCaptchaTMinus.value--
            if (loginCaptchaTMinus.value === 0) {
              loginCaptchaDisabled.value = false
              loginCaptchaTMinus.value = 60
              clearInterval(loginTimer)
              loginTimer = null
            }
          }, 1000)
        })
      })
      .finally(() => {
        loginCaptchaLoading.value = false
      })
  })
}

const handleSetPhone = (type) => {
  phoneFormRef.value
    .validate()
    .then(() => {
      const { phone, captcha } = phoneData
      const data = {
        mobile: phone,
        verifyCode: captcha,
      }
      setPhoneLoading.value = true
      http('/mall/p/user/updatePhone', {
        method: 'put',
        body: data,
      })
        .then((res) => {
          useMall(res, () => {
            message.success('操作成功')
            showPhone.value = false
            refreshUser()
            // if (loginType.value != 'wechat' && type == 2) {
            //   // 非微信扫码 修改手机号 重新登录
            //   clearUser()
            //   window.location.href = '/login'
            // } else {
            //   if (isFirstBind.value && type == 1) {
            //     showSetPwd()
            //   }
            //   refreshUser()
            // }
          })
        })
        .finally(() => {
          setPhoneLoading.value = false
        })
    })
    .catch((error: any) => {
      console.log('error', error)
    })
}

defineExpose({ openModal });

</script>

<style scoped></style>
