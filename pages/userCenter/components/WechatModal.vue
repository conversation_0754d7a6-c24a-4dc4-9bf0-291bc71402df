<template>
  <a-modal v-model:open="open" title="绑定微信" :footer="null">
    <div class="mx-auto overflow-hidden h-300px">
      <div text-center id="wx-login" class="h-full w-full"></div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import {} from 'vue'

import { userStore } from '~/store/user'

const open = ref(false)
const userStoreObj = userStore()

const { user } = storeToRefs(userStoreObj)

const openModal = async () => {
  open.value = true
  await nextTick()
  init()
  // @ts-ignore
  // new WxLogin({
  //   self_redirect: true,
  //   id: 'wx_container',
  //   appid: 'wx14817ec4ef183e7b',
  //   scope: 'snsapi_login',
  //   redirect_uri: encodeURIComponent(`https://www.yidao.cloud/mall/wx/callback?extra=${extra}`),
  //   state: Math.ceil(Math.random() * 1000),
  //   style: '',
  //   href: 'data:text/css;base64,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',
  // })
}

const init = () => {
  const userId: any = user.value.userId
  const reg = new RegExp('\\+', 'ig')
  const extra = userId ? userId.replace(reg, '%2B') : ''

  wxutils('wx-login', 'bind', {
    self_redirect: true,
    redirect_uri: encodeURIComponent(`https://www.yanxuan.cloud/mall/wx/callback?extra=${extra}`),
  })
}

const handleUnBindWechat = () => {
  Modal.confirm({
    title: '提示',
    content: '确认解除绑定吗?',
    onOk: () => {
      http('/mall/p/user/unbindWechat', {
        method: 'put',
      }).then((res) => {
        useMall(res, () => {
          message.success('操作成功')
          // refreshUser()
        })
      })
    },
  })
}

const receiveMessage = (event) => {
  if (event.data === 'closeDialog') {
    message.success('绑定成功')
    open.value = false
    // closeModal()
  }
  if (event.data === 'reScan') {
    // handleBindWechat()
    init()
  }
}

useEventListener('message', receiveMessage, false)

defineExpose({ openModal })
</script>

<style scoped></style>
