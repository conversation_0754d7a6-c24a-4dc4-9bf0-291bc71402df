<template>
  <a-modal v-model:open="open" :title="formState.userMail ? '修改邮箱' : '绑定邮箱'" @ok="handleEmailOk" destroy-on-close>
    <a-form :model="emailForm" :rules="emailRule" ref="emailRef" :label-col="{ span: 4 }" :wrapper-col="{ span: 24 }">
      <a-form-item name="email" label="邮箱">
        <a-input v-model:value="emailForm.email" placeholder="请输入邮箱"></a-input>
      </a-form-item>
      <a-form-item name="emailCode" label="验证码">
        <a-input-group compact>
          <a-input v-model:value="emailForm.emailCode" placeholder="验证码" style="width: calc(100% - 120px)" />
          <a-button w-30 type="primary" :loading="mailLoading" :disabled="mailDisabled" @click="onMailSend">
            {{ mailDisabled ? `重新获取(${mailTime})` : '获取验证码' }}
          </a-button>
        </a-input-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { bindEmail } from '~/api/user'
const { refreshUser } = useUser()
const open = ref(false)
const formState = ref({})
const mailLoading = ref(false)
const mailDisabled = ref(false)
const mailTime = ref(60)
const isFirstBind = computed(() =>!formState.value.userMobile && !formState.value.userMail)

const emailRule = ref({
  email: [
    { required: true, message: '请输入邮箱' },
    {
      pattern: /([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})/,
      message: '邮箱格式不正确',
    },
  ],
  emailCode: [{ required: true, message: '请输入验证码' }],
})

const emailRef = ref()
const emailForm = ref({
  email: '',
  emailCode: '',
})
const emailState = ref('')
const openModal = (state) => {
  emailState.value = state
  emailForm.value.email = ''
  emailForm.value.emailCode = ''
  open.value = true
}

const handleEmailOk = () => {
  emailRef.value?.validate().then(async () => {
    const res = await bindEmail({
      userMail: emailForm.value.email,
      verifyCode: emailForm.value.emailCode,
    })
    useMall(res, () => {
      open.value = false
      message.success(emailState.value == 'bind' ? '绑定成功' : '修改成功')
      if (isFirstBind.value && emailState.value == 'bind') {
        showSetPwd()
      }
      nextTick(() => {
        refreshUser()
      })
    })
  })
}

const showSetPwd = () => {
  Modal.confirm({
    title: '提示',
    content: '当前账号尚未设置登录密码，是否现在设置',
    okText: '去设置',
    cancelText: '取消',
    onOk() {
      navigateTo({ path: '/userCenter/security' })
    },
  })
}

let mailTimer
const onMailSend = () => {
  emailRef.value.validateFields(['email']).then(() => {
    mailLoading.value = true
    http('/mall/p/user/sendVerifyCode', {
      method: 'put',
      body: {
        eventType: 'UPDATE_EMAIL',
        userMail: emailForm.value.email,
      },
    })
      .then((res) => {
        useMall(res, () => {
          message.success('验证码已发送')
          mailDisabled.value = true
          mailTimer = setInterval(() => {
            mailTime.value--
            if (mailTime.value === 0) {
              mailDisabled.value = false
              mailTime.value = 60
              clearInterval(mailTimer)
              mailTimer = null
            }
          }, 1000)
        })
      })
      .finally(() => {
        mailLoading.value = false
      })
  })
}

defineExpose({ openModal })
</script>

<style scoped></style>
