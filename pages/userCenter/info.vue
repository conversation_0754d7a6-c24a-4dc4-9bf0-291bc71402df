<template>
  <NuxtLayout name="child-page" title="个人信息">
    <div w-full max-w-150>
      <a-form ref="formRef" :rules="rules" :model="formState" :label-col="{ span: 24 }" autocomplete="off">
        <!-- <a-form-item text-center>
          <a-upload
            v-model:file-list="fileList"
            name="avatar"
            list-type="picture-card"
            :show-upload-list="false"
            action=""
            :before-upload="beforeUpload"
            :customRequest="uploadImage"
          >
            <img v-if="formState.pic" :src="formState.pic" alt="avatar" w-full h-full />
            <div v-else>
              <plus-outlined></plus-outlined>
              <div class="ant-upload-text">上传头像</div>
            </div>
          </a-upload>
          <div color-subText>限2MB以内的jpg、jpeg、png文件</div>
        </a-form-item> -->

        <a-form-item text-center name="pic" label="个人头像">
          <img-upload
            type="avatar"
            name="merchantLogo"
            :value="formState.pic"
            :beforeUpload="beforeUpload"
            @change="(v) => (formState.pic = v)"
          />
          <!-- <img v-if="!editable" :src="formState.merchantLogo" alt="logo" /> -->

          <!-- <img v-if="formState.pic" :src="formState.pic" alt="avatar" w-full h-full />
      <div v-else>
        <plus-outlined></plus-outlined>
        <div class="ant-upload-text">企业LOGO</div>
      </div> -->

          <div color-subText>限2MB以内的jpg、jpeg、png文件</div>
        </a-form-item>

        <a-form-item name="nickName" label="用户名">
          <a-input v-model:value="formState.nickName" size="large" :maxlength="32" />
        </a-form-item>

        <a-form-item name="birthday" label="生日">
          <a-date-picker value-format="YYYY-MM-DD" class="w-full" size="large" v-model:value="formState.birthDate" />
        </a-form-item>

        <!-- <a-form-item name="email" label="邮箱">
<a-input v-model:value="formState.email" />
</a-form-item> -->

        <a-form-item name="personalProfile" label="个人简介">
          <a-textarea v-model:value="formState.userMemo" size="large" />
        </a-form-item>

        <a-button w-full type="primary" html-type="submit" size="large" :loading="loading" @click="onConfirm">
          确定
        </a-button>
      </a-form>
    </div>
  </NuxtLayout>
</template>

<script lang="ts" setup>
import type { Rule } from 'ant-design-vue/es/form'
import { uploadFile } from '~/api/common'
import { cloneDeep } from 'lodash-es'
import { userUpdate } from '~/api/user'

const store = userStore()
const user = computed(() => store.user)
const { refreshUser } = useUser()

const formRef = ref(null)
type InfoFormState = Pick<
  User,
  'nickName' | 'userMobile' | 'userMail' | 'pic' | 'birthDate' | 'position' | 'userMemo' | 'unionId' | 'tuName'
>
const fileList = ref([])
const formState = ref({} as InfoFormState)
const loading = ref<boolean>(false)
const avatarChange = ref<boolean>(false)
const rules: Record<string, Rule[]> = {
  nickName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
}

const beforeUpload = (file: File) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('您只能上传jpg、jpeg和png格式的图片')
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过2MB')
  }
  return isJpgOrPng && isLt2M
}

const uploadImage = (data: any) => {
  uploadFile(data.file, 'Avatar')
    .then((res) => {
      if (res.code === 'ok') {
        formState.value.pic = res.data
        avatarChange.value = true
      }
    })
    .catch((err) => {})
}

const onConfirm = () => {
  formRef.value?.validate().then(async () => {
    loading.value = true
    const [err, res] = await try_http('/mall/p/user/setUserInfo', {
      method: 'put',
      body: {
        ...formState.value,
        avatarUrl: formState.value.pic,
      },
    })
    if (!err) {
      useMall(res, () => {
        refreshUser()
        message.success('个人资料已修改')
      })
    }
    loading.value = false
  })
}

onMounted(() => {
  formState.value = cloneDeep(user.value)
})
</script>

<style scoped></style>
