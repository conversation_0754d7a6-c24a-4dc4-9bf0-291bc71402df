<template>
  <NuxtLayout name="child-page" title="收藏夹" size="large">
    <div w-full max-w-360 class="h-[calc(100vh-200px)]" flex flex-col bg-primaryBgTransparent>
      <div class="px-4 pt-4">
        <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
          <a-tab-pane key="product" tab="产品">
            <a-form :label-col="{ flex: '80px' }">
              <div grid grid-cols-4 gap-x-4>
                <a-form-item label="产品名称">
                  <a-input v-model:value="formState.prodName" placeholder="请输入" allowClear></a-input>
                </a-form-item>

                <a-form-item label="品牌">
                  <a-input placeholder="请输入" v-model:value="formState.brandName" allowClear></a-input>
                </a-form-item>

                <a-form-item>
                  <a-space>
                    <a-button type="primary" @click="search">搜索</a-button>
                    <a-button @click="reset">重置</a-button>
                  </a-space>
                </a-form-item>
              </div>
            </a-form>

            <a-table :columns="columns" :data-source="list" size="middle" :scroll="{ x: 1500 }" :pagination="false"
              :loading="loading">
              <template #bodyCell="{ column, value, record }">
                <div v-if="column.dataIndex === 'action'">
                  <a-button type="link" @click="showDetail(record)">查看详情</a-button>
                  <a-button type="link" @click="toggleFav(record)">
                    {{ record.isFav ? '取消收藏' : '收藏' }}
                  </a-button>
                </div>
              </template>
            </a-table>

            <a-pagination my-4 text-right v-model:current="page.current" v-model:page-size="page.size" show-quick-jumper
              :total="page.total" />
          </a-tab-pane>

          <a-tab-pane key="brand" tab="品牌">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div v-for="brand in brandList" :key="brand.id" class="p-4 border border-white/20 rounded-2">
                <div class="flex items-center gap-4">
                  <img :src="useImage(convertImage(brand.shopLogo))" class="w-16 h-16 rounded-lg object-cover" />
                  <div class="flex-1">
                    <div class="text-16px font-medium">{{ brand.shopName }}</div>
                    <div class="text-14px text-gray-300 mt-1">{{ brand.merchantName }}</div>
                  </div>
                </div>
                <div class="flex justify-between mt-4 border-t border-white/10 pt-3">
                  <a-button type="link" size="small" class="flex items-center" @click="goHome(brand)">
                    <template #icon>
                      <HomeOutlined />
                    </template>
                    进入主页
                  </a-button>
                  <a-button type="link" size="small" class="flex items-center" @click="goChat(brand.shopId)">
                    <template #icon>
                      <span class="i-custom-customer" mr-2></span>
                    </template>
                    咨询
                  </a-button>
                  <a-button type="link" size="small" class="flex items-center" @click="toggleFavBrand(brand)">
                    <template #icon>
                      <StarOutlined />
                    </template>
                    {{ brand.isFav ? '取消收藏' : '收藏' }}
                  </a-button>
                </div>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
import { getFavourite, getFavBrandList, setFavBrand, disFavBrand } from '~/api/user'
import { userStore } from '~/store/user'
import { companyStore } from '~/store/company'

const activeTab = ref('product')
const brandList = ref([])

type State = {
  prodName: string
  categoryName: string
  brandName: string
}
const formState = ref({} as State)

const loading = ref(false)
const companyStoreObj = companyStore()
const { company } = storeToRefs(companyStoreObj)

const merchantId = company.value.shopCompanyId as string
const store = userStore()
const user = computed(() => store.user)
const list = ref<Obj[]>([])

const page = reactive({
  current: 1,
  size: 10,
  total: 0,
})

const dataSource = ref<Obj[]>([])

const fetchFavList = async () => {
  loading.value = true
  const res = await getFavourite({
    ...formState.value,
    pageSize: page.size,
    current: page.current,
  })
  if (res.success) {
    list.value = res.data.records.map(item => ({
      ...item,
      isFav: true
    }))
    page.total = res.data.total
  }
  loading.value = false
}

const fetchFavBrandList = async () => {
  const res = await getFavBrandList({
    size: 1000
  })
  if (res.success) {
    brandList.value = res.data.records.map(item => ({
      ...item,
      isFav: true
    }))
  }
}

const handleTabChange = (key: string) => {
  if (key === 'brand') {
    fetchFavBrandList()
  }
}

const reset = () => {
  formState.value = {} as State
  page.current = 1
  fetchFavList()
}

const search = () => {
  page.current = 1
  fetchFavList()
}

onMounted(async () => {
  await fetchFavList()
})

const columns = ref([
  {
    title: '产品名称',
    dataIndex: 'prodName',
  },
  {
    title: '产品型号',
    dataIndex: 'model',
  },
  {
    title: '类目',
    dataIndex: 'categoryName',
  },
  {
    title: '品牌',
    dataIndex: 'brandName',
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: '200px',
  },
] as any)

const toggleFav = async (record: Obj) => {
  if (record.isFav) {
    const res = await http('/mall/p/my-favorites', {
      method: 'delete',
      query: {
        userId: user.value.userId,
        id: record.id,
      },
    })
    if (res.success) {
      message.success('已取消收藏')
      record.isFav = false
    }
  } else {
    const res = await http('/mall/p/my-favorites', {
      method: 'post',
      body: {
        userId: user.value.userId,
        prodName: record.prodName,
        categoryName: record.categoryName,
        brandName: record.brandName,
        model: record.model,
        partId: record.partId,
        folderId: 0,
      },
    })
    if (res.success) {
      message.success('收藏成功')
      record.isFav = true
    }
  }
}

const showDetail = (record: Obj) => {
  const bomai = useRuntimeConfig().public.VITE_BOMAI_URL
  let url = ``
  if (!!record.shopId) {
    url = `${bomai}/brand/${record.shopId}/${record.partId}`
  } else {
    url = `${bomai}/parts/${record.partId}`
  }
  if (record.model) {
    url = url + `?typeCode=${record.model}`
  }
  window.open(url, '__target')
}

const goChat = (shop_id: string | number) => {
  navigateTo({
    path: '/chat',
    query: {
      shop_id: shop_id,
    },
  })
}

const toggleFavBrand = async (record: Obj) => {
  if (record.isFav) {
    const res = await disFavBrand(record.shopId)
    if (res.success) {
      message.success('已取消收藏')
      record.isFav = false
    }
  } else {
    const res = await setFavBrand(record.shopId)
    if (res.success) {
      message.success('收藏成功')
      record.isFav = true
    }
  }
}

const goHome = (record: Obj) => {
  const bomai = useRuntimeConfig().public.VITE_BOMAI_URL
  const url = `${bomai}/brand/${record.shopId}`
  window.open(url, '__target')
}

</script>
