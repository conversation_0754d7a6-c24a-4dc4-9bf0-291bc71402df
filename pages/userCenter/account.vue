<template>
  <NuxtLayout name="child-page" title="账号绑定">
    <div w-full max-w-150>
      <div
        v-for="(item, index) in groups"
        :key="index"
        h-12.5
        flex
        items-center
        justify-between
        px-3
        bg-primaryBg
        text-primaryText
        :class="{ 'border-b border-b-[#353541]': index < groups.length - 1 }"
      >
        <div w-full flex items-center justify-between>
          <div flex items-center>
            <span font-size-20px :class="item.icon" mr-2></span>
            <span>{{ item.name }}</span>
            <span v-if="item.value" color-gray ml-3>{{ item.value }}</span>
          </div>
          <a-button v-if="item.buttonText" type="link" link @click="item.clickEvent">{{ item.buttonText }}</a-button>
        </div>
      </div>

      <phone-modal ref="phoneModalRef" />
      <email-modal ref="emailModalRef" />
      <wechat-modal ref="wechatModalRef" />
    </div>
  </NuxtLayout>
</template>

<script lang="ts" setup>
import {} from 'vue'
import PhoneModal from './components/PhoneModal.vue'
import EmailModal from './components/EmailModal.vue'
import WechatModal from './components/WechatModal.vue'
const { user } = storeToRefs(userStore())
type menuItem = {
  icon?: string
  name?: string
  value?: string
  buttonText?: string
  clickEvent?: Function
}
const phoneModalRef = ref(null)
const emailModalRef = ref(null)
const wechatModalRef = ref(null)

const onPhone = () => {
  // 暂时隐藏手机号修改
  if (user.value.userMobile) return
  if (phoneModalRef.value) {
    phoneModalRef.value.openModal()
  }
}

const onEmail = () => {
  if (emailModalRef.value) {
    emailModalRef.value.openModal()
  }
}

const onWechat = () => {
  if (wechatModalRef.value) {
    wechatModalRef.value.openModal()
  }
}

const groups: Array<menuItem> = ref([
  {
    icon: 'i-a-phone-outlined',
    name: '手机号',
    value: user.value.userMobile,
    buttonText: !!user.value.userMobile ? '' : '绑定',
    // buttonText: !!user.value.userMobile ? '修改' : '绑定',
    clickEvent: onPhone,
  },
  {
    icon: 'i-a-mail-outlined',
    name: '邮箱',
    value: user.value.userMail,
    buttonText: !!user.value.userMail ? '修改' : '绑定',
    clickEvent: onEmail,
  },
  {
    icon: 'i-a-wechat-outlined',
    name: '微信',
    value: user.value.unionId,
    buttonText: !!user.value.unionId ? '解绑' : '绑定',
    clickEvent: onWechat,
  },
])
</script>

<style scoped></style>
