<template>
  <div class="w-full h-full flex-center overflow-hidden">
    <left-logo v-if="!isIframe"></left-logo>
    <div class="bg-#2A2A35 w-full max-w-566px p-3">
      <Back class="text-7 invisible" :class="{ '!visible': !isIframe }" />
      <div class="text-center">
        <img h-15 src="~/assets/images/logo_rect_white.png" />
      </div>

      <div class="mt-10 px-10">
        <div v-if="step == 1" class="flex items-center mb-3">
          <span font-size-3.5 color-white>重置密码</span>
        </div>
        <!-- 步骤1: 验证手机号 -->
        <div>
          <a-form
            ref="phoneFormRef"
            :rules="phoneRules"
            :model="phoneForm"
            @keyup.enter="resetPassword"
            v-if="step == 1"
          >
            <a-form-item name="mobile">
              <a-input placeholder="请输入手机号" v-model:value="phoneForm.mobile"></a-input>
            </a-form-item>
            <a-form-item name="smsCode">
              <msg-input :api="sendVerifyCode" msg-key="forgot-pw" v-model:value="phoneForm.smsCode"></msg-input>
            </a-form-item>
            <a-form-item name="password">
              <a-input
                type="password"
                placeholder="请输入新密码"
                v-model:value="phoneForm.password"
                autocomplete="new-password"
              ></a-input>
            </a-form-item>
            <a-form-item name="confirmPassword">
              <a-input
                type="password"
                placeholder="请确认新密码"
                v-model:value="phoneForm.confirmPassword"
                autocomplete="new-password"
              ></a-input>
            </a-form-item>
            <a-button type="primary" :loading="loading" w-full @click="resetPassword">重置密码</a-button>

            <!-- <a-button type="primary" mt-5 :loading="loading" w-full @click="verifyPhone">下一步</a-button> -->
          </a-form>
        </div>

        <!-- 步骤2: 设置新密码 -->
        <!-- <div v-if="step === 2"> -->
        <!--   <a-form -->
        <!--     ref="passwordFormRef" -->
        <!--     :rules="passwordRules" -->
        <!--     :model="passwordForm" -->
        <!--     @keyup.enter="resetPassword" -->
        <!--   ></a-form> -->
        <!-- </div> -->

        <!-- 步骤3: 完成 -->
        <div v-if="step === 3" class="text-center">
          <div class="text-green-500 text-60px">
            <span class="i-a-check-circle-outlined"></span>
          </div>
          <div class="text-white text-18px mb-8">密码重置成功！</div>
          <a-button class="mb-8" type="primary" w-full @click="goToLogin">返回登录</a-button>
        </div>
      </div>

      <div v-if="step === 1" class="text-(center 14px white) mt-3 mb-5">
        <span>想起密码了？</span>
        <nuxt-link class="text-primary!" to="/login" v-if="!isIframe">返回登录</nuxt-link>
        <span class="text-primary cursor-pointer" v-else @click="goToLogin">返回登录</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance } from 'ant-design-vue'

const phoneFormRef = useTemplateRef<FormInstance>('phoneFormRef')
const passwordFormRef = useTemplateRef<FormInstance>('passwordFormRef')

const step = ref(1) // 1: 验证手机号, 2: 设置密码, 3: 完成
const loading = ref(false)

// 手机验证表单
const phoneForm = ref({
  mobile: '',
  smsCode: '',
  password: '',
  confirmPassword: '',
})

// 密码设置表单
// const passwordForm = ref({
//   password: '',
//   confirmPassword: '',
// })

const phoneRules = {
  mobile: [
    { required: true, message: '请输入手机号' },
    { pattern: MOBILE_REG.reg, message: MOBILE_REG.msg },
  ],
  smsCode: [{ required: true, message: '请输入验证码' }],
  password: [
    { required: true, message: '请输入新密码' },
    { min: 6, message: '密码长度不能少于6位' },
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码' },
    {
      validator: (_rule, value) => {
        if (value && value !== phoneForm.value.password) {
          return Promise.reject(new Error('两次输入的密码不一致'))
        }
        return Promise.resolve()
      },
    },
  ],
}

const passwordRules = {}

// 发送验证码
const sendVerifyCode = async () => {
  await phoneFormRef.value?.validate('mobile')
  await http('/mall/user/sendVerifyCode', {
    method: 'put',
    body: {
      eventType: 'RESET_PASSWORD',
      mobile: phoneForm.value.mobile,
    },
  })
}

// 验证手机号
const verifyPhone = async () => {
  // await phoneFormRef.value?.validate()
  // loading.value = true
  //
  // const [err, res] = await try_http('/mall/user/verifyResetCode', {
  //   method: 'post',
  //   body: {
  //     mobile: phoneForm.value.mobile,
  //     smsCode: phoneForm.value.smsCode,
  //   },
  // })
  //
  // loading.value = false
  //
  // if (!err) {
  //   step.value = 2
  // }
  // step.value = 2
}

// 重置密码
const resetPassword = async () => {
  await phoneFormRef.value?.validate()
  loading.value = true

  const [err, res] = await try_http('/mall/user/resetPwd', {
    method: 'put',
    body: {
      username: phoneForm.value.mobile,
      verifyCode: phoneForm.value.smsCode,
      password: aesEncode(phoneForm.value.password),
    },
  })

  loading.value = false

  if (!err) {
    step.value = 3
  }
}

const route = useRoute()
const isIframe = computed(() => {
  return route.query.isIframe == '1'
})

const router = useRouter()
const goToLogin = () => {
  if (isIframe.value) {
    const resolve = router.resolve('/login')
    window.open(resolve.href)
  } else {
    navigateTo('/login')
  }
}
</script>
