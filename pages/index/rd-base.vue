<template>
  <!-- 英雄区 -->
  <section class="hero">
    <div class="container">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">装备研发标准库系统</h1>
          <p class="hero-subtitle">外购件&自制件的标准化及设计共享系统</p>
          <div class="hero-buttons">
            <button class="btn btn-primary btn-big" @click="handleTrialApplication">申请试用</button>
            <!-- <button class="btn btn-primary btn-big btn-disabled" disabled>即将上线</button> -->
          </div>
        </div>
      </div>
    </div>
    <div class="hero-shapes"></div>
    <!-- 视频背景 -->
    <div class="image-background">
      <img src="~/assets/images/rdbase_bg.png" />
    </div>
  </section>

  <!-- 价值主张区 -->
  <section class="value-proposition" id="value-proposition" data-aos="fade-up">
    <div class="container">
      <div class="section-title">
        <h2>为什么选择装备研发标准库系统？</h2>
        <p>利用标准库系统，实现设计共享及物料的归一化，从而实现供应链优化降本</p>
      </div>
      <div class="value-grid">
        <div class="value-card" data-aos="zoom-in" data-aos-delay="100">
          <div class="card-icon">
            <i class="fas i-fa-cogs"></i>
          </div>
          <h3>选型规范化</h3>
          <p>从源头规范研发工程师选型，避免错误选型和重复开发，建立企业级标准化体系，确保选型的一致性和可靠性。</p>
          <!-- <div class="value-stats">
            <span class="stat-number">30%</span>
            <span class="stat-label">减少设计变更</span>
          </div> -->
        </div>
        <div class="value-card" data-aos="zoom-in" data-aos-delay="200">
          <div class="card-icon">
            <i class="fas i-fa-shopping-cart"></i>
          </div>
          <h3>采购集中化</h3>
          <p>整合采购需求，实现统谈集采，通过标准化采购流程和供应商集中管理，大幅降低采购成本和管理复杂度。</p>
          <!-- <div class="value-stats">
            <span class="stat-number">20%</span>
            <span class="stat-label">降低采购成本</span>
          </div> -->
        </div>
        <div class="value-card" data-aos="zoom-in" data-aos-delay="300">
          <div class="card-icon">
            <i class="fas i-fa-chart-line"></i>
          </div>
          <h3>设计共享化</h3>
          <p>
            通过对外购件的品牌、型号规格及自制件的标准化管理，沉淀设计资产，提高设计的复用度，提高项目开发效率，降低后端供应链的杂乱无序。
          </p>
          <!-- <div class="value-stats">
            <span class="stat-number">50%</span>
            <span class="stat-label">提升工作效率</span>
          </div> -->
        </div>
      </div>
    </div>
  </section>

  <!-- 核心功能展示区 -->
  <section class="core-features" id="core-features" data-aos="fade-up">
    <div class="container">
      <div class="section-title">
        <h2>装备研发标准库功能展示</h2>
        <p>中大型装备制造商标准化必备数字化工具</p>
      </div>

      <div class="features-tabs">
        <div class="tab-nav">
          <button
            v-for="(feature, index) in features"
            :key="index"
            :class="['tab-btn', { active: activeTab === index }]"
            @click="setActiveTab(index)"
          >
            <i :class="feature.icon"></i>
            <span>{{ feature.name }}</span>
          </button>
        </div>

        <div class="tab-content">
          <div class="feature-display">
            <div class="feature-info">
              <h3>{{ features[activeTab].name }}</h3>
              <p class="feature-desc">{{ features[activeTab].description }}</p>
              <ul class="feature-highlights">
                <li v-for="highlight in features[activeTab].highlights" :key="highlight">
                  <i class="fas i-fa-check-circle"></i>
                  {{ highlight }}
                </li>
              </ul>
            </div>
            <div class="feature-visual">
              <div class="feature-icon-large">
                <i :class="features[activeTab].icon"></i>
              </div>
              <div class="feature-badge">
                {{ features[activeTab].badge }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 系统优势区 -->
  <section class="system-advantages" id="advantages" data-aos="fade-up">
    <div class="container">
      <div class="section-title">
        <h2>装备研发标准库优势</h2>
      </div>
      <div class="advantages-grid">
        <div class="advantage-item" data-aos="fade-up" data-aos-delay="100">
          <div class="advantage-number">01</div>
          <div class="advantage-content">
            <h3>一站式解决</h3>
            <p>找图纸、选模型、下采购，一个网站全搞定。可从研选工场平台同步下载CAD模型和技术文档。</p>
          </div>
        </div>
        <div class="advantage-item" data-aos="fade-up" data-aos-delay="200">
          <div class="advantage-number">02</div>
          <div class="advantage-content">
            <h3>物料标准化</h3>
            <p>从外购标准件到企业自制件，统一平台进行管理，物料编码规则标准化，杜绝一物多码现象。</p>
          </div>
        </div>
        <div class="advantage-item" data-aos="fade-up" data-aos-delay="300">
          <div class="advantage-number">03</div>
          <div class="advantage-content">
            <h3>工具集成</h3>
            <p>常用CAD插件，让工程师随时查询已导入的行业标准件和企业标准件。</p>
          </div>
        </div>
        <div class="advantage-item" data-aos="fade-up" data-aos-delay="400">
          <div class="advantage-number">04</div>
          <div class="advantage-content">
            <h3>模块复用</h3>
            <p>沉淀企业知识资产，实现设计模块的标准化管理和快速复用，提升研发效率。</p>
          </div>
        </div>
        <div class="advantage-item" data-aos="fade-up" data-aos-delay="500">
          <div class="advantage-number">05</div>
          <div class="advantage-content">
            <h3>成本透明化</h3>
            <p>通过连接研选工场零部件交易平台，实时获取最新的交易信息。</p>
          </div>
        </div>
        <div class="advantage-item" data-aos="fade-up" data-aos-delay="600">
          <div class="advantage-number">06</div>
          <div class="advantage-content">
            <h3>库存管理</h3>
            <p>可对接ERP或WMS，让工程师实时掌握库存信息。</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 社会证明与案例区 -->
  <!-- <section class="social-proof" id="social-proof" data-aos="fade-up">
    <div class="container">
      <div class="section-title">
        <h2>自动化装备上市公司都在用的标准库系统</h2>
      </div>
      <div class="testimonials">
        <div class="testimonial-card" data-aos="zoom-in" data-aos-delay="100">
          <div class="testimonial-content">
            <p>"通过装备研发标准库系统，我们实现了选型标准化，年节省采购成本超过500万元，设计变更减少了40%。"</p>
          </div>
          <div class="testimonial-author">
            <div class="author-info">
              <h4>某Top 10装备制造商</h4>
              <span>技术总监</span>
            </div>
          </div>
        </div>
        <div class="testimonial-card" data-aos="zoom-in" data-aos-delay="200">
          <div class="testimonial-content">
            <p>"标准库系统帮助我们建立了完整的供应商体系，采购效率提升了60%，库存周转率显著改善。"</p>
          </div>
          <div class="testimonial-author">
            <div class="author-info">
              <h4>知名自动化企业</h4>
              <span>采购经理</span>
            </div>
          </div>
        </div>
        <div class="testimonial-card" data-aos="zoom-in" data-aos-delay="300">
          <div class="testimonial-content">
            <p>"系统的模块复用功能让我们的研发效率大幅提升，新产品开发周期缩短了30%。"</p>
          </div>
          <div class="testimonial-author">
            <div class="author-info">
              <h4>上市装备企业</h4>
              <span>研发部长</span>
            </div>
          </div>
        </div>
      </div>

      <div class="stats-section">
        <div class="stat-item" data-aos="fade-up" data-aos-delay="100">
          <div class="stat-number">10万+</div>
          <div class="stat-label">优选行业标准件SKU</div>
        </div>
        <div class="stat-item" data-aos="fade-up" data-aos-delay="200">
          <div class="stat-number">500+</div>
          <div class="stat-label">合作企业客户</div>
        </div>
        <div class="stat-item" data-aos="fade-up" data-aos-delay="300">
          <div class="stat-number">20%</div>
          <div class="stat-label">平均降低采购成本</div>
        </div>
        <div class="stat-item" data-aos="fade-up" data-aos-delay="400">
          <div class="stat-number">50%</div>
          <div class="stat-label">提升研发效率</div>
        </div>
      </div>
    </div>
  </section> -->

  <!-- 行动号召区 -->
  <section class="cta" id="contact">
    <div class="cta-content" data-aos="zoom-in">
      <div class="container">
        <h2 class="cta-title">立即体验装备研发标准库系统，开启降本增效之旅</h2>
        <p class="cta-subtitle">从源头帮助装备制造商降本提效，让您的企业在激烈的市场竞争中脱颖而出</p>

        <div class="cta-buttons">
          <button class="btn btn-outline-light btn-big" @click="handleTrialApplication">申请试用</button>
          <!-- <button class="btn btn-outline-light btn-big btn-disabled" disabled>即将上线</button> -->
        </div>
      </div>
    </div>
  </section>

  <!-- 申请试用表单 -->
  <ApplyTrialForm ref="applyRef" />
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue'
import ApplyTrialForm from '~/components/ApplyTrialForm/index.vue'

definePageMeta({
  layout: 'page',
})

// 显示/隐藏申请表单
const showTrialForm = ref(false)

// 功能模块数据
const features = ref([
  // {
  //   name: '品类库',
  //   icon: 'fas i-fa-folder-open',
  //   description: '管理公司会使用到的所有标准件类目和料号',
  //   highlights: ['优选行业标准件超10万个SKU', '完整的分类体系管理', '快速检索和筛选功能', '实时更新行业标准'],
  //   badge: '10万+ SKU',
  // },
  // {
  //   name: '品牌库',
  //   icon: 'fas i-fa-award',
  //   description: '建设零部件供应商品牌库，优化供应商选择',
  //   highlights: ['权威品牌认证体系', '供应商评级管理', '品牌对比分析', '质量追溯体系'],
  //   badge: '认证品牌',
  // },
  {
    name: '外购标准件管理',
    icon: 'fas i-fa-barcode',
    description: '常用品牌、外购的行业标准件管理，提高标准件复用率，提升设计效率，实现供应链统谈集采。',
    highlights: [
      '常用外购品牌管理，对品牌的导入严格控制',
      '行业品类及产品名称，统一研发采购术语体系，减少一物多码',
      '产品系列（SPU）及产品型号（SKU）管理，实现料码的归一化，提高零部件复用率',
      '优选件管理（品牌优选、型号优选）进一步提升零部件复用率',
      '连接研选工场供应链系统，实时获取标准件交易价格和交期',
    ],
    badge: '外购件管理',
  },
  // {
  //   name: '供应商库',
  //   icon: 'fas i-fa-handshake',
  //   description: '分层分级管理供应商资源，优化供应链',
  //   highlights: ['供应商分级评估', '准入认证流程', '绩效监控管理', '风险预警机制'],
  //   badge: '分级管理',
  // },
  {
    name: '企业模块管理',
    icon: 'fas i-fa-cubes',
    description: '企业标准模块管理，实现设计资产沉淀及复用',
    highlights: ['模块标准化定义', '设计资产沉淀', '快速复用机制', '版本控制管理'],
    badge: '资产复用',
  },
  {
    name: '企业案例库',
    icon: 'fas i-fa-lightbulb',
    description: '解决方案管理，积累项目经验和最佳实践',
    highlights: ['成功案例展示', '解决方案模板', '经验知识沉淀', '最佳实践分享'],
    badge: '经验沉淀',
  },
])

const activeTab = ref(0)
let autoRotateInterval = null

// 自动轮播功能
const startAutoRotate = () => {
  autoRotateInterval = setInterval(() => {
    activeTab.value = (activeTab.value + 1) % features.value.length
  }, 10000) // 10秒切换一次
}

// 手动点击时重置计时器
const setActiveTab = (index) => {
  activeTab.value = index
  if (autoRotateInterval) {
    clearInterval(autoRotateInterval)
    startAutoRotate()
  }
}

// 组件挂载时启动自动轮播
onMounted(() => {
  startAutoRotate()
})

// 组件卸载时清除计时器
onUnmounted(() => {
  if (autoRotateInterval) {
    clearInterval(autoRotateInterval)
  }
})

const isLogin = useLoginState()
const applyRef = ref()
const company = companyStore()
const handleTrialApplication = () => {
  if (!isLogin.value) {
    message.info('您尚未登录，请登录后再操作')
    // TODO: Redirect to login page
    return navigateTo({
      path: '/login',
      query: {
        redirect: '/rd-base',
      },
    })
  }
  const { merchantName, creditCode } = company.company
  applyRef.value.open(merchantName, creditCode).then(() => {
    console.log('apply')
  })
}
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;
  padding: 0 80px;
}

/* 英雄区 */
.hero {
  background: linear-gradient(135deg, var(--secondary) 0%, var(--dark) 100%);
  color: var(--light);
  padding-top: 88px;
  height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;

  .hero-content {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 5;
  }

  .hero-text {
    flex: 1;
    text-align: center;
  }

  .hero-title {
    font-size: 52px;
    font-weight: bold;
    margin-bottom: 24px;
    line-height: 1.3;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    animation: fadeInDown 1s ease-out;
  }

  .hero-subtitle {
    font-size: 24px;
    margin-bottom: 40px;
    line-height: 1.6;
    color: var(--light-gray);
    animation: fadeInUp 1s 0.3s ease-out backwards;
  }

  .hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    animation: fadeInUp 1s 0.6s ease-out backwards;
  }

  .hero-shapes {
    position: absolute;
    top: -100px;
    left: -100px;
    height: 500px;
    background: radial-gradient(circle, rgba(249, 76, 48, 0.1) 0%, rgba(249, 76, 48, 0) 70%);
    border-radius: 50%;
    z-index: 1;

    &::after {
      content: '';
      position: absolute;
      bottom: -150px;
      right: -150px;
      height: 600px;
      background: radial-gradient(circle, rgba(42, 42, 53, 0.1) 0%, rgba(42, 42, 53, 0) 70%);
      border-radius: 50%;
    }
  }

  .image-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(30, 30, 38, 0.7);
      z-index: 1;
    }

    img {
      position: absolute;
      min-width: 100%;
      min-height: 100%;
      width: auto;
      height: auto;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      object-fit: cover;
    }
  }
}

/* 通用按钮样式 */
.btn {
  padding: 12px 32px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 2px solid transparent;
  font-size: 16px;

  &.btn-big {
    padding: 16px 40px;
    font-size: 18px;
  }

  &.btn-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }
}

.btn-primary {
  background: linear-gradient(45deg, var(--primary), #e03a20);
  color: var(--light);
  border-color: var(--primary);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(249, 76, 48, 0.4);
  }
}

.btn-outline-light {
  background-color: transparent;
  color: var(--light);
  border-color: var(--light);

  &:hover {
    background-color: var(--light);
    color: var(--primary);
    transform: translateY(-2px);
  }
}

.btn-light {
  background-color: var(--light);
  color: var(--primary);
  border-color: var(--light);

  &:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
  }
}

/* Section通用样式 */
.section-title {
  text-align: center;
  margin-bottom: 60px;

  h2 {
    font-size: 36px;
    color: var(--light);
    margin-bottom: 16px;
    font-weight: 700;
  }

  p {
    font-size: 18px;
    color: var(--light-gray);
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.6;
  }
}

/* 价值主张区 */
.value-proposition {
  padding: 100px 0;
  background-color: var(--dark);
  border-top: 1px solid rgba(255, 255, 255, 0.1);

  .value-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
  }

  .value-card {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    padding: 40px 30px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(249, 76, 48, 0.1), transparent);
      opacity: 0;
      transition: opacity 0.4s ease;
    }

    &:hover {
      transform: translateY(-10px);
      border-color: var(--primary);
      box-shadow: 0 20px 40px rgba(249, 76, 48, 0.2);

      &::before {
        opacity: 1;
      }

      .card-icon {
        transform: scale(1.1);
        background: linear-gradient(45deg, var(--primary), #ffca46);
      }
    }

    .card-icon {
      width: 80px;
      height: 80px;
      border-radius: 20px;
      background: linear-gradient(45deg, var(--primary), #e03a20);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 24px;
      transition: all 0.3s ease;

      i {
        font-size: 32px;
        color: var(--light);
      }
    }

    h3 {
      font-size: 24px;
      color: var(--light);
      margin-bottom: 16px;
      font-weight: 600;
    }

    p {
      font-size: 16px;
      color: var(--light-gray);
      line-height: 1.6;
      margin-bottom: 24px;
    }

    .value-stats {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .stat-number {
        font-size: 32px;
        font-weight: 700;
        color: var(--primary);
      }

      .stat-label {
        font-size: 14px;
        color: var(--light-gray);
      }
    }
  }
}

/* 核心功能展示区 */
.core-features {
  padding: 100px 0;
  background-color: var(--secondary);
  border-top: 1px solid rgba(255, 255, 255, 0.1);

  .features-tabs {
    .tab-nav {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      margin-bottom: 40px;

      .tab-btn {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 20px 16px;
        color: var(--light-gray);
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;

        i {
          font-size: 24px;
        }

        span {
          font-size: 14px;
          font-weight: 500;
        }

        &:hover {
          border-color: var(--primary);
          color: var(--light);
          transform: translateY(-2px);
        }

        &.active {
          background: linear-gradient(45deg, var(--primary), #e03a20);
          border-color: var(--primary);
          color: var(--light);
          transform: translateY(-4px);
          box-shadow: 0 8px 25px rgba(249, 76, 48, 0.3);
        }
      }
    }

    .tab-content {
      .feature-display {
        background: rgba(255, 255, 255, 0.03);
        border-radius: 16px;
        padding: 50px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        gap: 60px;
        align-items: center;

        .feature-info {
          flex: 2;

          h3 {
            font-size: 32px;
            color: var(--light);
            margin-bottom: 20px;
            font-weight: 700;
          }

          .feature-desc {
            font-size: 18px;
            color: var(--light-gray);
            line-height: 1.6;
            margin-bottom: 30px;
          }

          .feature-highlights {
            list-style: none;
            padding: 0;

            li {
              display: flex;
              align-items: center;
              gap: 12px;
              margin-bottom: 12px;
              font-size: 16px;
              color: var(--light);

              i {
                color: var(--primary);
                font-size: 16px;
              }
            }
          }
        }

        .feature-visual {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative;

          .feature-icon-large {
            width: 120px;
            height: 120px;
            border-radius: 30px;
            background: linear-gradient(45deg, var(--primary), #ffca46);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(249, 76, 48, 0.3);

            i {
              font-size: 48px;
              color: var(--light);
            }
          }

          .feature-badge {
            background: rgba(255, 202, 70, 0.2);
            color: #ffca46;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            border: 1px solid rgba(255, 202, 70, 0.3);
          }
        }
      }
    }
  }

  .auxiliary-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-top: 60px;

    .aux-feature {
      background: rgba(255, 255, 255, 0.03);
      border-radius: 12px;
      padding: 30px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      gap: 20px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        border-color: var(--primary);
        box-shadow: 0 10px 25px rgba(249, 76, 48, 0.15);
      }

      .aux-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        background: linear-gradient(45deg, var(--primary), #e03a20);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        i {
          font-size: 24px;
          color: var(--light);
        }
      }

      .aux-content {
        h4 {
          font-size: 18px;
          color: var(--light);
          margin-bottom: 8px;
          font-weight: 600;
        }

        p {
          font-size: 14px;
          color: var(--light-gray);
          line-height: 1.5;
          margin: 0;
        }
      }
    }
  }
}

/* 系统优势区 */
.system-advantages {
  padding: 100px 0;
  background-color: var(--dark);
  border-top: 1px solid rgba(255, 255, 255, 0.1);

  .advantages-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
  }

  .advantage-item {
    display: flex;
    gap: 30px;
    padding: 40px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(to bottom, var(--primary), #ffca46);
      transform: scaleY(0);
      transition: transform 0.4s ease;
    }

    &:hover {
      transform: translateY(-8px);
      border-color: var(--primary);
      box-shadow: 0 15px 35px rgba(249, 76, 48, 0.2);

      &::before {
        transform: scaleY(1);
      }

      .advantage-number {
        background: linear-gradient(45deg, var(--primary), #ffca46);
        transform: scale(1.1);
      }
    }

    .advantage-number {
      width: 60px;
      height: 60px;
      border-radius: 15px;
      background: linear-gradient(45deg, var(--primary), #e03a20);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: 700;
      color: var(--light);
      flex-shrink: 0;
      transition: all 0.3s ease;
    }

    .advantage-content {
      h3 {
        font-size: 24px;
        color: var(--light);
        margin-bottom: 12px;
        font-weight: 600;
      }

      p {
        font-size: 16px;
        color: var(--light-gray);
        line-height: 1.6;
        margin: 0;
      }
    }
  }
}

/* 社会证明与案例区 */
.social-proof {
  padding: 100px 0;
  background-color: var(--secondary);
  border-top: 1px solid rgba(255, 255, 255, 0.1);

  .testimonials {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 80px;
  }

  .testimonial-card {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 16px;
    padding: 40px 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s ease;
    position: relative;

    &::before {
      content: '"';
      position: absolute;
      top: -10px;
      left: 30px;
      font-size: 60px;
      color: var(--primary);
      font-weight: 700;
      line-height: 1;
    }

    &:hover {
      transform: translateY(-8px);
      border-color: var(--primary);
      box-shadow: 0 20px 40px rgba(249, 76, 48, 0.15);
    }

    .testimonial-content {
      margin-bottom: 30px;

      p {
        font-size: 16px;
        color: var(--light-gray);
        line-height: 1.6;
        font-style: italic;
        margin: 0;
      }
    }

    .testimonial-author {
      .author-info {
        h4 {
          font-size: 18px;
          color: var(--light);
          margin-bottom: 4px;
          font-weight: 600;
        }

        span {
          font-size: 14px;
          color: var(--primary);
        }
      }
    }
  }

  .stats-section {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
    padding-top: 60px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .stat-item {
    text-align: center;

    .stat-number {
      font-size: 48px;
      font-weight: 700;
      color: var(--primary);
      display: block;
      margin-bottom: 12px;
    }

    .stat-label {
      font-size: 16px;
      color: var(--light-gray);
    }
  }
}

/* 行动号召区 */
.cta {
  padding: 100px 0;
  background: linear-gradient(135deg, var(--primary) 0%, #d63a1f 100%);
  color: var(--light);
  text-align: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><path fill="rgba(255,255,255,0.05)" d="M50 0 L100 50 L50 100 L0 50 Z"/></svg>');
    background-size: 20px 20px;
    opacity: 0.5;
  }

  .cta-content {
    position: relative;
    z-index: 2;
  }

  .cta-title {
    font-size: 42px;
    margin-bottom: 24px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    font-weight: 700;
  }

  .cta-subtitle {
    font-size: 18px;
    max-width: 700px;
    margin: 0 auto 40px;
    opacity: 0.9;
    line-height: 1.6;
  }

  .contact-info {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin-bottom: 40px;

    .contact-item {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 18px;
      font-weight: 600;

      i {
        font-size: 20px;
        color: rgba(255, 255, 255, 0.8);
      }

      a {
        color: var(--light);
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 50px;
  }

  .follow-section {
    p {
      font-size: 16px;
      margin-bottom: 20px;
      opacity: 0.8;
    }

    .social-links {
      display: flex;
      justify-content: center;
      gap: 20px;

      .social-link {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--light);
        text-decoration: none;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
        }

        i {
          font-size: 20px;
        }
      }
    }
  }
}

/* 动画定义 */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 0 40px;
  }

  .hero .hero-title {
    font-size: 44px;
  }

  .features-tabs .tab-nav {
    grid-template-columns: repeat(3, 1fr);
  }

  .value-grid,
  .advantages-grid,
  .testimonials {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 992px) {
  .container {
    padding: 0 20px;
  }

  .hero {
    padding-top: 73px;
    min-height: 80vh;
  }

  .hero .hero-title {
    font-size: 36px;
  }

  .hero .hero-subtitle {
    font-size: 20px;
  }

  .section-title h2 {
    font-size: 30px;
  }

  .features-tabs .tab-nav {
    grid-template-columns: repeat(2, 1fr);
  }

  .feature-display {
    flex-direction: column;
    text-align: center;
    gap: 40px !important;
  }

  .value-grid,
  .advantages-grid,
  .testimonials,
  .auxiliary-features {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .stats-section {
    grid-template-columns: repeat(2, 1fr);
  }

  .contact-info {
    flex-direction: column;
    gap: 20px;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
    gap: 15px;

    .btn {
      width: 80%;
      max-width: 300px;
    }
  }
}

@media (max-width: 768px) {
  .hero .hero-title {
    font-size: 28px;
  }

  .hero .hero-subtitle {
    font-size: 18px;
  }

  .hero .hero-buttons {
    flex-direction: column;
    align-items: center;
    gap: 15px;

    .btn {
      width: 80%;
      max-width: 250px;
    }
  }

  .features-tabs .tab-nav {
    grid-template-columns: 1fr;
  }

  .stats-section {
    grid-template-columns: 1fr;
  }

  .cta .cta-title {
    font-size: 28px;
  }

  .section-title h2 {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .hero .hero-title {
    font-size: 24px;
  }

  .section-title h2 {
    font-size: 20px;
  }

  .value-card,
  .advantage-item,
  .testimonial-card {
    padding: 20px;
  }
}
</style>
