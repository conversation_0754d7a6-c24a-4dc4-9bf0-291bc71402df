<template>
  <!-- 插件 -->
  <section class="plugin" id="plugin">
    <div class="container">
      <div class="section-title">
        <h2>SolidWorks 插件</h2>
      </div>
      <div class="plugin-container">
        <div class="plugin-image" data-aos="fade-right">
          <img src="~/assets/images/plugin.png" alt="SolidWorks插件界面" />
        </div>
        <div class="plugin-content" data-aos="fade-left" data-aos-delay="200">
          <p class="plugin-desc">
            研选工场SolidWorks插件为您的设计工作提供强大支持，让零部件选型与设计过程完美融合。无需在多个平台间切换，即可在SolidWorks环境中轻松访问、搜索和导入所需零部件。
          </p>
          <div class="plugin-features">
            <div class="plugin-feature">
              <i class="fas i-fa-search"></i>
              <div>
                <h4>智能搜索</h4>
                <p>直接在SolidWorks界面中搜索平台全部零部件库，包括3D模型、参数和供应信息</p>
              </div>
            </div>
            <div class="plugin-feature">
              <i class="fas i-fa-cloud-download-alt"></i>
              <div>
                <h4>一键导入</h4>
                <p>无需手动下载，一键将3D模型导入当前设计环境，节省大量时间</p>
              </div>
            </div>
            <div class="plugin-feature">
              <i class="fas i-fa-sync-alt"></i>
              <div>
                <h4>实时更新</h4>
                <p>零部件数据实时同步，确保您始终使用最新的模型和信息</p>
              </div>
            </div>
            <div class="plugin-feature">
              <i class="fas i-fa-cart-plus"></i>
              <div>
                <h4>快速下单</h4>
                <p>从设计环境直接提交采购需求，缩短从设计到采购的流程</p>
              </div>
            </div>
          </div>
          <div class="plugin-buttons">
            <div class="btn btn-primary" @click="download">
              下载插件
              <loading-outlined v-if="donwloadLoading"></loading-outlined>
            </div>
            <div class="btn btn-outline" @click="toggleHistory">
              {{ showHistory ? '收起历史版本' : '查看历史版本' }}
            </div>
          </div>
        </div>
      </div>

      <!-- 历史版本区域 -->
      <div v-if="showHistory" class="version-history" data-aos="fade-up">
        <div class="version-history-header">
          <h3>历史版本</h3>
          <p>查看所有插件的历史版本和更新日志</p>
        </div>

        <div v-if="versionsLoading" class="version-loading">
          <loading-outlined />
          <span>加载历史版本中...</span>
        </div>

        <div v-else-if="versions.length" class="version-list">
          <div v-for="(version, index) in versions" :key="version.id" class="version-item">
            <div class="version-info">
              <div class="version-header">
                <span class="version-number">v{{ version.version }}</span>
                <template v-if="index == 0">
                  <span class="version-badge current">当前版本</span>
                  <span class="version-badge new">最新</span>
                </template>
              </div>
              <div class="version-time">发布时间：{{ formatDate(version.publishTime) }}</div>
              <div v-if="version.remark" class="version-description">
                {{ version.remark }}
              </div>
            </div>
            <div class="version-actions">
              <div class="btn btn-sm btn-outline" @click="viewChangelog(version)">
                <i class="fas i-fa-list-ul"></i>
                更新日志
              </div>
              <div class="btn btn-sm btn-primary" @click="downloadVersion(version)">
                <loading-outlined v-if="version.downloading"></loading-outlined>
                <i v-else class="fas i-fa-download"></i>
                下载
              </div>
            </div>
          </div>
        </div>

        <div v-else class="version-empty">
          <i class="fas i-fa-inbox"></i>
          <p>暂无历史版本</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { _http } from '~/api/mall-platform'
import fileDownload from 'js-file-download'

definePageMeta({
  layout: 'page',
})

// 历史版本相关状态
const showHistory = ref(false)
const versions = ref([])
const versionsLoading = ref(false)

// 下载最新版本
const donwloadLoading = ref(false)
const download = async () => {
  if (donwloadLoading.value) return
  donwloadLoading.value = true
  const [err, res] = await _try(() =>
    _http.get('/shop/pluginVersion/page', {
      status: 1,
    }),
  )
  donwloadLoading.value = false
  if (!err) {
    const data = parseJson(res.data, {})
    if (data.records?.length) {
      const file = data.records[0]
      const { filePath, version } = file
      $fetch(useObs(filePath), {
        responseType: 'blob',
      }).then((res) => {
        const ext = filePath.split('.').at(-1)
        fileDownload(res, `YanXuanForSWInstaller_${version}.${ext}`)
      })
    }
  } else {
    console.error('下载失败', err)
  }
}

// 切换历史版本显示
const toggleHistory = async () => {
  showHistory.value = !showHistory.value
  if (showHistory.value && versions.value.length === 0) {
    await loadVersionHistory()
  }
}

// 加载历史版本
const loadVersionHistory = async () => {
  versionsLoading.value = true
  const [err, res] = await _try(() =>
    _http.get('/shop/pluginVersion/page', {
      status: 1, // 应该获取发布后的版本
      pageSize: 20,
      pageNum: 1,
    }),
  )
  versionsLoading.value = false

  if (!err) {
    const data = parseJson(res.data, {})
    if (data.records?.length) {
      // 按创建时间排序，最新的在前面
      versions.value = data.records.map((item, index) => ({
        ...item,
        downloading: false,
      }))
    }
  } else {
    console.error('加载历史版本失败', err)
  }
}

// 下载指定版本
const downloadVersion = async (version) => {
  if (version.downloading) return
  version.downloading = true

  try {
    const res = await $fetch(useObs(version.filePath), {
      responseType: 'blob',
    })
    const ext = version.filePath.split('.').at(-1)
    fileDownload(res, `YanXuanForSWInstaller_${version.version}.${ext}`)
  } catch (error) {
    console.error('下载失败', error)
    message.error('下载失败，请稍后重试')
  } finally {
    version.downloading = false
  }
}

// 查看更新日志
const viewChangelog = (version) => {
  if (version.updateLog) {
    // 如果有备注，显示在弹窗中
    Modal.info({
      title: `v${version.version} 更新日志`,
      content: h(
        'div',
        {
          innerHTML: version.updateLog,
        },
        '123',
      ),
      width: 600,
    })
  } else {
    message.info('该版本暂无更新日志')
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const bomai = useRuntimeConfig().public.VITE_BOMAI_URL
const goHistory = () => {
  window.open(bomai + '/plugin-history', '_blank')
}
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;
  padding: 0 80px;
}

.btn {
  padding: 10px 24px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  text-decoration: none;
  display: inline-block;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
  z-index: 1;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transition: left 0.3s ease-in-out;
    z-index: -1;
  }

  &:hover::before {
    left: 0;
  }

  &.btn-big {
    padding: 14px 32px;
    font-size: 20px;
  }
}

.btn-primary {
  background-color: var(--primary);
  color: var(--light);
  border-color: var(--primary);

  &:hover {
    background-color: #e03a20;
    border-color: #e03a20;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(249, 76, 48, 0.4);
  }
}

.btn-outline {
  background-color: transparent;
  color: var(--light);
  border: 1px solid var(--light-gray);

  &:hover {
    background-color: var(--primary);
    color: var(--light);
    border-color: var(--primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(249, 76, 48, 0.4);
  }
}

/* Section Base Styles */
.section-title {
  text-align: center;
  margin-bottom: 60px;

  h2 {
    font-size: 36px;
    color: var(--light);
    margin-bottom: 16px;
  }

  p {
    font-size: 18px;
    color: var(--light-gray);
    max-width: 800px;
    margin: 0 auto;
  }
}

/* 插件区 */
.plugin {
  padding: 120px 0 100px;
  background-color: var(--secondary);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .plugin-container {
    display: flex;
    align-items: center;
    gap: 60px;
    margin-top: 60px;
  }

  .plugin-image {
    flex: 1;
    max-width: 50%;
    margin-right: 100px;

    img {
      width: 100%;
      border-radius: 10px;
    }
  }

  .plugin-content {
    flex: 1;
  }

  .plugin-title {
    font-size: 32px;
    color: var(--light);
    margin-bottom: 24px;
  }

  .plugin-desc {
    font-size: 17px;
    color: var(--light-gray);
    line-height: 1.7;
    margin-bottom: 40px;
  }

  .plugin-features {
    margin-bottom: 40px;
  }

  .plugin-feature {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;

    i {
      color: var(--primary);
      font-size: 24px;
      margin-right: 20px;
      min-width: 24px;
    }

    h4 {
      font-size: 20px;
      color: var(--light);
      margin-bottom: 8px;
    }

    p {
      font-size: 16px;
      color: var(--light-gray);
      line-height: 1.5;
    }
  }

  .plugin-buttons {
    display: flex;
    gap: 20px;
  }

  /* 历史版本区域 */
  .version-history {
    margin-top: 60px;
    padding-top: 60px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .version-history-header {
    text-align: center;
    margin-bottom: 40px;

    h3 {
      font-size: 28px;
      color: var(--light);
      margin-bottom: 12px;
    }

    p {
      font-size: 16px;
      color: var(--light-gray);
    }
  }

  .version-loading {
    text-align: center;
    padding: 40px 0;
    color: var(--light-gray);
    font-size: 16px;

    .anticon {
      margin-right: 8px;
      font-size: 18px;
    }
  }

  .version-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .version-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease-in-out;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }
  }

  .version-info {
    flex: 1;
    margin-right: 20px;
  }

  .version-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;

    .version-number {
      font-size: 20px;
      font-weight: 600;
      color: var(--light);
    }

    .version-badge {
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;

      &.current {
        background: var(--primary);
        color: var(--light);
      }

      &.new {
        background: #52c41a;
        color: var(--light);
      }
    }
  }

  .version-time {
    font-size: 14px;
    color: var(--light-gray);
    margin-bottom: 8px;
  }

  .version-description {
    font-size: 15px;
    color: var(--light-gray);
    line-height: 1.5;
    max-width: 600px;
  }

  .version-actions {
    display: flex;
    gap: 12px;
    flex-shrink: 0;
  }

  .btn-sm {
    padding: 8px 16px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;

    i {
      font-size: 12px;
    }

    .anticon {
      font-size: 12px;
    }
  }

  .version-empty {
    text-align: center;
    padding: 60px 0;
    color: var(--light-gray);

    i {
      font-size: 48px;
      color: rgba(255, 255, 255, 0.3);
      margin-bottom: 16px;
      display: block;
    }

    p {
      font-size: 16px;
    }
  }
}

/* Responsive styles for plugin section */
@media (max-width: 1200px) {
  .plugin .plugin-title {
    font-size: 28px;
  }
}

@media (max-width: 992px) {
  .plugin {
    padding: 80px 0;
  }

  .plugin .plugin-container {
    flex-direction: column;
    gap: 40px;
  }

  .plugin .plugin-image {
    max-width: 100%;
    margin-right: 0;
  }

  .plugin .plugin-content {
    text-align: center;
  }

  .plugin .plugin-feature {
    text-align: left;
  }

  .plugin .plugin-buttons {
    justify-content: center;
  }

  .plugin .version-history {
    margin-top: 40px;
    padding-top: 40px;
  }

  .plugin .version-history-header h3 {
    font-size: 24px;
  }

  .plugin .version-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .plugin .version-info {
    margin-right: 0;
  }

  .plugin .version-actions {
    align-self: stretch;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 40px;
  }

  .plugin .plugin-title {
    font-size: 26px;
  }

  .plugin .plugin-desc {
    font-size: 16px;
  }

  .plugin .plugin-feature h4 {
    font-size: 18px;
  }

  .plugin .plugin-feature p {
    font-size: 15px;
  }

  .plugin .version-history-header h3 {
    font-size: 22px;
  }

  .plugin .version-history-header p {
    font-size: 15px;
  }

  .plugin .version-item {
    padding: 20px;
  }

  .plugin .version-header .version-number {
    font-size: 18px;
  }

  .plugin .version-description {
    font-size: 14px;
  }

  .plugin .btn-sm {
    font-size: 13px;
    padding: 6px 12px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 20px;
  }

  .plugin .plugin-title {
    font-size: 24px;
  }

  .plugin .plugin-buttons {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .plugin .plugin-buttons .btn {
    width: 80%;
    max-width: 250px;
  }

  .plugin .version-history {
    margin-top: 30px;
    padding-top: 30px;
  }

  .plugin .version-history-header h3 {
    font-size: 20px;
  }

  .plugin .version-history-header p {
    font-size: 14px;
  }

  .plugin .version-item {
    padding: 16px;
    gap: 16px;
  }

  .plugin .version-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .plugin .version-actions {
    flex-direction: column;
    gap: 10px;
  }

  .plugin .version-actions .btn {
    width: 100%;
    justify-content: center;
  }

  .plugin .version-empty {
    padding: 40px 0;
  }

  .plugin .version-empty i {
    font-size: 36px;
  }
}
</style>
