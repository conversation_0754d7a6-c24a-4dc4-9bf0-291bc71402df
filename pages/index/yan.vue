<template>
  <div class="text-white">
    <div class="min-h-100vh pt-10% pb-4 flex flex-col items-center max-w-1280px mx-auto px-5">
      <div max-w-840px flex flex-col items-center>
        <h1 text="white 60px">
          <img h-15 src="~/assets/images/logo_rect_white.png" />
        </h1>

        <h2 mt-1 font-normal text-20px>装备制造业AI供应链服务平台</h2>

        <div class="mt-6 mb-2 text-24px">{{ tip }}</div>

        <ask-input @send="send" v-model="ask"></ask-input>

        <div class="w-full mt-8">
          <div class="mb-4" v-for="item in groups" :key="item.name">
            <div class="flex flex-col md:flex-row justify-between gap-2">
              <div v-for="ask in item.asks" :key="ask.ask"
                class="w-full md:w-auto inline-block border border-white bg-transparent rounded-full p-2 cursor-pointer hover:bg-opacity-20 hover:bg-white transition-colors flex items-center"
                @click="onAsk(ask.ask)">
                <span class="text-white text-14px">{{ ask.ask }}</span>
                <span class="i-ant-design-arrow-right-outlined text-white ml-1"></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 智能体区域使用新容器，宽度为1200px -->
      <div class="w-full max-w-1280px mx-auto px-5">
        <div class="w-full mt-8 flex flex-col gap-8">
          <!-- 等平台端的智能体管理完成后，以下模块需要改为动态获取 -->
          <!-- 官方智能体区域 -->
          <div class="w-full">
            <h3 class="text-18px pb-2">官方智能体</h3>

            <div class="space-y-4 md:space-y-0 md:grid md:grid-cols-[repeat(auto-fill,minmax(290px,1fr))] md:gap-3">
              <!-- 小妍 -->
              <div
                class="flex items-start gap-3 p-4 rounded-lg cursor-pointer border border-white/20 hover:bg-white/10 transition-all h-24 w-full md:w-290px"
                @click="goChat(0)">
                <img src="~/assets/images/yan_avatar.png" class="w-12 h-12 rounded-full" />
                <div>
                  <div class="text-16px font-medium">小妍</div>
                  <div class="text-14px text-gray-300 mt-1 line-clamp-2">
                    研选工场官方智能体总管，负责研选全站品牌、产品搜索，以及其他智能体引荐
                  </div>
                </div>
              </div>

              <!-- 研小比 -->
              <div
                class="flex items-start gap-3 p-4 rounded-lg cursor-pointer border border-white/20 hover:bg-white/10 transition-all h-24 w-full md:w-290px"
                @click="goChat(1, true)">
                <img src="~/assets/images/yanxiaobi_avatar.png" class="w-12 h-12 rounded-full" />
                <div>
                  <div class="text-16px font-medium">研小比</div>
                  <div class="text-14px text-gray-300 mt-1 line-clamp-2">
                    研选工场官方专用查价比价智能体，负责一站式产品跨平台比价
                  </div>
                </div>
              </div>

              <!-- 添加智能体按钮 -->
              <!-- <div class="block w-full md:w-290px">
                <div class="h-24 flex items-center justify-center gap-2 border border-white/20 rounded-lg p-3 group">
                  <span>更多官方智能体即将到来</span>
                </div>
              </div> -->
            </div>
          </div>

          <!-- 品牌智能体区域 -->
          <div class="w-full mb-8">
            <h3 class="text-18px pb-2">品牌智能体</h3>

            <div class="space-y-4 md:space-y-0 md:grid md:grid-cols-[repeat(auto-fill,minmax(290px,1fr))] md:gap-3">
              <div v-for="agent in brandAgentList" :key="agent.id"
                class="flex items-start gap-3 p-4 rounded-lg cursor-pointer border border-white/20 hover:bg-white/10 transition-all h-24 w-full md:w-290px"
                @click="goChat(agent.agentId)">
                <img :src="useImage(convertImage(agent.agentImg))" class="w-12 h-12 rounded-full" />
                <div>
                  <div class="text-16px font-medium">{{ agent.agentName }}</div>
                  <div class="text-14px text-gray-300 mt-1 line-clamp-2">{{ agent.description }}</div>
                </div>
              </div>

              <!-- 添加智能体按钮 -->
              <div class="block w-full md:w-290px">
                <div
                  class="h-24 flex items-center justify-center gap-2 border border-white/20 rounded-lg p-3 hover:bg-white/10 transition-all group cursor-pointer"
                  @click="joinBrand">
                  <span>加入研选工场，成为严选供应商</span>
                  <span
                    class="i-ant-design-arrow-right-outlined text-20px transform group-hover:translate-x-1 transition-transform"></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="max-w-840px mx-auto px-5 w-full flex flex-col items-center">
        <div class="flex-1"></div>

        <footer class="text-#b4b4b4 text-13px">
          <div class="flex justify-center gap-4">
            <NuxtLink :to="{ path: '/agreement', query: { key: 'termOfService' } }">用户协议</NuxtLink>
            <NuxtLink :to="{ path: '/agreement', query: { key: 'disclaimer' } }">免责声明</NuxtLink>
            <NuxtLink :to="{ path: '/agreement', query: { key: 'privacyPolicy' } }">隐私政策</NuxtLink>
          </div>
          <div class="mt-4">
            <span>Copyright&copy;{{ currentYear }}&nbsp;&nbsp;研选工场（苏州）网络有限公司 |</span>
            <a href="https://beian.miit.gov.cn/">苏ICP备2024149956号</a>
          </div>
        </footer>
      </div>
    </div>
    <side-widget></side-widget>
  </div>
</template>

<script setup lang="ts">
const user = userStore()
const isLogin = useLoginState()

const currentYear = ref(new Date().getFullYear())

const brandAgentList = ref<obj[]>([])

const joinBrand = () => {
  const url = (useRuntimeConfig().public.VITE_BOMAI_URL as string) + '/brand-join'
  window.open(url, '_blank')
}

const tip = computed(() => {
  const username = user.user.nickName
  if (username) {
    return `你好，${username}，我是小妍，你有选型和采购的问题都可以问我！`
  }
  return '你好，我是小妍，你有选型和采购的问题都可以问我！'
})

const groups = [
  {
    name: '关于研选工场',
    asks: [
      {
        ask: '研选工场是一家什么样的公司？',
      },
      {
        ask: '能否给我介绍一下研选工场的主营业务？',
      },
      {
        ask: '怎么才能联系到研选工场？',
      },
    ],
  },
  // {
  //   name: '关于小妍',
  //   asks: [
  //     {
  //       ask: '小妍是谁？',
  //     },
  //     {
  //       ask: '小妍能做什么？',
  //     },
  //     {
  //       ask: '我要如何让小妍帮我提高采购效率？',
  //     },
  //   ],
  // },
  // {
  //   name: '关于研选品牌馆',
  //   asks: [
  //     {
  //       ask: '研选品牌馆是什么？',
  //     },
  //     {
  //       ask: '加入研选品牌馆后具体能获得哪些权益？',
  //     },
  //     {
  //       ask: '我要如何才能加入研选品牌馆？',
  //     },
  //   ],
  // },
]

const ask = ref('')
const router = useRouter()

const onAsk = (str: string = '') => {
  ask.value = str
  send()
}

const send = (value: string = '') => {
  if (!value) {
    return
  }
  // 首页聊天框点击跳转
  router.push({
    path: '/chat',
    query: {
      ask: value,
    },
  })
}

const goChat = (shop_id: string | number, needLogin: boolean = false) => {
  if (needLogin && !isLogin.value) {
    message.info('请先登录')
    navigateTo({
      path: '/login',
    })
  } else {
    navigateTo({
      path: '/chat',
      query: {
        shop_id: shop_id,
      },
    })
  }
}

const env = useRuntimeConfig().public.VITE_API_BASE
console.log("%c Line:227 🥝 env", "color:#7f2b82", env);
const getBrandAgentList = async () => {
  if (env.endsWith('cloud')) {
    brandAgentList.value = [
      {
        status: 1,
        type: 2,
        orderNum: 1,
        agentId: '164843427033339',
        agentName: '小赛',
        agentImg: '2025/03/214bd18e37a343dea03d8555846afd5f.png',
        description: '赛得尔智能体',
        createdTime: '2025-02-14 19:29:36',
        updatedTime: '2025-03-31 14:45:46',
      },
      {
        status: 1,
        type: 2,
        orderNum: 2,
        agentId: '164843427028198',
        agentName: '施耐德',
        agentImg: 'https://obs-bbc-yx.obs.cn-east-3.myhuaweicloud.com/2025/03/b1abfd51a8a84cb79b85270eda5c0315.png',
        description: '施耐德电气品牌智能体',
        createdTime: '2025-03-28 10:43:52',
        updatedTime: '2025-03-31 14:00:02',
      },
      {
        status: 1,
        type: 2,
        orderNum: 3,
        agentId: '164843427028086',
        agentName: 'SMC',
        agentImg: 'https://obs-bbc-yx.obs.cn-east-3.myhuaweicloud.com/2025/03/75e819ab6a594bd494e799b50e2cf733.png',
        description: 'SMC品牌智能体',
        createdTime: '2025-03-28 14:00:52',
        updatedTime: '2025-03-31 14:43:30',
      },
    ]
  } else {
    const [err, res] = await try_http('/mall/p/llm/agent/agentList', {
      query: getshopsign(
        {
          type: 2,
        },
        'get'
      ),
      headers: {
        grantType: 'sign',
      },
    })
    if (!err) {
      const data = JSON.parse(res.data)
      brandAgentList.value = data.slice(0, 3)
    }
  }
}

onMounted(() => {
  getBrandAgentList()
})
</script>
