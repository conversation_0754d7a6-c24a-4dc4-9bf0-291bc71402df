<template>
  <a-form
    ref="formRef"
    :rules="rules"
    :model="formState"
    :disabled="!formEditable"
    :label-col="{ span: 24 }"
    autocomplete="off"
  >
    <a-form-item text-center name="merchantLogo" label="企业LOGO">
      <img-upload
        type="avatar"
        name="merchantLogo"
        :value="formState.merchantLogo"
        :beforeUpload="beforeUpload"
        @change="(v) => (formState.merchantLogo = v)"
      />
      <!-- <img v-if="!editable" :src="formState.merchantLogo" alt="logo" /> -->

      <!-- <img v-if="formState.pic" :src="formState.pic" alt="avatar" w-full h-full />
      <div v-else>
        <plus-outlined></plus-outlined>
        <div class="ant-upload-text">企业LOGO</div>
      </div> -->

      <div color-gray>限2MB以内的jpg、jpeg、png文件</div>
    </a-form-item>
    <a-form-item name="merchantShortName" label="企业简称">
      <a-input v-model:value.trim="formState.merchantShortName" size="large" :maxlength="10" />
    </a-form-item>
    <a-form-item name="merchantSlogan" label="企业介绍">
      <a-textarea v-model:value.trim="formState.merchantSlogan" :maxlength="200" showCount size="large" />
    </a-form-item>

    <a-form-item name="merchantPhone" label="联系电话">
      <a-input v-model:value.trim="formState.merchantPhone" size="large" :maxlength="13" />
    </a-form-item>
    <a-form-item name="merchantMail" label="邮箱">
      <a-input v-model:value.trim="formState.merchantMail" size="large" />
    </a-form-item>
    <a-form-item name="mainProduct" label="主营">
      <a-textarea v-model:value.trim="formState.mainProduct" :maxlength="500" showCount size="large" />
    </a-form-item>
    <a-form-item name="merchantWebsite" label="企业官网">
      <a-input v-model:value.trim="formState.merchantWebsite" size="large" />
    </a-form-item>

    <a-form-item text-center name="promotionalImg" label="企业宣传图">
      <img-upload
        name="promotionalImg"
        :value="formState.promotionalImg"
        :beforeUpload="beforeUpload"
        @change="(v) => (formState.promotionalImg = v)"
      />
      <div sub-text>限2MB以内的jpg、jpeg、png文件</div>
    </a-form-item>
  </a-form>
</template>

<script setup>
import {} from 'vue'
const formRef = ref(null)
const formType = ref(1) // 1-创建，2-更新
const formEditable = ref(true)
const formState = ref({
  merchantShortName: '',
  mainProduct: '',
  merchantLogo: '',
  promotionalImg: '',
  merchantSlogan: '',
  merchantWebsite: '',
  merchantMail: '',
  merchantPhone: '',
})
const isTel = (v) => {
  // 400电话的正则
  const tel1 = /^400[0-9]{7}$/
  // 800电话正则
  const tel2 = /^800[0-9]{7}$/
  // 手机号码正则
  const tel3 = /^1[3-9]([0-9]{9})$/
  // 座机号码正则
  const tel4 = /^0\d{2,3}-\d{7,8}$/
  return tel1.test(v) || tel2.test(v) || tel3.test(v) || tel4.test(v)
}

const validateTel = async (rule, value) => {
  if (value) {
    if (isTel(value)) {
      return Promise.resolve()
    } else {
      return Promise.reject('请输入正确的电话')
    }
  } else {
    return Promise.resolve()
  }
}

const isEmail = (s) => {
  return /^([a-zA-Z0-9_.-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(s)
}

const validateEmail = async (rule, value) => {
  if (!value) return Promise.resolve()
  if (!isEmail(value)) {
    return Promise.reject('请输入正确的邮箱')
  } else {
    return Promise.resolve()
  }
}

const validateMobile = async (rule, value) => {
  if (!value) return Promise.resolve()
  if (value) {
    const mobile = /^(?:(?:\+|00)86)?1\d{2}([\d*]{4})\d{4}$/
    if (mobile.test(value)) {
      return Promise.resolve()
    } else {
      return Promise.reject('请输入正确的联系方式')
    }
  }
}

const beforeUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('您只能上传jpg、jpeg和png格式的图片')
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过2MB')
  }
  return isJpgOrPng && isLt2M
}

const rules = ref({
  merchantShortName: [{ required: true, message: '品牌名称不能为空', trigger: 'blur' }],
  userMobile: [
    { required: true, message: '联系人电话不能为空', trigger: 'blur' },
    { validator: validateMobile, trigger: 'blur' },
  ],
  merchantPhone: [{ validator: validateTel, trigger: 'blur' }],
  merchantMail: [{ validator: validateEmail, trigger: 'blur' }],
  merchantPhone: [{ validator: validateTel, trigger: 'blur' }],
  merchantMail: [{ validator: validateEmail, trigger: 'blur' }],
  // businessLiense: [{ required: true, message: '营业执照不能为空', trigger: 'blur' }],
})

const init = (type, data, editable) => {
  formType.value = type
  if (!!data) {
    formState.value = data
  }
  formEditable.value = editable
}

defineExpose({ init, formRef, formState })
</script>

<style scoped></style>
