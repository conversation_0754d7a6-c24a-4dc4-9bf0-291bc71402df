<template>
  <div flex-center text-14px overflow-hidden flex flex-col h-75 color-primaryText bg-primaryBg :key="item.shopId">
    <!-- shopName: 商城名称 intro: 简介 mainProduct: 主营 firmName: 企业名称 -->
    <div w-full flex-1 px-4 text-center>
      <div inline-block rounded-full w-20 h-20 my-4 overflow-hidden>
        <img w-full h-full object-scale-down :src="useImage(convertImage(item.merchantLogo))" alt="" />
      </div>
      <div font-bold truncate text-center font-size-4 :title="item.merchantName">
        {{ item.merchantName }}
      </div>
      <div h-15 line-clamp-3 text-gray my-6 text-left :title="item.merchantSlogan">
        {{ item.merchantSlogan }}
      </div>
      <!-- <div line-clamp-2 text-gray mt-8 text-left :title="item.mainProduct">{{ item.mainProduct }}</div> -->
      <div flex items-center justify-center>
        <!-- <a-button w-25 size="large">品牌馆</a-button> -->
        <a-button min-w-25 :disabled="disabled" type="primary" size="large" @click="handleClick">
          {{ text }}
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { type CompItem, ComResult, ComStatus } from '~/api/mall-platform/types'

const props = defineProps<{
  item: CompItem
  map: Map<number, [ComStatus, ComResult]>
}>()

const { item, map } = toRefs(props)

const emits = defineEmits<{
  click: []
}>()

const text = computed(() => {
  const status = map.value.get(item.value.shopCompanyId)
  if (status == undefined) return '申请加入'

  const [applyStatus, applyResult] = status
  if (applyStatus == ComStatus.noApply) return '已发送申请'
  else if (applyStatus == ComStatus.apply) {
    if (applyResult == ComResult.fail) {
      return '已拒绝,重新申请'
    } else {
      return '已加入'
    }
  }
  return '申请加入'
})

const disabled = computed(() => {
  if (text.value == '已拒绝,重新申请' || text.value == '申请加入') return false
  return true
})

const handleClick = () => {
  if (!disabled.value) {
    emits('click')
  }
}
</script>
