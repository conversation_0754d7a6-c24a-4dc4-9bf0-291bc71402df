<template>
  <NuxtLayout name="child-page" title="加入企业">
    <div w-full max-w-200>
      <a-input-search
        v-model:value="page.merchantName"
        enter-button
        size="large"
        placeholder="搜索企业名称"
        @search="search"
        allow-clear
      />
      <img v-if="!hasSearch" src="" mx-auto block alt="" />
      <template v-else>
        <!-- <card-skeleton :count="page.size" v-if="loading"></card-skeleton> -->
        <template v-if="companyList.length">
          <div grid grid-cols-3 gap="x-5 y-5" mt-5>
            <Item
              v-for="item in companyList"
              :key="item.shopCompanyId"
              :item="item"
              :map="userMap"
              @click="apply(item)"
            ></Item>
          </div>

          <a-pagination
            v-model:current="page.current"
            v-model:page-size="page.size"
            text-center
            m-4
            :total="page.total"
            @change="handleChange"
          ></a-pagination>
        </template>
        <Empty mt-40 v-else />
      </template>
    </div>
  </NuxtLayout>
</template>

<script lang="ts" setup>
import {} from 'vue'
import { type CompBind, type CompItem, ComResult, ComStatus } from '~/api/mall-platform/types'
// import { Pagination } from '~/api/shop/type'
// import join from '~/assets/images/join.png'
import Item from './components/com-item.vue'
import { getShopCompanyByMerchantName, getShopCompanyByUser } from '~/api/mall-platform'
import { userStore } from '~/store/user'
const user = userStore()
const hasSearch = ref(true)

const companyList = ref<CompItem[]>([])
const page = reactive({
  current: 1,
  size: 9,
  merchantName: '',
  total: 0,
})

const userMap = ref<Map<number, [ComStatus, ComResult]>>(new Map())

const loading = ref(0)
const bindLoading = ref(false)

const fetchCompany = async () => {
  hasSearch.value = true

  const res = await getShopCompanyByMerchantName(pick(page, ['current', 'size', 'merchantName']))

  useMall(res, () => {
    const ret = JSON.parse(res.data) as Pagination<CompItem>
    companyList.value = ret.records
    page.total = ret.total
  })
}

const fetchBindList = async () => {
  const res = await getShopCompanyByUser({
    size: 9999,
    userMobile: user.user.userMobile,
  })
  useMall(res, () => {
    const ret = JSON.parse(res.data) as Pagination<CompBind>
    const map = new Map()
    ret.records.forEach((item) => {
      if (!map.get(item.merchantId)) {
        map.set(item.merchantId, [item.auditStatus, item.auditResult])
      }
    })
    userMap.value = map
  })
}

const apply = async (item: CompItem) => {
  if (bindLoading.value) return
  bindLoading.value = true
  const [err] = await _try(() =>
    http.post('/mall-platform/shop/merchantUser', {
      merchantId: item.shopCompanyId,
      userMobile: user.user.userMobile,
      shopId: 0,
      auditStatus: ComStatus.noApply,
      auditResult: ComResult.fail,
    })
  )
  bindLoading.value = false
  if (!err) {
    message.success('发送申请成功')
    fetchBindList()
  }
}
const fetchData = async () => {
  page.current = 1
  loading.value++
  await Promise.all([fetchBindList(), fetchCompany()])
  loading.value--
}

const search = () => {
  if (!page.merchantName) {
    return
  }
  fetchData()
}

const handleChange = () => {
  fetchData()
}
</script>

<style scoped></style>
