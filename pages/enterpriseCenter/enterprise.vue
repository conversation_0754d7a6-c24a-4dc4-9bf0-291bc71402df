<template>
  <NuxtLayout name="child-page" title="企业信息">
    <div w-full max-w-150>
      <a-alert v-if="mallStatus == 3" text-center message="您的企业正在审核中，请耐心等待审核结果" type="warning" />
      <a-alert
        v-else-if="mallStatus === 50"
        text-center
        message="您的企业未能通过审核，请在消息中心中查看详情"
        type="error"
        closable
      />
      <enterpriseForm ref="enterpriseFormRef" />
      <div v-if="mallStatus !== 3" flex items-center justify-between>
        <a-button flex-1 mr-4 size="large" @click="onReset">重置</a-button>
        <a-button flex-1 type="primary" size="large" @click="onSubmit">提交审核</a-button>
      </div>
      <!-- <div flex v-else> -->
      <!--   <a-button flex-1 size="large" @click="onCancel">撤销审核</a-button> -->
      <!-- </div> -->
    </div>
  </NuxtLayout>
</template>

<script setup>
import enterpriseForm from './components/enterpriseForm.vue'
import { companyStore } from '~/store/company'
import { putShopCompany } from '~/api/mall-manage/index'
import { getShopCompanyByShopCompanyId } from '~/api/mall-platform/index'
import { cloneDeep } from 'lodash-es'
const enterpriseFormRef = ref(null)
const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)
const companyStoreObj = companyStore()
const company = ref({})
const oldCompany = ref({})

const mallStatus = computed(() => {
  // 0: 审核中 1: 正常 2: 审核不通过
  return company.value.status
})

const getInfo = (companyId) => {
  getShopCompanyByShopCompanyId(companyId).then((res) => {
    useMall(res, (data) => {
      if (data) {
        company.value = JSON.parse(data)
        oldCompany.value = JSON.parse(data)
        enterpriseFormRef.value?.init(2, company.value, mallStatus.value !== 3)
      }
    })
  })
}

const onSubmit = async () => {
  await enterpriseFormRef.value.formRef.validate()
  const formState = enterpriseFormRef.value.formState
  const submitData = {
    merchantShortName: formState.merchantShortName,
    merchantSlogan: formState.merchantSlogan,
    merchantPhone: formState.merchantPhone,
    merchantMail: formState.merchantMail,
    mainProduct: formState.mainProduct,
    merchantWebsite: formState.merchantWebsite,
    merchantLogo: formState.merchantLogo,
    promotionalImg: formState.promotionalImg,
    userMobile: user.value.userMobile,
    shopCompanyId: company.value.shopCompanyId,
    applyType: 5,
  }
  const res = await putShopCompany(submitData)
  if (res.code == 0) {
    getInfo(company.value.shopCompanyId)
    message.success('提交成功，请耐心等待审核')
  }
}

const onReset = () => {
  // 将公司数据映射到表单字段
  const data = cloneDeep(oldCompany.value)
  enterpriseFormRef.value?.init(2, data, mallStatus.value !== 3)
}

const onCancel = () => {
  // 撤销审核
}

const route = useRoute()
onMounted(() => {
  // 等待 DOM 更新后初始化表单
  if (route.query.companyId) {
    getInfo(route.query.companyId)
  } else {
    getInfo(companyStoreObj.company.shopCompanyId)
  }
})
</script>
