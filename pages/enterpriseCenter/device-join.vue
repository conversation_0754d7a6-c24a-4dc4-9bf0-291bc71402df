<template>
  <NuxtLayout name="child-page" title="开通设备商">
    <div w-full max-w-150>
      <div v-show="step === 0">
        <commercialForm ref="commercialFormRef" />
      </div>

      <div v-show="step === 1">
        <enterpriseForm ref="enterpriseFormRef" />
      </div>
      <div flex items-center justify-between>
        <a-button flex-1 mr-4 size="large" :disabled="step === 0" @click="onPrevious">上一步</a-button>
        <a-button flex-1 type="primary" size="large" @click="onNext">
          {{ step === 0 ? '下一步' : '完成' }}
        </a-button>
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup>
import {} from 'vue'
import { saveMember, getCompany } from '~/api/mall-manage/index'
import commercialForm from './components/commercialForm.vue'
import enterpriseForm from './components/enterpriseForm.vue'
const store = userStore()
const route = useRoute()
const router = useRouter()

const commercialFormRef = ref(null)
const enterpriseFormRef = ref(null)
const user = computed(() => store.user)

const submitData = ref({
  type: 1,
  userMobile: user.value.userMobile,
})

const step = ref(0) // 0 - 工商信息，1 - 企业信息

const onNext = async () => {
  if (step.value === 0) {
    try {
      await commercialFormRef.value.formRef.validate()
      // 验证通过，进入下一步
      submitData.value = {
        ...submitData.value,
        ...commercialFormRef.value.formState,
      }
      step.value++
    } catch (error) {
      // 验证失败，不做处理
    }
  } else {
    try {
      await enterpriseFormRef.value.formRef.validate()
      // 验证通过，提交表单
      submitData.value = {
        ...submitData.value,
        ...enterpriseFormRef.value.formState,
        type: 1,
      }
      onSubmit()
    } catch (error) {
      // 验证失败，不做处理
    }
  }
}

const onPrevious = () => {
  step.value--
}

const onSubmit = () => {
  saveMember(submitData.value).then((res) => {
    if (res.code === '00000') {
      message.success('申请已提交,请耐心等待审核')
      router.push('/enterpriseCenter')
    }
  })
}

onMounted(async () => {
  const companyId = route.query.companyId
  if (!companyId) {
    message.error('企业ID不能为空')
    router.push('/enterpriseCenter')
    return
  }

  try {
    // 获取企业信息并回显
    const companyInfo = await getCompany(companyId)
    if (companyInfo) {
      // 第三个参数为true表示是开通供应商，此时企业名称和统一社会信用代码不校验且禁止修改
      commercialFormRef.value.init(1, companyInfo, true, true)
      enterpriseFormRef.value.init(1, companyInfo, true)
    } else {
      message.error('获取企业信息失败')
      router.push('/enterpriseCenter')
    }
  } catch (error) {
    message.error('获取企业信息失败')
    router.push('/enterpriseCenter')
  }
})
</script>

<style scoped></style>
