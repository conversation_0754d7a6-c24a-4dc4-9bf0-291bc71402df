<template>
  <NuxtLayout name="child-page" title="企业中心">
    <div v-if="isPerSpace" w-full max-w-150>
      <div class="w-full">
        <a-alert type="warning">
          <template #message>
            <div text-center>
              您当前处于个人空间，请
              <span button-link @click="router.push('/switch')">切换</span>
              至企业空间，或者
              <span button-link @click="showCreateEnterpriseModal">创建企业</span>
              /
              <span button-link @click="auth('/enterpriseCenter/join')">加入企业</span>
            </div>
          </template>
        </a-alert>
        <!-- <div my-5 text-primaryText font-size-20px text-center font-bold>成为企业会员，享受以下服务</div>
        <div flex color-primaryText>
          <div mr-4 flex-1 text-center link cursor-pointer @click="router.push('/brand')">
            <div mb-2>
              <img src="~/assets/images/enter_brand.jpg" w-62.5 h-62.5 />
            </div>
            使用品牌馆打造企业形象
          </div>
          <div flex-1 text-center link cursor-pointer @click="onBomai">
            <div mb-2>
              <img src="~/assets/images/enter_bomai.jpg" w-62.5 h-62.5 />
            </div>
            使用爆买商城为企业降本增效
          </div>
        </div> -->
      </div>
    </div>
    <div v-else w-full max-w-150>
      <div flex flex-col items-center mb-8>
        <a-avatar :size="96" :src="company.merchantLogo"></a-avatar>
        <div my-2>
          <span font-size-24px text-white>{{ company.merchantName }}</span>
        </div>
      </div>
      <NuxtLink v-for="(item, index) in groups" :key="index" :to="item.link" @click="handleClick(item)">
        <div menu-item :class="{ 'border-b border-b-[#353541]': index < groups.length - 1 }">
          <div flex items-center>
            <span :class="item.icon" mr-2></span>
            <span>{{ item.name }}</span>
          </div>
          <span class="i-a-right-outlined"></span>
        </div>
      </NuxtLink>
      <NuxtLink @click="showCreateEnterpriseModal">
        <div menu-item mt-25 class="border-b border-b-[#353541]">
          <div flex items-center>
            <span class="i-a-plus-outlined" mr-2></span>
            <span>创建新企业</span>
          </div>
        </div>
      </NuxtLink>

      <!-- <div menu-item @click="onLogout">
        <div flex items-center>
          <span class="i-a-logout-outlined" mr-2></span>
          <span>退出企业</span>
        </div>
      </div> -->
    </div>
    <CreateEnterprise ref="createEnterpriseRef" />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import { getCompany } from '~/api/mall-manage'
import { getShopsByCompanyId, isEnterpriseAdmin } from '~/api/mall-platform'

const router = useRouter()
const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)
const companyStoreObj = companyStore()
const { company } = storeToRefs(companyStoreObj)

// Create a ref to the CreateEnterprise component
const createEnterpriseRef = ref(null)

const isPerSpace = computed(() => {
  return !company.value.shopCompanyId
})

type menuItem = {
  type?: string
  icon?: string
  name?: string
  link?: string
  action?: () => void
}

const bomai = useRuntimeConfig().public.VITE_BOMAI_URL

const { manageBrand } = useManageBrand()

const groups = computed(() => {
  const base: menuItem[] = [
    // {
    //   icon: 'i-a-user-outlined',
    //   name: '成员列表',
    //   link: '/enterpriseCenter/member',
    // },
    // {
    //   icon: 'i-a-audit-outlined',
    //   name: '成员审核',
    //   link: '/enterpriseCenter/audit',
    // },
    // {
    //   icon: 'i-a-user-outlined',
    //   name: '角色',
    //   link: '/enterpriseCenter/role',
    // },
    // {
    //   icon: 'i-a-team-outlined',
    //   name: '用户组',
    //   link: '/enterpriseCenter/userGroup',
    // },
    // {
    //   icon: 'i-a-bank-outlined',
    //   name: '企业信息',
    //   action: async () => {
    //     const companyInfo = await getCompany(company.value.shopCompanyId)
    //     const { status } = companyInfo
    //     if (status == 3) {
    //       message.warn('当前企业正在审核中')
    //       return
    //     }
    //     if (status == 10) {
    //       Modal.confirm({
    //         title: '提示',
    //         content: '企业未通过审核，是否前往修改企业信息',
    //         onOk: async () => {
    //           router.push('/enterpriseCenter/device-join?companyId=' + companyInfo.shopCompanyId)
    //         },
    //       })
    //     } else {
    //       router.push('/enterpriseCenter/enterprise')
    //     }
    //   },
    // },
    // {
    //   icon: 'i-a-reconciliation-outlined',
    //   name: '工商信息',
    //   action: async () => {
    //     const companyInfo = await getCompany(company.value.shopCompanyId)
    //     const { status } = companyInfo
    //     if (status == 3) {
    //       message.warn('当前企业正在审核中')
    //       return
    //     }
    //     if (status == 10) {
    //       Modal.confirm({
    //         title: '提示',
    //         content: '企业未通过审核，是否前往修改企业信息',
    //         onOk: async () => {
    //           router.push('/enterpriseCenter/device-join?companyId=' + companyInfo.shopCompanyId)
    //         },
    //       })
    //     } else {
    //       router.push('/enterpriseCenter/commercial')
    //     }
    //   },
    // },
  ]
  const { type, status } = company.value
  if (type == 2 && status != 2 && status != 20) {
    base.push({
      icon: 'i-a-setting-outlined',
      name: '开通设备商',
      action: () => {
        Modal.confirm({
          title: '提示',
          content: '是否开通设备商？',
          onOk: async () => {
            const isAdmin = await isEnterpriseAdmin(user.value.userMobile, company.value.shopCompanyId)
            if (!isAdmin) {
              message.error('您不是企业管理员，无法成为设备商')
              return
            }
            const companyInfo = await getCompany(company.value.shopCompanyId)
            if (companyInfo.status == 1 && companyInfo.type == 3) {
              message.warn('企业已开通设备商，无需重复开通')
              return
            }

            if (companyInfo.status == 3) {
              message.warn('当前企业正在审核中')
              return
            }

            if (companyInfo.status == 30) {
              Modal.confirm({
                title: '提示',
                content: '您有审核失败的企业，是否前往修改审核信息',
                onOk: async () => {
                  router.push('/enterpriseCenter/device-join?companyId=' + companyInfo.shopCompanyId)
                },
              })
              return
            }

            const [err, res] = await try_http('/mall/p/shopCompany/storage', {
              method: 'POST',
              body: {
                ...companyInfo,
                type: 1,
              },
            })
            if (!err) {
              message.success('设备商开通审核已提交')
            }

            // if (companyInfo.type == 2) {
            // }
          },
        })
      },
    })
  }

  if (type == 1 && status != 2 && status != 10) {
    base.push({
      icon: 'i-a-setting-outlined',
      name: '开通供应商',
      action: () => {
        Modal.confirm({
          title: '提示',
          content: '是否开通供应商？',
          onOk: async () => {
            const isAdmin = await isEnterpriseAdmin(user.value.userMobile, company.value.shopCompanyId)
            if (!isAdmin) {
              message.error('您不是企业管理员，无法开通供应商')
              return
            }
            const companyInfo = await getCompany(company.value.shopCompanyId)
            if (companyInfo.status == 1 && companyInfo.type == 3) {
              message.warn('企业已开通供应商，无需重复开通')
              return
            }
            if (companyInfo.status == 3) {
              message.warn('当前企业正在审核中')
              return
            }
            if (companyInfo.status == 40) {
              Modal.confirm({
                title: '提示',
                content: '您有审核失败的企业，是否前往修改审核信息',
                onOk: async () => {
                  window.open(`${bomai}/brand-join/settle?companyId=${companyInfo.shopCompanyId}&type=edit`, '_blank')
                },
              })
              return
            }

            window.open(`${bomai}/brand-join/settle?companyId=${companyInfo.shopCompanyId}`, '_blank')
          },
        })
      },
    })
  }

  // if (type == 3 || (type == 2 && status != 20)) {
  //   base.push(
  //     {
  //       icon: 'i-a-setting-outlined',
  //       name: '经营信息',
  //       action: async () => {
  //         const companyInfo = await getCompany(company.value.shopCompanyId)
  //         const { status } = companyInfo
  //         if (status == 3) {
  //           message.warn('当前企业正在审核中')
  //           return
  //         }
  //
  //         window.open(`${bomai}/brand-join/operation?companyId=${company.value.shopCompanyId}`, '_blank')
  //       },
  //     },
  //     {
  //       icon: 'i-a-setting-outlined',
  //       name: '财务信息',
  //       action: async () => {
  //         const companyInfo = await getCompany(company.value.shopCompanyId)
  //         const { status } = companyInfo
  //         if (status == 3) {
  //           message.warn('当前企业正在审核中')
  //           return
  //         }
  //         window.open(`${bomai}/brand-join/finance?companyId=${company.value.shopCompanyId}`, '_blank')
  //       },
  //     }
  //   )
  // }

  if (type == 1 || type == 3) {
    base.push({
      icon: 'i-a-user-outlined',
      name: '设备商工作区',
      action: () => {
        window.open(`${bomai}/workSpace`, '_blank')
      },
    })
  }

  if ([2, 3].includes(type)) {
    base.push(
      company.value.creditCode == '91320594MAE5L8PD96'
        ? {
            icon: 'i-a-shop-outlined',
            name: '研选品牌馆',
            link: '/enterpriseCenter/library',
          }
        : {
            icon: 'i-a-shop-outlined',
            name: '供应商管理后台',
            action: async () => {
              manageBrand()
            },
          }
    )
  }

  return base
})

const handleClick = (item: menuItem) => {
  item.action?.()
}

const onBomai = () => {
  window.open('https://bomai.yanxuan.cloud', '_blank')
}

const showCreateEnterpriseModal = () => {
  if (!user.value.userMobile) {
    message.info('请先在 个人中心 - 账号绑定 中绑定手机号')
    return
  }
  if (createEnterpriseRef.value) {
    createEnterpriseRef.value.show()
  }
}

const auth = (url) => {
  if (!user.value.userMobile) {
    message.info('请先在 个人中心 - 账号绑定 中绑定手机号')
    return
  }
  router.push(url)
}
</script>

<style scoped></style>
