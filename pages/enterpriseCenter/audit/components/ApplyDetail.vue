<template>
  <a-modal
    title="审核"
    v-model:open="open"
    @ok="confirm"
    :confirm-loading="loading"
  >
    <a-form :label-col="{ span: 4 }" label-align="right">
      <a-form-item label="审核结果">
        <a-radio-group v-model:value="user.auditResult">
          <a-radio :value="ComResult.pass">通过</a-radio>
          <a-radio :value="ComResult.fail">不通过</a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="部门" v-if="user.auditResult == ComResult.pass">
        <a-tree-select
          placeholder="请选择"
          :tree-data="treeData"
          v-model:value="user.orgId"
          :field-names="{ label: 'name', value: 'id' }"
        ></a-tree-select>
      </a-form-item>

      <a-form-item label="备注">
        <a-textarea
          placeholder="请输入"
          v-model:value="user.remark"
          :maxlength="200"
        ></a-textarea>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ComResult } from '~/api/mall-platform/types'
import { Member } from '~/api/mall-platform/type'
import { cloneDeep } from 'lodash-es'
import { getOrgList } from '~/api/mall-platform'
import { Pagination } from '~/api/mall-platform/type'

const open = ref(false)
const user = ref({} as Member)

const route = useRoute()
const company = computed(() => companyStore().company)
const treeLoading = ref(false)
const treeData = ref<Obj[]>([])
const fetchOrg = async () => {
  treeLoading.value = true
  const res = await getOrgList({
    merchantId: company.value.shopCompanyId,
    size: 9999,
  })
  treeLoading.value = false
  useMall(res, () => {
    const ret = JSON.parse(res.data) as Pagination<any>
    treeData.value = arr_to_tree(ret.records, 'id', 'parentId')
  })
}

const show = async (_user: Member) => {
  user.value = cloneDeep(_user)
  user.value.auditResult = ComResult.pass
  await fetchOrg()
  open.value = true
}

const emits = defineEmits(['ok'])

const loading = ref(false)

const confirm = async () => {
  loading.value = true
  const res = await http.put('/mall-platform/shop/merchantUser', {
    id: user.value.id,
    auditStatus: 1,
    auditResult: user.value.auditResult,
    merchantId: company.value.shopCompanyId,
    remark: user.value.remark,
    userMobile: user.value.userMobile,
  })
  useMall(res, async () => {
    if (user.value.auditResult == ComResult.fail) {
      message.success('审核未通过')
      loading.value = false
      open.value = false
      emits('ok')
    } else {
      const res = await http.put('/mall-platform/shop/userOrg', {
    merchantId: company.value.shopCompanyId,
        orgId: user.value.orgId,
        userId: user.value.id,
      })
      loading.value = false
      useMall(res, () => {
        message.success('审核通过')
        open.value = false
        emits('ok')
      })
    }
  })
}

defineExpose({
  show,
})
</script>
