<template>
  <a-modal title="详情" v-model:open="open" :footer="null">
    <a-form :label-col="{ span: 10 }" label-align="right">
      <a-form-item label="用户名">{{ user.nickName }}</a-form-item>
      <a-form-item label="姓名">{{ user.realName }}</a-form-item>
      <a-form-item label="手机号">{{ user.userMobile }}</a-form-item>
      <a-form-item label="邮箱">{{ user.nickName }}</a-form-item>
      <a-form-item label="身份">{{ user.identity }}</a-form-item>
      <a-form-item label="公司">{{ user.company }}</a-form-item>
      <a-form-item label="职位">{{ user.position }}</a-form-item>
      <a-form-item label="工作证照">
        <img max-w-30 :src="user.workPermit" v-if="user.workPermit" alt="" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import type { Member } from '~/api/member/type'

const user = ref({} as Member)
const open = ref(false)

const show = (_user: Member) => {
  open.value = true
  user.value = _user
}

defineExpose({
  show,
})
</script>
