<template>
  <NuxtLayout name="child-page" title="工商信息">
    <div w-full max-w-150>
      <a-alert v-if="mallStatus == 3" text-center message="您的企业正在审核中，请耐心等待审核结果" type="warning" />
      <a-alert
        v-else-if="mallStatus === 60"
        text-center
        message="您的企业未能通过审核，请在消息中心中查看详情"
        type="error"
        closable
      />
      <commercialForm ref="commercialFormRef" />
      <div v-if="mallStatus !== 3" flex items-center justify-between>
        <a-button flex-1 mr-4 size="large" @click="onReset">重置</a-button>
        <a-button flex-1 type="primary" size="large" @click="onSubmit">提交审核</a-button>
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup>
import commercialForm from './components/commercialForm.vue'
import { companyStore } from '~/store/company'
import { putShopCompany } from '~/api/mall-manage/index'
import { getShopCompanyByShopCompanyId } from '~/api/mall-platform/index'
import { cloneDeep } from 'lodash-es'

const commercialFormRef = ref(null)
const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)
const companyStoreObj = companyStore()
const company = ref({})
const oldCompany = ref({})

const mallStatus = computed(() => {
  // 0: 审核中 1: 正常 2: 审核不通过
  return company.value.status
})

const getInfo = (companyId) => {
  getShopCompanyByShopCompanyId(companyId).then((res) => {
    useMall(res, (data) => {
      if (data) {
        company.value = JSON.parse(data)
        oldCompany.value = JSON.parse(data)
        commercialFormRef.value?.init(2, company.value, mallStatus.value !== 3)
      }
    })
  })
}

const onSubmit = async () => {
  await commercialFormRef.value.formRef.validate()
  const formState = commercialFormRef.value.formState
  const submitData = {
    merchantName: formState.merchantName,
    creditCode: formState.creditCode,
    residence: formState.residence,
    representative: formState.representative,
    capital: formState.capital,
    foundTime: formState.foundTime,
    startTime: formState.startTime,
    endTime: formState.endTime,
    businessScope: formState.businessScope,
    businessLicense: formState.businessLicense,
    identityCardFront: formState.identityCardFront,
    identityCardLater: formState.identityCardLater,
    userMobile: user.value.userMobile,
    shopCompanyId: company.value.shopCompanyId,
    applyType: 6,
  }
  const res = await putShopCompany(submitData)
  if (res.code == 0) {
    getInfo(company.value.shopCompanyId)
    message.success('提交成功，请耐心等待审核')
  }
}

const onReset = () => {
  const data = cloneDeep(oldCompany.value)
  commercialFormRef.value?.init(2, data, mallStatus.value !== 3)
}

const route = useRoute()
onMounted(() => {
  if (route.query.companyId) {
    getInfo(route.query.companyId)
  } else {
    getInfo(companyStoreObj.company.shopCompanyId)
  }
})
</script>
