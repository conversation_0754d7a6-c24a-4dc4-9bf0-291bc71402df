<template>
  <NuxtLayout name="child-page" title="企业产品库" size="large">
    <div w-full max-w-360>
      <a-button v-if="useAuth('sale:mall:add')" type="primary" @click="onOpenStore">创建品牌</a-button>
      <a-tabs v-model:activeKey="activeKey" @change="onChangeTab">
        <a-tab-pane key="9" tab="全部商城" />
        <!-- <a-tab-pane key="-1" tab="已删除" /> -->
        <!-- <a-tab-pane key="0" tab="停业中" /> -->
        <!-- <a-tab-pane key="6" tab="审核失败" /> -->
        <a-tab-pane key="1" tab="运营中" />
        <a-tab-pane key="5" tab="开店审核中" />
        <a-tab-pane key="3" tab="上线审核中" />
        <a-tab-pane key="4" tab="填写中" />
        <a-tab-pane key="2" tab="已下架" />
        <template #rightExtra>
          <a-input-search
            v-model:value="searchKey"
            placeholder="搜索商城名称"
            allowClear
            enter-button
            @search="onSearch"
          />
        </template>
      </a-tabs>
      <div grid grid-cols-5 gap="x-5 y-5" mt-5>
        <div
          v-for="(item, index) in mallList"
          :key="index"
          flex-center
          px-4
          text-14px
          overflow-hidden
          flex
          flex-col
          h-72
          color-primaryText
          bg-primaryBg
          relative
        >
          <div w-25 h-25>
            <img w-full h-full object-scale-down :src="useImage(convertImage(item.shopLogo))" alt="" />
          </div>

          <template v-if="item.shopStatus == 4">
            <div text-4 p-2 class="shop-name" :title="item.shopName">商城信息填写中</div>
          </template>
          <template v-else>
            <div text-4 p-3 font-bold truncate class="shop-name" :title="item.shopName">{{ item.shopName }}</div>
            <div text-12px h-8 line-clamp-2 text-gray class="intro" :title="item.intro">{{ item.intro }}</div>
          </template>

          <div mt-6 v-if="item.shopStatus == 1">
            <a-button type="primary" min-w-25 flex-1 mr-4 @click="onView(item)">查看</a-button>
            <a-button type="primary" min-w-25 flex-1 @click="onManage(item)">管理</a-button>
          </div>
          <div mt-6 v-if="item.shopStatus == 2 || item.shopStatus == 3">
            <a-button type="primary" min-w-25 flex-1 @click="onManage(item)">管理</a-button>
          </div>
          <div mt-6 v-if="item.shopStatus == 4">
            <a-button type="primary" min-w-25 style="width: 100%" @click="onComplete(item)">继续填写</a-button>
          </div>

          <div v-if="item.shopStatus == 5 || item.shopStatus == 3" class="in-approve">审核中</div>
          <div v-if="item.shopStatus == 2" class="in-approve">已下架</div>
        </div>
      </div>

      <div v-if="!mallList.length" pt-100 style="text-align: center">
        <!-- <img h-181px src="~/assets/images/daodao_nodata.png" /> -->
        <span text-18px color-primary font-bold>什么也没有...</span>
      </div>

      <a-pagination
        v-if="mallList.length"
        text-right
        mt-16
        v-model:current="page.current"
        v-model:page-size="page.size"
        show-size-changer
        show-quick-jumper
        :total="page.total"
        @change="handleChange"
      />
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
import { getShopsByCompanyId } from '~/api/mall-platform/index'
import { Pagination } from '~/api/shop/type'
import { userStore } from '~/store/user'

const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)
const bomai = useRuntimeConfig().public.VITE_BOMAI_URL
const route = useRoute()

const company = computed(() => companyStore().company)

const searchKey = ref('')
const activeKey = ref('9')
const page = reactive({
  current: 1,
  size: 10,
  total: 0,
})

const getRootUrl = (toPort) => {
  const { origin, port } = window.location
  if (port && process.env.NODE_ENV == 'development') {
    return origin.replace(port, toPort) + '/vendor'
  }
  return useRuntimeConfig().public.VITE_MALL_MANAGE_URL
}

const mallList = ref<any>([])

const onOpenStore = () => {
  const _list = mallList.value.filter((d) => d.shopStatus == 4 && d.shopAdminPhone == user.value.userMobile)
  if (_list.length) {
    Modal.confirm({
      title: '提示',
      content: '您在当前企业中尚有未完成信息填写的商城,是否继续填写?',
      okText: '去填写',
      onOk() {
        onManage(_list[0])
      },
      onCancel() {
        onRegister()
      },
    })
  } else {
    onRegister()
  }
}

const onRegister = () => {
  const url = getRootUrl('9527')
  window.open(`${url}?type=openstore&phone=${user.value.userMobile}&companyId=${company.value.shopCompanyId}`)
}

const onView = (t) => {
  window.open(`${bomai}/brand/${t.shopId}`)
}

const onManage = async (t) => {
  const url = getRootUrl('9527')
  const [err, res] = await try_http('/mall/p/shop-employee/login-check', {
    params: {
      shopId: t.shopId,
    },
  })
  if (!err) {
    if (res.data == 1) {
      window.open(
        `${url}/login?type=signstore&phone=${user.value.userMobile}&shopId=${t.shopId}&companyId=${company.value.shopCompanyId}`,
      )
    } else {
      switch (res.data) {
        case 0:
          return message.error('请联系企业管理员为您开通权限')
        case 2:
          return message.error('用户被禁用登录')
        case 3:
          return message.error('企业信息有误')
        case 4:
          return message.error('店铺未找到')
      }
    }
  }
}

const onComplete = (t) => {
  const url = getRootUrl('9527')
  window.open(
    `${url}/login?type=signstore&phone=${user.value.userMobile}&shopId=${t.shopId}&companyId=${company.value.shopCompanyId}`,
  )
  // window.open(`http://192.168.0.17:9527/manage?type=signstore&phone=${user.value.userMobile}&shopId=${t.shopId}`)
}

const onChangeTab = () => {
  page.current = 1
  getMallList()
}

const handleChange = () => {
  getMallList()
}

const onSearch = () => {
  page.current = 1
  getMallList()
}

const getMallList = () => {
  getShopsByCompanyId({
    merchantId: company.value.shopCompanyId,
    shopName: searchKey.value,
    shopStatus: activeKey.value == '9' ? '' : activeKey.value,
    current: page.current,
    size: page.size,
  }).then((res) => {
    useMall(res, (ret: any) => {
      const response = JSON.parse(ret)
      mallList.value = response.records
      page.total = response.total

      if (!response.records.length && page.current > 1) {
        page.current = 1
        getMallList()
      }
    })
  })
}

const convertImage = (img?: string) => {
  if (!img) return
  if (img.startsWith('http')) return img
  return `https://obs-bbc.obs.cn-east-3.myhuaweicloud.com/${img}`
}

onMounted(() => {
  getMallList()
})
</script>

<style lang="less" scoped>
.in-approve {
  color: #c95454;
  border: 4px solid #c95454;
  border-radius: 10px;
  width: 150px;
  height: 60px;
  text-align: center;
  padding: 8px 0;
  font-size: 32px;
  font-weight: bold;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -30px;
  margin-left: -90px;
  transform: rotate(-45deg);
  z-index: 0;
}
</style>
