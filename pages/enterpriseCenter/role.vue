<template>
  <NuxtLayout name="child-page" title="角色" size="large">
    <div w-full max-w-360 class="h-[calc(100vh-200px)]" flex flex-col bg-primaryBgTransparent p-4>
      <a-form :label-col="{ flex: '80px' }">
        <div grid grid-cols-3 gap-x-4>
          <a-form-item label="角色名称">
            <a-input v-model:value="searchForm.roleName" placeholder="请输入" allowClear />
          </a-form-item>

          <!-- <a-form-item label="状态">
          <a-select
            placeholder="请选择"
            v-model:value="searchForm.status"
            w-180
            allowClear
          >
            <a-select-option :value="1">启用</a-select-option>
            <a-select-option :value="0">禁用</a-select-option>
          </a-select>
        </a-form-item> -->

          <a-form-item>
            <a-space>
              <a-button type="primary" @click="search">搜索</a-button>
              <a-button @click="reset">重置</a-button>
            </a-space>
          </a-form-item>
        </div>
      </a-form>

      <div>
        <a-button v-if="useAuth('auth:role:add')" type="primary" @click="onAdd">新建</a-button>
      </div>

      <!-- 列表 -->
      <div py-2>
        <a-table
          style="width: 100%"
          :columns="columns"
          row-key="id"
          size="middle"
          :data-source="dataSource"
          :pagination="pagination"
          @change="handleChanger"
        >
          <template #bodyCell="{ column, record, value }">
            <template v-if="column.dataIndex == 'action'">
              <a-button v-if="useAuth('auth:role:edit')" type="link" @click="onEdit(record.roleId)">编辑</a-button>

              <a-popconfirm v-if="useAuth('auth:role:del')" title="确认删除?" @confirm="onDel(record.roleId)">
                <a-button type="link">删除</a-button>
              </a-popconfirm>
            </template>
          </template>
        </a-table>
      </div>
    </div>
    <a-modal
      :width="800"
      :title="modalTitle"
      v-model:open="visible"
      @ok="confirm"
      :ok-button-props="{
        disabled: false,
      }"
    >
      <a-form mt-4 :label-col="{ flex: '80px' }" ref="formRef" :model="dataForm" :rules="rules">
        <div grid grid-cols-2 gap-x-4>
          <a-form-item label="角色名称" name="roleName">
            <a-input v-model:value="dataForm.roleName" placeholder="请输入" allowClear :maxlength="10" show-count />
          </a-form-item>

          <!-- <a-form-item label="状态">
            <a-select
              placeholder="请选择"
              v-model:value="dataForm.status"
              w-180
              allowClear
            >
              <a-select-option :value="1">启用</a-select-option>
              <a-select-option :value="0">禁用</a-select-option>
            </a-select>
          </a-form-item> -->
        </div>

        <a-form-item label="备注" name="remark">
          <a-input v-model:value="dataForm.remark" placeholder="请输入" allowClear :maxlength="100" show-count />
        </a-form-item>

        <a-divider orientation="left">权限信息</a-divider>

        <a-tree
          checkable
          :tree-data="menuTree"
          :fieldNames="{
            children: 'list',
            title: 'name',
            key: 'menuId',
          }"
          v-model:checkedKeys="checkedKeys"
          @check="onCheck"
        />
      </a-form>
    </a-modal>
  </NuxtLayout>
</template>

<script setup>
import { getRole, addRole, editRole, delRole } from '~/api/mall-platform/index'
import { companyStore } from '~/store/company'

const useCompany = companyStore()
const pagination = ref({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showTotal: (total) => {
    return `共${total}条数据`
  }
})
const rules = {
  roleName: [
    { required: true, message: '请输入角色名称', trigger: 'blur', whitespace: true }
  ]
}
const searchForm = ref({})
const search = () => {
  pagination.value.current = 1
  getList()
}

const reset = () => {
  searchForm.value = {}
}

const handleChanger = (obj) => {
  pagination.value = obj
  getList()
}

const columns = [
  {
    title: '角色名称',
    dataIndex: 'roleName',
    width: 200,
    fixed: 'left',
    ellipsis: true
  },
  // {
  //   title: '状态',
  //   dataIndex: 'status',
  //   width: 100,
  //   ellipsis: true
  // },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 300,
    ellipsis: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 100,
    ellipsis: true
  }
]

const dataSource = ref([])
const getList = () => {
  const merchantId = useCompany.company.shopCompanyId
  if (!merchantId) return
  http('/mall/p/sys/enterpriseRole/page', {
    method: 'get',
    params: {
      enterpriseId: merchantId,
      current: pagination.value.current,
      size: pagination.value.pageSize,
      ...searchForm.value
    }
  }).then(res => {
    useMall(res, (ret) => {
      dataSource.value = ret.records
      pagination.value.total = ret.total

      if (!dataSource.value.length && pagination.value.current > 1) {
        pagination.value.current = 1
        getList()
      }
    })
  })
}

const menuTree = ref([])
const checkedKeys = ref([])
const getMenuList = () => {
  const merchantId = useCompany.company.shopCompanyId
  if (!merchantId) return
  http(`/mall/p/sys/enterpriseMenu/getEnterpriseMenuTree/${merchantId}`, {
    method: 'get',
    params: {}
  }).then(res => {
    useMall(res, (ret) => {
      menuTree.value = ret
      setMenuList(ret)
    })
  })
}

const menuList = ref([])
const setMenuList = (menu) => {
  menu.forEach(d => {
    menuList.value.push(d)
    if (d.list && d.list.length) {
      setMenuList(d.list)
    }
  })
}

// 所有菜单父子id映射表
const menuIds = computed(() => {
  const ret = {}
  menuList.value.forEach(d => {
    if (ret[d.parentId]) {
      ret[d.parentId].push(d.menuId)
    } else {
      ret[d.parentId] = [d.menuId]
    }
  })
  return ret
})

const modalTitle = ref('新建角色')
const visible = ref(false)
const onAdd = () => {
  visible.value = true
  modalTitle.value = '新建角色'
  nextTick(() => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    checkedKeys.value = []
    dataForm.value = {}
  })
}

const onEdit = (roleId) => {
  visible.value = true
  modalTitle.value = '编辑角色'
  http(`/mall/p/sys/enterpriseRole/info/${roleId}`, {
    method: 'get',
    params: {}
  }).then(res => {
    useMall(res, (ret) => {
      dataForm.value = {
        ...ret
      }
      checkedKeys.value = ret.menuIdList.filter(d => !menuIds.value[d])
    })
  })
}

const onDel = (id) => {
  http('/mall/p/sys/enterpriseRole', {
    method: 'delete',
    body: [id]
  }).then(res => {
    useMall(res, (ret) => {
      getList()
    })
  })
}

const onCheck = (checks, e) => {
  console.log(getAllKeys(checks))
  // const { checkedNodes } = e
  // console.log(checkedNodes)
  // const arr = new Set()
  // checkedNodes.forEach(d => {
  //   if (d.parentId) {
  //     arr.add(d.parentId)
  //   }
  //   arr.add(d.menuId)
  // })
  // checkedKeys.value = Array.from(arr)
}

const dataForm = ref({})
const formRef = ref(null)
const confirm = () => {
  formRef.value.validate().then(() => {
    http('/mall/p/sys/enterpriseRole', {
      method: dataForm.value.roleId ? 'put' : 'post',
      body: {
        ...dataForm.value,
        menuIdList: getAllKeys(checkedKeys.value),
        enterpriseId: useCompany.company.shopCompanyId
      }
    }).then(res => {
      useMall(res, (ret) => {
        visible.value = false
        getList()
      })
    })
  })
}

const getAllKeys = (keys) => {
  const set = new Set()
  const _keys = keys.filter(d => !menuIds.value[d])
  let parentIds = []
  const getParent = (d) => {
    for (let k in menuIds.value) {
      if (menuIds.value[k].includes(Number(d))) {
        if (k != 0) {
          parentIds.push(Number(k))
          getParent(k)
        }
      }
    }
  }
  _keys.forEach(d => {
    parentIds = []
    getParent(d)
    parentIds.forEach(_d => set.add(_d))
    set.add(d)
  })
  return Array.from(set)
}

onMounted(() => {
  getList()
  getMenuList()
})
</script>