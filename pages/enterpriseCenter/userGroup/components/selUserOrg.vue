<template>
  <a-modal
    :width="800"
    title="选择用户"
    :maskClosable="false"
    v-model:open="open"
    @ok="confirm"
  >
    <div flex min-h-125>
      <div flex-1 border-r border="textBg" pt-2>
        <div mb-2 pl-2 border-b border="textBg"><b>组织架构</b></div>
        <a-tree
          v-if="orgTree && orgTree.length"
          default-expand-all
          :tree-data="orgTree"
          :field-names="{ key: 'id' }"
          checkable
          checkStrictly
          block-node
          v-model:checkedKeys="selOrgs"
          v-model:selectedKeys="selectedKeys"
          @check="onSelOrg"
          @select="getMember"
        >
          <template #title="{ data }">
            <div flex>
              <div truncate flex-1 :title="data.name">{{ data.name }}</div>
            </div>
          </template>
        </a-tree>
      </div>

      <div flex-1 border-r border="textBg" pt-2>
        <div flex justify-between mb-2 pl-2 border-b border="textBg">
          <b>可选项</b> 
          <a-checkbox
            v-model:checked="checkAll"
            @change="onCheckAllChange"
          >
            全选
          </a-checkbox>
        </div>
        <div px-2>
          <a-input 
            v-model:value="searchKey"
            placeholder="请输入名称回车查询"
            @pressEnter="getMember"
          />
          <a-checkbox-group 
            v-model:value="selMems"
            :options="memberList"
            @change="onSelMem"
            style="display: grid;"
          />
          <hm-empty v-if="!memberList.length" />
        </div>
      </div>

      <div flex flex-col flex-1 pt-2>
        <div mb-2 pl-2 border-b border="textBg">
          <b>已选项</b>
        </div>
        <div mb-2 pl-2 border-b border="textBg">
          <b>已选节点: {{ selOrg.length }}</b>
        </div>
        <div flex-wrap flex-1 p-2s>
          <a-tag
            mb-2
            v-for="(item, index) in selOrg"
            :key="item.id"
            closable
            @close="onDelOrg(index)"
          >
            {{ item.name }}
          </a-tag>
          <hm-empty v-if="!selOrg.length" />
        </div>
        <div mb-2 pl-2 border-b border="textBg">
          <b>已选条目: {{ selMem.length }}</b>
        </div>
        <div flex-wrap flex-1 p-2>
          <a-tag 
            mb-2
            v-for="(item, index) in selMem"
            :key="item.id"
            closable
            @close="onDelMem(index)"
          >
            {{ item.name }}
          </a-tag>
          <hm-empty v-if="!selMem.length" />
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { getMemberList, getOrgList } from '~/api/mall-platform'
import { companyStore } from '~/store/company'

const emits = defineEmits(['ok'])
const companyStoreObj = companyStore()
const { company } = storeToRefs(companyStoreObj)

const merchantId = company.value.shopCompanyId as string

const selOrgs = ref({})
const selOrg = ref([])
const onSelOrg = (checkedKeys, e) => {
  const { checked, node } = e
  if (checked) {
    selOrg.value.push(node)
  } else {
    const index = selOrg.value.findIndex(d => d.id == node.id)
    selOrg.value.splice(index, 1)
  }
}

const onDelOrg = (index) => {
  selOrg.value.splice(index, 1)
  selOrgs.value = {
    checked: selOrg.value.map(d => d.key),
    halfChecked: []
  }
}

const checkAll = ref(false)
const onCheckAllChange = (e) => {
  memberList.value.filter(d => !d.disabled).forEach(d => {
    const index = selMems.value.findIndex(_d => _d == d.value)
    const _index = selMem.value.findIndex(_d => _d.id == d.value)
    if (e.target.checked) {
      index == -1 && selMems.value.push(d.value)
      _index == -1 && selMem.value.push({
        id: d.userId,
        name: d.realName
      })
    } else {
      index > -1 && selMems.value.splice(index, 1)
      _index > -1 && selMem.value.splice(index, 1)
    }
  })
}

const selMems = ref([])
const selMem = ref([])
const onSelMem = (node) => {
  memberList.value.filter(d => !d.disabled).forEach(d => {
    const index = selMems.value.findIndex(_d => _d == d.value)
    const _index = selMem.value.findIndex(_d => _d.id == d.value)
    if (node.includes(d.userId)) {
      index == -1 && selMems.value.push(d.value)
      _index == -1 && selMem.value.push({
        id: d.userId,
        name: d.realName
      })
    } else {
      index > -1 && selMems.value.splice(index, 1)
      _index > -1 && selMem.value.splice(index, 1)
    }
  })
  checkIsSelAll()
}

const onDelMem = (index) => {
  selMem.value.splice(index, 1)
  selMems.value = selMem.value.map(d => d.id)
  checkIsSelAll()
}

const checkIsSelAll = () => {
  let isAll = true
  const list = memberList.value.filter(d => !d.disabled)
  list.forEach(d => {
    if (!selMems.value.includes(d.userId)) {
      isAll = false
    }
  })
  checkAll.value = (list.length && isAll)
}

const orgTree = ref([])
const selOrgId = ref(null)
const selectedKeys = ref([])
const getOrg = async () => {
  const res = await getOrgList({
    merchantId,
    size: 9999,
  })
  useMall(res, () => {
    const ret = JSON.parse(res.data)
    orgTree.value = arr_to_tree(ret.records.map(d => {
      d.disabled = disableOrg.value.includes(d.id.toString())
      return d
    }), 'id', 'parentId')
    selOrgId.value = orgTree.value[0].id
    selectedKeys.value = [selOrgId.value]
    getMember()
  })
}

const searchKey = ref('')
const memberList = ref([])
const getMember = (e) => {
  selOrgId.value = e ? e[0] : selOrgId.value
  getMemberList({
    realName: searchKey.value,
    orgId: selOrgId.value,
    merchantId,
    auditStatus: 1,
    auditResult: 1,
  }).then(res => {
    useMall(res, () => {
      const ret = JSON.parse(res.data)
      memberList.value = ret.records.map(d => {
        d.label = d.realName
        d.value = d.userId
        d.disabled = disableMem.value.includes(d.userId)
        return d
      })
      checkIsSelAll()
    })
  })
}

const open = ref(false)
const disableOrg = ref([])
const disableMem = ref([])
const init = ({ orgs, mems }) => {
  disableOrg.value = orgs
  disableMem.value = mems
  open.value = true
  searchKey.value = ''
  selMem.value = []
  selMems.value = []
  selOrg.value = []
  selOrgs.value = {}
  selOrgId.value = null
  getOrg()
}

const confirm = () => {
  const data = []
  selOrg.value.forEach(d => {
    data.push({
      isOrg: 1,
      unionId: d.id
    })
  })
  selMem.value.forEach(d => {
    data.push({
      isOrg: 0,
      unionId: d.id
    })
  })  
  if (!data.length) {
    message.warning('请选择成员')
    return
  }
  open.value = false
  emits('ok', data)
}

defineExpose({
  init
})
</script>

<style lang="less" scoped>

</style>
