<template>
  <NuxtLayout name="child-page" title="用户组" size="large">
    <div w-full max-w-360 class="h-[calc(100vh-200px)]" flex bg-primaryBgTransparent>
      <div w-50 h-full border-r border="textBg" py-4 px-2>
        <a-tree
          v-if="groupTree && groupTree.length"
          :field-names="{ key: 'id' }"
          :tree-data="groupTree"
          default-expand-all
          v-model:selected-keys="selGroup"
          @select="getMemberAndRole"
          block-node
        >
          <template #title="{ data }">
            <div flex>
              <div truncate flex-1 :title="data.name">{{ data.name }}</div>
              <a-dropdown>
                <MoreOutlined />
                <template #overlay>
                  <a-menu>
                    <a-menu-item v-if="useAuth('auth:group:addGroup')" @click="onAdd(data)">
                      <PlusOutlined />
                      创建子用户组
                    </a-menu-item>

                    <a-menu-item v-if="useAuth('auth:group:editGroup') && !data.isRoot" @click="onEdit(data)">
                      <EditOutlined />
                      编辑
                    </a-menu-item>

                    <a-menu-item v-if="useAuth('auth:group:delGroup') && !data.isRoot" @click="onDel(data)">
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </template>
        </a-tree>
      </div>

      <div v-if="selGroup.length" flex-1 p-4 overflow-x-hidden>
        <a-divider orientation="left">成员</a-divider>
        <div v-if="!isSelRoot" flex flex-wrap px-4>
          <a-tag
            mb-2
            v-for="(item, index) in memberList"
            :key="item.id"
            :closable="useAuth('auth:group:delMem')"
            @close="onDelMember(item.id)"
          >
            <template #icon v-if="item.isOrg == 1">
              <FolderOpenOutlined />
            </template>
            {{ item.name }}
          </a-tag>
          <a-button v-if="useAuth('auth:group:addMem')" type="primary" size="small" @click="onAddMember">
            <template #icon>
              <PlusOutlined />
            </template>
          </a-button>
        </div>

        <a-divider orientation="left">角色</a-divider>
        <div v-if="!isSelRoot" flex flex-wrap px-4>
          <a-tag
            mb-2
            v-for="(item, index) in roleList"
            :key="item.id"
            :closable="useAuth('auth:group:delRole')"
            @close="onDelRole(item.id)"
          >
            {{ item.name }}
          </a-tag>
          <a-button v-if="useAuth('auth:group:addRole')" type="primary" size="small" @click="onAddRole">
            <template #icon>
              <PlusOutlined />
            </template>
          </a-button>
        </div>
      </div>
    </div>
    <a-modal :width="500" :title="modalTitle" :maskClosable="false" v-model:open="open" @ok="confirm">
      <a-form mt-5 :label-col="{ flex: '100px' }" ref="formRef" :model="dataForm" :rules="rules">
        <a-form-item label="用户组名称" name="name">
          <a-input v-model:value="dataForm.name" placeholder="请输入" allowClear :maxlength="30" show-count />
        </a-form-item>
      </a-form>
    </a-modal>

    <a-modal :width="500" title="添加角色" :maskClosable="false" v-model:open="openRole" @ok="confirmRole">
      <a-form mt-5 :label-col="{ flex: '80px' }" ref="roleFormRef" :model="roleDataForm" :rules="rules">
        <a-form-item label="角色" name="roleIds">
          <a-select v-model:value="roleDataForm.roleIds" placeholder="请选择" mode="multiple" allowClear>
            <a-select-option
              v-for="item in roleOption.filter((d) => roleList.findIndex((t) => t.roleId == d.roleId) == -1)"
              :key="item.roleId"
              :value="item.roleId"
            >
              {{ item.roleName }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <userOrg ref="userOrgRef" @ok="onUserOk" />
  </NuxtLayout>
</template>

<script setup>
import { companyStore } from '~/store/company'
import userOrg from './components/selUserOrg.vue'

const useCompany = companyStore()
const rules = {
  name: [{ required: true, message: '请输入用户组名称', trigger: 'blur', whitespace: true }],
  roleIds: [{ required: true, message: '请选择角色', trigger: 'change' }],
}

const modalTitle = ref('新增用户组')
const open = ref(false)
const onAdd = (data) => {
  open.value = true
  modalTitle.value = '新增用户组'
  nextTick(() => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    dataForm.value = {
      parentId: data.id,
    }
  })
}

const onEdit = (data) => {
  open.value = true
  modalTitle.value = '编辑用户组'
  dataForm.value = {
    ...data,
  }
}

const onDel = async ({ id }) => {
  const [err, res] = await try_http(`/mall/p/sys/enterpriseUserGroup/${id}`, {
    method: 'delete',
  })
  if (err) return
  if (res.success) {
    if (selGroup.value[0] == id) {
      selGroup.value = []
    }
    getGroupList()
  }
}

const dataForm = ref({})
const formRef = ref(null)
const confirm = async () => {
  await formRef.value.validate()
  const [err, res] = await try_http('/mall/p/sys/enterpriseUserGroup', {
    method: dataForm.value.id ? 'put' : 'post',
    body: {
      ...dataForm.value,
      enterpriseId: useCompany.company.shopCompanyId,
    },
  })
  if (err) return
  if (res.success) {
    open.value = false
    getGroupList()
  }
}

const groupTree = ref([])
const expandedKeys = ref([])
const selGroup = ref([])
const getGroupList = async () => {
  const merchantId = useCompany.company.shopCompanyId
  if (!merchantId) return
  const [err, res] = await try_http(`/mall/p/sys/enterpriseUserGroup/list/${merchantId}`, {
    method: 'get',
    params: {},
  })
  if (err) return
  if (res.success) {
    groupTree.value = res.data
    if (res.data.length && !selGroup.value.length) {
      selGroup.value = [res.data[0].id]
    }
    getMemberAndRole()
  }
}

const isSelRoot = computed(() => {
  return selGroup.value[0] == groupTree.value[0]?.id
})

const memberList = ref([])
const roleList = ref([])
const getMemberAndRole = () => {
  if (!selGroup.value.length) return
  onGetGroupRole()
  onGetGroupMem()
}

// 成员 部门
const userOrgRef = ref(null)
const onAddMember = () => {
  console.log(memberList.value)
  userOrgRef.value?.init({
    orgs: memberList.value.filter((d) => d.isOrg == 1).map((d) => d.unionId),
    mems: memberList.value.filter((d) => d.isOrg == 0).map((d) => d.unionId),
  })
}

const onUserOk = async (data) => {
  const postData = data.map((d) => {
    d.userGroupId = selGroup.value[0]
    return d
  })
  const [err, res] = await try_http('/mall/p/sys/enterpriseUserGroupMember', {
    method: 'post',
    body: postData,
  })
  if (err) return
  if (res.success) {
    onGetGroupMem()
  }
}

const onDelMember = async (id) => {
  const [err, res] = await try_http(`/mall/p/sys/enterpriseUserGroupMember`, {
    method: 'delete',
    params: {
      id,
    },
  })
  if (err) return
  if (res.success) {
    onGetGroupMem()
  }
}

const onGetGroupMem = async () => {
  const [err, res] = await try_http(`/mall/p/sys/enterpriseUserGroupMember/list`, {
    method: 'get',
    params: {
      groupId: selGroup.value[0],
    },
  })
  if (err) return
  if (res.success) {
    memberList.value = res.data
  }
}
// 成员 部门

// 角色
const roleOption = ref([])
const getRoleList = async () => {
  const merchantId = useCompany.company.shopCompanyId
  if (!merchantId) return
  const [err, res] = await try_http(`/mall/p/sys/enterpriseRole/list`, {
    method: 'get',
    params: {
      enterpriseId: merchantId,
    },
  })
  if (err) return
  if (res.success) {
    roleOption.value = res.data
  }
}

const openRole = ref(false)
const onAddRole = () => {
  openRole.value = true
  nextTick(() => {
    roleFormRef.value.resetFields()
    roleFormRef.value.clearValidate()
  })
}

const roleDataForm = ref({})
const roleFormRef = ref(null)
const confirmRole = async () => {
  await roleFormRef.value.validate()
  const [err, res] = await try_http('/mall/p/sys/enterpriseUserGroupRole', {
    method: 'post',
    body: {
      groupId: selGroup.value[0],
      roleIds: roleDataForm.value.roleIds,
    },
  })
  if (err) return
  if (res.success) {
    openRole.value = false
    onGetGroupRole()
  }
}

const onDelRole = async (id) => {
  const [err, res] = await try_http(`/mall/p/sys/enterpriseUserGroupRole`, {
    method: 'delete',
    body: [id],
  })
  if (err) return
  if (res.success) {
    onGetGroupRole()
  }
}

const onGetGroupRole = async () => {
  const [err, res] = await try_http(`/mall/p/sys/enterpriseUserGroupRole/list`, {
    method: 'get',
    params: {
      groupId: selGroup.value[0],
    },
  })
  if (err) return
  if (res.success) {
    roleList.value = res.data
  }
}
// 角色

onMounted(async () => {
  getGroupList()
  getRoleList()
})
</script>

<style lang="less" scoped>
:deep(.ant-tree-treenode) {
  padding: 0 !important;
  .ant-tree-node-content-wrapper {
    padding-right: 12px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow-x: hidden;
    background: none;
  }
}
:deep(.ant-tree-treenode-selected) {
  @apply bg-primary! text-white!;
}
</style>
