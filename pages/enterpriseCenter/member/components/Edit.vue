<template>
  <a-modal title="编辑" v-model:open="open" @ok="confirm">
    <a-form :label-col="{ span: 4 }">
      <a-form-item label="姓名">{{ user.realName }}</a-form-item>

      <a-form-item label="部门">
        <a-tree-select
          v-model:value="user.orgId"
          :tree-data="treeData"
          :field-names="{ label: 'name', value: 'id' }"
          :getPopupContainer="(el) => el.parentNode"
        ></a-tree-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { Member } from '~/api/member/type'
import { cloneDeep } from 'lodash-es'
import { getOrgList, saveUserOrg } from '~/api/mall-platform'
import { Pagination } from '~/api/shop/type'

const open = ref(false)

const user = ref({} as Member)

const show = async (_user: Member) => {
  user.value = cloneDeep(_user)
  await fetchOrg()
  open.value = true
}
const companyStoreObj = companyStore()
const { company } = storeToRefs(companyStoreObj)

const merchantId = company.value.shopCompanyId as string

const emits = defineEmits(['ok'])
const confirm = async () => {
  const res = await saveUserOrg({
    orgId: user.value.orgId,
    orgUserId: user.value.userId,
    merchantId,
  })
  useMall(res, () => {
    message.success('更新成功')
    open.value = false
    emits('ok')
  })
}

const treeLoading = ref(false)
const treeData = ref<Obj[]>([])
const fetchOrg = async () => {
  treeLoading.value = true
  const res = await getOrgList({
    merchantId,
    size: 9999,
  })
  treeLoading.value = false
  useMall(res, () => {
    const ret = JSON.parse(res.data) as Pagination<any>
    treeData.value = arr_to_tree(ret.records, 'id', 'parentId')
  })
}

defineExpose({
  show,
})
</script>
