<template>
  <NuxtLayout name="child-page" title="创建企业">
    <div w-full max-w-150>
      <div v-show="step === 0">
        <commercialForm ref="commercialFormRef" />
      </div>

      <div v-show="step === 1">
        <enterpriseForm ref="enterpriseFormRef" />
      </div>
      <div my-4>
        <a-checkbox v-model:checked="checked">
          我已阅读并同意
          <span button-link @click="onOpenRegistration">《研选工场企业入驻协议》</span>
        </a-checkbox>
      </div>
      <div flex items-center justify-between>
        <a-button flex-1 mr-4 size="large" :disabled="step === 0" @click="onPrevious">上一步</a-button>
        <a-button flex-1 type="primary" size="large" :disabled="step === 1 && !checked" @click="onNext">
          {{ step === 0 ? '下一步' : '完成' }}
        </a-button>
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup>
import {} from 'vue'
import { saveMember } from '~/api/mall-manage/index'
import commercialForm from './components/commercialForm.vue'
import enterpriseForm from './components/enterpriseForm.vue'
const store = userStore()

const commercialFormRef = ref(null)
const enterpriseFormRef = ref(null)
const user = computed(() => store.user)

const submitData = ref({
  type: 1,
  userMobile: user.value.userMobile,
})

const step = ref(0) // 0 - 工商信息，1 - 企业信息
const checked = ref(false)
const onNext = async () => {
  if (step.value === 0) {
    try {
      await commercialFormRef.value.formRef.validate()
      // 验证通过，进入下一步
      submitData.value = {
        ...submitData.value,
        ...commercialFormRef.value.formState,
      }
      step.value++
      // 初始化企业表单
    } catch (error) {
      // 验证失败，不做处理
    }
  } else {
    try {
      await enterpriseFormRef.value.formRef.validate()
      // 验证通过，提交表单
      submitData.value = {
        ...submitData.value,
        ...enterpriseFormRef.value.formState,
      }
      onSubmit()
    } catch (error) {
      // 验证失败，不做处理
    }
  }
}

const onPrevious = () => {
  step.value--
}

const onOpenRegistration = () => {
  const currentDomain = window.location.origin // 获取当前域名
  const newPath = '/agreement?key=registration' // 新的路径
  const newLink = `${currentDomain}${newPath}` // 拼接新的完整链接
  window.open(newLink, '_blank')
}

const router = useRouter()
const onSubmit = () => {
  saveMember(submitData.value).then((res) => {
    if (res.code === '00000') {
      message.success('申请已提交,请耐心等待审核')
      router.push('/userCenter')
    }
  })
}

const scrollToTop = () => {
  const container = document.getElementById('layout-default')
  container.scrollTo(0, 0)
}

onMounted(() => {
  commercialFormRef.value.init(1, null, true)
  enterpriseFormRef.value.init(1, null, true)
  nextTick(() => {
    scrollToTop()
  })
})

watch(step, () => {
  nextTick(() => {
    scrollToTop()
  })
})
</script>

<style scoped></style>
