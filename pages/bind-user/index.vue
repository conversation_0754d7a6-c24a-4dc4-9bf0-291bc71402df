<template>
  <div class="w-full h-full flex-center">
    <left-logo></left-logo>
    <div class="bg-#2A2A35 w-full max-w-566px p-3">
      <Back class="text-7" />
      <div class="text-center">
        <img h-15 src="~/assets/images/logo_rect_white.png" />
      </div>
      <div class="mt-10 px-10">
        <a-form class="flex-1" ref="formRef" :rules="rules" :model="form" @keyup.enter="bindUser">
          <div class="text-white mb-3">绑定手机号</div>
          <a-form-item mt-3 name="mobile">
            <a-input placeholder="请输入手机号" v-model:value="form.mobile"></a-input>
          </a-form-item>
          <a-form-item name="smsCode">
            <msg-input :api="bindMsg" msg-key="bind" v-model:value="form.smsCode"></msg-input>
          </a-form-item>
          <a-button :disabled="!uid" type="primary" my-5 :loading="loading" w-full @click="bindUser">绑定</a-button>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance } from 'ant-design-vue'
const route = useRoute()
const uid = route.query.uid as string

const token = useCookie(TOKEN, {
  domain: useRuntimeConfig().public.VITE_DOMAIN,
})

const formRef = useTemplateRef<FormInstance>('formRef')
const form = ref({
  mobile: '',
  smsCode: '',
})

const rules = {
  mobile: [
    { required: true, message: '请输入手机号' },
    { pattern: MOBILE_REG.reg, message: MOBILE_REG.msg },
  ],
  smsCode: { required: true, message: '请输入验证码' },
}

const bindMsg = async () => {
  await formRef.value?.validate('mobile')
  await http('/mall/user/sendVerifyCode', {
    method: 'put',
    body: {
      eventType: 'UPDATE_PHONE',
      mobile: form.value.mobile,
    },
  })
}

const loading = ref(false)

const bindUser = async () => {
  await formRef.value?.validate()
  loading.value = true
  const { mobile, smsCode } = form.value
  const [err, res] = await try_http('/mall/user/updatePhone', {
    method: 'put',
    body: {
      mobile,
      verifyCode: smsCode,
      uid,
    },
  })
  loading.value = false
  if (!err) {
    token.value = res.data.accessToken
    navigateTo({ path: '/' })
  }
}
</script>
