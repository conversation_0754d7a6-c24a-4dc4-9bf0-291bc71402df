<template>
  <div class="w-full h-full flex-center">
    <left-logo v-if="!isIframe"></left-logo>
    <div class="bg-#2A2A35 w-full max-w-566px p-3">
      <Back class="text-7" />
      <div class="text-center">
        <img h-15 src="~/assets/images/logo_rect_white.png" />
      </div>
      <div class="mt-10 px-10 flex">
        <a-form class="flex-1 b-r b-#373742 pr-14px" max-md="pr-0 b-r-0" ref="formRef" :rules="rules" :model="form">
          <div class="flex items-center">
            <span>手机注册</span>
          </div>
          <a-form-item mt-3 name="mobile">
            <a-input placeholder="请输入手机号" v-model:value="form.mobile"></a-input>
          </a-form-item>
          <a-form-item name="smsCode">
            <msg-input :api="getSms" msg-key="register" v-model:value="form.smsCode"></msg-input>
          </a-form-item>
          <div>
            <a-checkbox v-model:checked="checked">
              <div class="text-13px">
                <span>同意</span>
                <NuxtLink class="text-primary!" :to="{ path: '/agreement', query: { key: 'termOfService' } }"
                  target="_blank">
                  用户协议
                </NuxtLink>
                <span>、</span>
                <NuxtLink class="text-primary!" :to="{ path: '/agreement', query: { key: 'privacyPolicy' } }"
                  target="_blank">
                  隐私政策
                </NuxtLink>
                <span>与</span>
                <NuxtLink class="text-primary!" :to="{ path: '/agreement', query: { key: 'disclaimer' } }"
                  target="_blank">
                  免责声明
                </NuxtLink>
              </div>
            </a-checkbox>
          </div>
          <a-button mt-4 type="primary" :loading="loading" w-full :disabled="!checked" @click="showVerify">
            注册
          </a-button>
        </a-form>
        <div class="text-(white 14px) pl-14px" max-md="hidden" v-if="!inviteCode">
          <div mb-3>微信扫码注册</div>
          <div w-168px h-168px overflow-hidden>
            <div id="wx-login" w-full h-full></div>
          </div>

          <!-- <Wx /> -->
        </div>
      </div>
      <div class="text-(center 14px white) mt-3 mb-5">
        <span>已有账号，</span>
        <nuxt-link class="text-primary!" to="/login">立即登录</nuxt-link>
      </div>
    </div>

    <VerifitionVerify ref="verifyRef" :captcha-type="'blockPuzzle'" :img-size="{ width: '400px', height: '200px' }"
      @success="register" />
  </div>
</template>

<script setup lang="ts">
import type { FormInstance } from 'ant-design-vue'
const formRef = useTemplateRef<FormInstance>('formRef')
const checked = ref(false)
const form = ref({
  mobile: '',
  smsCode: '',
})

const rules = {
  mobile: [
    { required: true, message: '请输入手机号' },
    { pattern: MOBILE_REG.reg, message: MOBILE_REG.msg },
  ],
  smsCode: { required: true, message: '请输入验证码' },
}

const getSms = async () => {
  await formRef.value?.validate('mobile')
  await http('/mall/user/sendVerifyCode', {
    method: 'put',
    body: {
      eventType: 'REGISTER',
      mobile: form.value.mobile,
    },
  })
}

const loading = ref(false)
const register = async () => {
  const { smsCode, mobile } = form.value
  const [err, res] = await try_http('/mall/user/register', {
    method: 'post',
    body: {
      verifyCode: smsCode,
      identity: '其他',
      mobile,
      inviteCipher: inviteCode.value || '',
    },
  })
  if (!err) {
    message.success('注册成功')
    token.value = res.data.accessToken
    navigateTo({ path: '/' })
  }
}

// 点击注册表单验证
// 显示图形验证码
const verifyRef = ref()
const showVerify = async () => {
  await formRef.value?.validate()
  verifyRef.value?.show()
}

const inviteCode = computed(() => route.query.inviteCode as string)

onMounted(() => {
  if (!inviteCode.value) {
    wxutils('wx-login', 'wx', {
      self_redirect: true
    })
  }
})

const isIframe = computed(() => {
  return route.query.isIframe == '1'
})

// 微信登陆
useEventListener('message', (e) => {
  if (e.origin == 'https://www.yanxuan.cloud') {
    if (typeof e.data == 'object' && e.data.type == 'wx-login') {
      afterLogin(e.data.token)
    } else if (typeof e.data == 'object' && e.data.type == 'wx-bind') {
      navigateTo({ path: '/bind-user', query: { uid: e.data.uid } })
    }
  }
})
const token = useCookie(TOKEN, {
  domain: useRuntimeConfig().public.VITE_DOMAIN,
})

const route = useRoute()

const afterLogin = (_token) => {
  token.value = _token
  window.parent.postMessage('remote-login', '*')
  nextTick(() => {
    const redirect = route.query.redirect as string
    if (redirect) {
      navigateTo(redirect, { external: true })
    } else {
      navigateTo({ path: '/' })
    }
  })
}
</script>
