<template>
  <nuxt-layout name="child-page" title="历史会话">
    <div class="max-w-800px w-full bg-primaryBg h-[calc(100vh-180px)] flex">
      <scroll-bar class="w-250px border-r border-#353640 shrink-0">
        <div class="divide-y divide-#353640">
          <div
            class="flex px-3 items-center h-60px group cursor-pointer"
            :class="{
              'bg-primary': current.agent_id == item.agent_id,
            }"
            v-for="item in conversationList"
            :key="item.agent_id"
            @click="changeConversation(item)"
          >
            <a-avatar :src="useObs(item.agent_img)"></a-avatar>
            <div flex-1 mx-3 overflow-hidden>
              <div class="text-(white 16px) font-400 truncate">{{ item.agent_name }}</div>
              <div class="text-(#aaaaaa 12px) truncate">{{ item.description }}</div>
            </div>
            <div class="text-(#aaaaaa 12px) group-hover-hidden">{{ item.data.length }}个对话</div>
            <a-dropdown>
              <more-outlined class="text-(white 20px) cursor-pointer hidden" group-hover="block" @click.stop />
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="newChat(item)">
                    <plus-outlined />
                    开启新会话
                  </a-menu-item>
                  <!-- <a-menu-item>删除全部会话</a-menu-item> -->
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </scroll-bar>
      <scroll-bar class="flex-1">
        <div class="divide-y divide-#353640">
          <div
            v-for="item in current.data || []"
            :key="item.id"
            class="h-60px px-4 flex items-center cursor-pointer group"
            @click="continueSession(item)"
          >
            <div class="flex-1 overflow-hidden">
              <a-input
                class="max-w-250px!"
                v-model:value="item.name"
                v-if="item.edit"
                @blur="updateTitle(item)"
                @click.stop
                ref="inputRef"
                @press-enter="() => inputRef[0].blur()"
              ></a-input>
              <div class="text-(white 16px) font-400 truncate" v-else>{{ item.name }}</div>
              <div class="text-(#aaaaaa 12px) truncate">{{ item.introduction }}</div>
            </div>
            <div class="text-(#aaaaaa 12px)" group-hover="hidden">
              {{ dayjs(item.created_at * 1000).format('HH:mm') }}
            </div>
            <a-dropdown>
              <more-outlined class="text-(white 20px) cursor-pointer hidden" @click.stop group-hover="block" />
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="continueSession(item)">
                    <template #icon>
                      <caret-right-outlined />
                    </template>
                    继续会话
                  </a-menu-item>

                  <a-menu-item @click="item.edit = true">
                    <template #icon>
                      <edit-outlined />
                    </template>
                    编辑标题
                  </a-menu-item>

                  <a-menu-item @click="removeSession(item)">
                    <template #icon>
                      <delete-outlined />
                    </template>
                    删除会话
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </scroll-bar>
    </div>
  </nuxt-layout>
</template>

<script setup>
import dayjs from 'dayjs'

const { user } = storeToRefs(userStore())

const conversationList = ref([])

const current = ref({})
const getConversationList = async () => {
  const [err, res] = await try_http('/mall/p/llm/agent/conversationsAll', {
    query: {
      user: user.value.userId,
      limit: 100,
    },
  })
  if (!err) {
    conversationList.value = res.data
    if (res.data.length) {
      if (!current.value.agent_id) {
        current.value = res.data[0]
      } else {
        current.value = res.data.find((item) => item.agent_id == current.value.agent_id) || {}
      }
    }
  }
}

getConversationList()

const changeConversation = (item) => {
  current.value = item
}

const continueSession = (item) => {
  navigateTo({
    path: '/chat',
    query: {
      session_id: item.id,
      shop_id: current.value.agent_id,
    },
  })
}

const inputRef = ref()

const updateTitle = async (item) => {
  const [err] = await try_http(`/mall/p/llm/agent/conversations/name`, {
    method: 'post',
    body: {
      name: item.name,
      agentId: current.value.agent_id,
      user: user.value.userId,
      conversation_id: item.id,
    },
  })
  if (!err) {
    item.edit = false
  }
}

const removeSession = (item) => {
  Modal.confirm({
    title: '提示',
    content: '是否删除该会话?',
    onOk: async () => {
      const [err] = await try_http(`/mall/p/llm/agent/conversations/${item.id}`, {
        method: 'delete',
        body: {
          agentId: current.value.agent_id,
          user: user.value.userId,
        },
      })
      if (!err) {
        message.success('操作成功')
        getConversationList()
      }
    },
  })
}

const newChat = (item) => {
  navigateTo({
    path: '/chat',
    query: {
      shop_id: item.agent_id,
    },
  })
}
</script>
