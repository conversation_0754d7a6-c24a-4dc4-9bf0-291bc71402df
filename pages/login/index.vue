<template>
  <div class="w-full h-full flex-center overflow-hidden">
    <left-logo v-if="!isIframe"></left-logo>
    <div class="bg-#2A2A35 w-full max-w-566px p-3">
      <Back class="text-7 invisible" :class="{ '!visible': !isIframe }" />
      <div class="text-center">
        <img h-15 src="~/assets/images/logo_rect_white.png" />
      </div>
      <div class="mt-10 px-10 flex">
        <a-form
          class="flex-1 b-r b-#373742 pr-14px"
          max-md="pr-0 b-r-0"
          ref="formRef"
          :rules="rules"
          :model="form"
          @keyup.enter="login"
        >
          <div class="flex items-center cursor-pointer" @click="changeType">
            <span>手机登录</span>
            <span class="i-a-swap-outlined text-primary mx-1"></span>
            <span class="text-primary">切换</span>
          </div>
          <a-form-item mt-3 name="mobile">
            <a-input placeholder="请输入手机号" v-model:value="form.mobile"></a-input>
          </a-form-item>
          <a-form-item name="smsCode" v-if="loginType == 'msg'">
            <msg-input :api="loginMsg" msg-key="login" v-model:value="form.smsCode"></msg-input>
          </a-form-item>
          <a-form-item name="password" v-if="loginType == 'pwd'">
            <a-input
              type="password"
              v-model:value="form.password"
              autocomplete="new-password"
              placeholder="请输入密码"
            ></a-input>
          </a-form-item>
          <div class="text-right position-relative top-[-20px]" v-if="loginType == 'pwd'">
            <nuxt-link class="text-primary text-14px" to="/forgot-pw" v-if="!isIframe">忘记密码?</nuxt-link>
            <span class="text-primary text-14px cursor-pointer" v-else @click="goForgotPassword">忘记密码?</span>
          </div>
          <a-button type="primary" :loading="loading" w-full @click="login">登录</a-button>
        </a-form>
        <div class="text-white pl-14px" max-md="hidden">
          <div mb-3 text-14px>微信扫码登录</div>
          <div w-168px h-168px overflow-hidden>
            <div id="wx-login" class="w-full h-full"></div>
          </div>
        </div>
      </div>
      <div class="text-(center 14px white) mt-3 mb-5">
        <span>没有账号?</span>
        <nuxt-link class="text-primary!" to="/register" v-if="!isIframe">立即注册</nuxt-link>
        <span class="text-primary cursor-pointer" v-else @click="goRegsiter">立即注册</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance } from 'ant-design-vue'

const token = useCookie(TOKEN, {
  domain: useRuntimeConfig().public.VITE_DOMAIN,
})

const formRef = useTemplateRef<FormInstance>('formRef')
const form = ref({
  mobile: '',
  smsCode: '',
  password: '',
})

type Login = 'msg' | 'pwd'
const loginType = ref<Login>('pwd')

const rules = computed(() => {
  const rule: obj = {
    mobile: [
      { required: true, message: '请输入手机号' },
      { pattern: MOBILE_REG.reg, message: MOBILE_REG.msg },
    ],
  }
  if (loginType.value == 'msg') {
    rule.smsCode = { required: true, message: '请输入验证码' }
  } else if (loginType.value == 'pwd') {
    rule.password = { required: true, message: '请输入密码' }
  }
  return rule
})

const loginMsg = async () => {
  await formRef.value?.validate('mobile')
  await http('/mall/user/sendVerifyCode', {
    method: 'put',
    body: {
      eventType: 'LOGIN',
      mobile: form.value.mobile,
    },
  })
}

const loading = ref(false)
const login = async () => {
  await formRef.value?.validate()
  loading.value = true
  if (loginType.value == 'msg') {
    smsLogin()
  } else {
    pwdLogin()
  }
}

const route = useRoute()
const isIframe = computed(() => {
  return route.query.isIframe == '1'
})

const router = useRouter()
const goRegsiter = () => {
  const resolve = router.resolve('/register')
  window.open(resolve.href)
}
const goForgotPassword = () => {
  const resolve = router.resolve('/forgot-pw')
  window.open(resolve.href)
}
const afterLogin = (_token) => {
  token.value = _token
  window.parent.postMessage('remote-login', '*')
  nextTick(() => {
    const redirect = route.query.redirect as string
    if (redirect) {
      navigateTo(redirect, { external: true })
    } else {
      navigateTo({ path: '/' })
    }
  })
}

const smsLogin = async () => {
  const { mobile, smsCode } = form.value
  const [err, res] = await try_http('/mall/smsLogin', {
    method: 'post',
    body: {
      userName: mobile,
      passWord: smsCode,
    },
  })
  loading.value = false
  if (!err) {
    afterLogin(res.data.accessToken)
  }
}

const pwdLogin = async () => {
  const { mobile, password } = form.value
  const [err, res] = await try_http('/mall/login', {
    method: 'post',
    body: {
      userName: mobile,
      passWord: aesEncode(password),
    },
  })
  loading.value = false
  if (!err) {
    afterLogin(res.data.accessToken)
  }
}

const changeType = () => {
  if (loginType.value == 'msg') {
    loginType.value = 'pwd'
  } else {
    loginType.value = 'msg'
  }
}

onMounted(() => {
  wxutils('wx-login', 'wx', {
    self_redirect: true,
  })
})

// 微信登陆
useEventListener('message', (e) => {
  if (e.origin == 'https://www.yanxuan.cloud') {
    if (typeof e.data == 'object' && e.data.type == 'wx-login') {
      afterLogin(e.data.token)
    } else if (typeof e.data == 'object' && e.data.type == 'wx-bind') {
      navigateTo({ path: '/bind-user', query: { uid: e.data.uid } })
    }
  }
})
</script>
