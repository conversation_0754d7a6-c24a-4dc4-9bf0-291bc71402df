<template>
  <div class="w-full h-screen flex-center">
    <CheckCircleFilled class="text-(success 40px)" />
  </div>
</template>

<script setup>
definePageMeta({
  layout: false,
})
const route = useRoute()
onMounted(() => {
  const { token, uid } = route.query
  let message = {}
  if (token) {
    message = {
      type: 'wx-login',
      token,
    }
  } else if (uid) {
    message = {
      type: 'wx-bind',
      uid,
    }
  }
  window.parent.postMessage(message, '*')
})
</script>
