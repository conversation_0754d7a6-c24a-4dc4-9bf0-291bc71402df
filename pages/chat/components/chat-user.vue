<template>
  <div class="px-16px py-12px bg-darkBg bg-opacity-75 w-full overflow-hidden" flex>
    <div w-32px h-32px mr-16px>
      <a-avatar :src="user.pic">
        {{ isLogin ? user.nickName : '游客' }}
      </a-avatar>
    </div>
    <div class="text-#fff line-height-28px font-size-14px" v-if="ask.type == 'text'">
      <span class="mr-16px" v-if="ask.text">{{ ask.text }}</span>
      <div v-if="ask.attachments" class="mt-8px flex gap-8px flex-wrap">
        <a-image :width="200" v-for="(url, index) in ask.attachments" :key="index" :src="url" />
      </div>
    </div>
    <card-form v-else-if="ask.type == 'form'" v-bind="ask.attrs"></card-form>
  </div>
</template>

<script lang="ts" setup>
import cardForm from './card-form.vue'
const store = userStore()
const user = computed(() => store.user)
const isLogin = computed(() => user.value.userId)

const props = defineProps<{
  ask: string
}>()

const ask = computed(() => {
  let text = props.ask
  text = text.replace(/^<selected_prods>.*<\/selected_prods>(.*)/s, '$1')
  if (text.startsWith('```selection-form-snapshot')) {
    try {
      const attrs = JSON.parse(text.replace(/```selection-form-snapshot(.*)```/s, '$1'))
      return {
        type: 'form',
        attrs,
      }
    } catch (err) {
      console.log('%c Line:36 🥛 err', 'color:#1DC867', err)
    }
    return {
      type: 'text',
      text,
    }
  } else if (text.startsWith('<SYSTEM_REFER>')) {
    try {
      const attachmentsMatch = text.match(/<attachments>(.*?)<\/attachments>/)
      let attachments
      if (attachmentsMatch) {
        attachments = JSON.parse(attachmentsMatch[1])
      }
      const cleanText = text.replace(/<SYSTEM_REFER>.*?<\/SYSTEM_REFER>/s, '').trim()
      return {
        type: 'text',
        text: cleanText,
        ...(attachments && { attachments }),
      }
    } catch (err) {
      console.log('%c Line:error parsing SYSTEM_REFER', 'color:#1DC867', err)
    }
    return {
      type: 'text',
      text,
    }
  } else {
    return {
      type: 'text',
      text,
    }
  }
})
</script>

<style scoped>
:deep(.ant-image) {
  display: inline-flex;
}
</style>
