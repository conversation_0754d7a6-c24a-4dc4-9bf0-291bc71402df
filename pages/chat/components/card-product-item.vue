<template>
  <div class="w-275px h-275px card-shadow bg-#32323E m-2 flex cursor-pointer" @click="emits('clickItem')">
    <div class="flex-center flex-1" v-if="loading">
      <a-spin :spinning="true"></a-spin>
    </div>
    <div v-else flex-1 p-4 overflow-hidden>
      <div class="text-center">
        <img class="w-120px h-120px mx-auto mt-2 object-scale-down" :src="useImage(info.imageUrl)" alt="" />
      </div>

      <div class="mt-2 truncate font-bold font-size-16px text-center h-28px" :title="name">
        {{ name }}
      </div>

      <div v-if="!isLogin" text="primary 14px" mt-4 font-bold cursor-pointer>登录后查看价格与交期 &gt;</div>

      <div w-full v-else>
        <div text="14px #858585" my-4px font-bold v-if="!info.canInquiry">该商品暂不支持询价与购买</div>
        <div text="14px #ff0000" my-4px font-bold v-else-if="isUpdate">价格信息更新中</div>
        <div v-else>
          <div my-4px flex items-center>
            <div class="flex-1 text-#ff0000">
              <span font-bold text-20px>{{ getPrice(isSku ? info.price : info.lowestPrice) }}</span>
              <span text-10px v-if="!isSku">起</span>
              <span text-14px v-if="info.packingUnit">/{{ info.packingUnit }}</span>
            </div>
            <div v-if="!isUpdate && isLogin" bg-primary text-white text-12px leading-1.15em px-4px py-2px rounded-2px>
              {{ term }}
            </div>
          </div>
        </div>
      </div>

      <div flex items-center justify-between>
        <div @click.stop="emits('clickBrand', brand)" cursor-pointer hover="text-primary">
          <a-avatar :src="useObs(brand.brandLogo)">
            {{ brand.brandName }}
          </a-avatar>
          <span ml-2 text-xs>{{ brand.brandName }}</span>
        </div>
        <div flex-center v-if="info.canInquiry">
          <a-tooltip title="询问客服">
            <div
              class="i-custom-customer text-white text-20px cursor-pointer hover-text-primary"
              @click.stop="emits('clickCustomer')"
            ></div>
          </a-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps<{
  loading?: boolean
  isSku?: boolean
  info: obj
}>()

const emits = defineEmits(['clickItem', 'clickBrand', 'clickCustomer'])

const { loading, isSku, info } = toRefs(props)

const isLogin = useLoginState()

const isUpdate = computed(() => {
  if (isSku.value) {
    const { price, tradeTerm } = info.value
    return !has(price) || !has(tradeTerm)
  } else {
    const { lowestDiscountedPrice, lowestTradeTerm } = info.value
    return !has(lowestDiscountedPrice) || !has(lowestTradeTerm)
  }
})

const name = computed(() => {
  return isSku.value ? info.value.skuCode : info.value.productName
})

const term = computed(() => {
  const { lowestTradeTerm, tradeTerm } = info.value
  if (isSku.value) {
    return getTradeTerm(tradeTerm)
  } else {
    return getTradeTerm(lowestTradeTerm, '最快')
  }
})

const brand = computed(() => {
  if (isSku.value) {
    const { brandName, brandLogo, brandCode } = info.value
    return {
      brandName,
      brandLogo,
      brandCode,
    }
  } else {
    const { websiteImgUrl, websiteName, websiteCode } = info.value.website || {}
    return {
      brandName: websiteName,
      brandLogo: websiteImgUrl,
      brandCode: websiteCode,
    }
  }
})
</script>
