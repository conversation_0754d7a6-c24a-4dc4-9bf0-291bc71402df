<script lang="tsx">
import { type PropType, h } from 'vue'
import type { AstNode } from './type'
import Card from './card.vue'
import CardList from './card-list.vue'
import CardForm from './card-form.vue'
import CardCompare from './card-compare.vue'
import RenderSpin from './renderSpin.vue'
const GroupReg = /group-(.*)/
export type CardProps = {
  display?: string
  type?: Nullable<string>
  code?: Nullable<string>
  id?: Nullable<string>
  content?: string
  site?: Nullable<string>
}

const renderNull = () => {
  return h(RenderSpin)
}

const renderDefault = (node: AstNode) => {
  return node.tag ? h(node.tag, node.attrs, node.content) : node.content
}
const renderLink = (node: AstNode) => {
  const href = node.attrs.href
  if (node.attrs.href) {
    const search = new URLSearchParams(href)
    const display = search.get('display')
    const type = search.get('type')
    const props = {} as CardProps
    if (display && type) {
      props.display = display
      props.type = search.get('type')
      props.code = search.get('code')
      props.id = search.get('id')
      props.site = search.get('site')
      props.content = node.content
      // @ts-ignore
      return h(Card, props)
    } else {
      return renderDefault(node)
    }
  }
}

const renderGroup = (node: AstNode) => {
  let match = node.custom.match(GroupReg)
  if (match) {
    const type = match[1]
    const content = node.content
    try {
      const ids = JSON.parse(content)
      if (type == 'prods-compare') {
        return h(CardCompare, { ids, key: content })
      }
      return h(CardList, { ids, type, key: content })
    } catch (error) {
      return renderNull()
    }
  }
}

const renderForm = (node: AstNode) => {
  const content = node.content
  try {
    if (content) {
      const props = JSON.parse(content)
      return h(CardForm, { ...props, key: content })
    }
  } catch (err) {
    return renderNull()
  }
}

const renderCode = (node: AstNode) => {
  if (node.custom == 'selection-form') {
    return renderForm(node)
  } else if (node.custom.startsWith('group-')) {
    return renderGroup(node)
  } else {
    return renderDefault(node)
  }
}

export default defineComponent({
  props: {
    children: {
      type: Array as PropType<AstNode[]>,
      default: () => [],
    },
  },
  setup(props) {
    const renderItem = (node: AstNode) => {
      if (node.type == 'fence') {
        return renderCode(node)
      }
      if (node.tag == 'a') {
        return renderLink(node)
      }
      if (node.children.length) {
        return h(
          node.tag,
          node.attrs,
          node.children.map((item) => renderItem(item))
        )
      } else {
        return renderDefault(node)
      }
    }
    return () => {
      const list = props.children
      return list.map((item) => {
        return renderItem(item)
      })
    }
  },
})
</script>
