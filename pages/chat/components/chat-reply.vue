<template>
  <div class="px-4 py-3 bg-primaryBg bg-opacity-75 w-full overflow-x-hidden">
    <div flex>
      <div w-8 h-8 mr-4 bg-white rounded-full>
        <a-avatar :src="useImage(convertImage(chat.agent.agentImg))"></a-avatar>
      </div>
      <div class="text-#fff line-height-28px font-size-14px flex-1 overflow-x-auto replay">
        <tool-info v-if="!item.answer && !item.tool" title="正在思考中..."></tool-info>
        <Render :children="nodeList" />
        <template v-if="item.tool">
          <tool-info v-if="item.tool.use == 'info'" :title="item.tool.title"></tool-info>
          <tool-process
            v-else-if="item.tool.use == 'processes'"
            :title="item.tool.title"
            v-model="item.tool.current"
            :processes="item.tool.processes"
          ></tool-process>
        </template>
        <span class="tail" v-if="!item.isFinished && item.answer"></span>
        <span v-if="item.isFinished && !isLogin">
          您可以
          <a href="javascript:void(0)" class="text-primary font-bold" @click="onLogin">登录</a>
          后再找我聊天，我的能力会更强哦！
        </span>
      </div>
    </div>
    <div flex text-white h-30px items-center v-if="item.isFinished && !item.isOpeningWords">
      <div class="flex-1"></div>
      <img v-if="item.fav == 'like'" :src="likeFill" @click="setFav()" class="w-5 h-5 mr-4 cursor-pointer" alt="" />
      <img v-else :src="like" @click="setFav('like')" class="w-5 h-5 mr-4 cursor-pointer" alt="" />
      <img v-if="item.fav == 'dislike'" :src="dislikeFill" @click="setFav()" class="w-5 h-5 cursor-pointer" alt="" />
      <img v-else :src="dislike" @click="setFav('dislike')" class="w-5 h-5 cursor-pointer" alt="" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import like from '~/assets/images/thumb-up-line.svg'
import likeFill from '~/assets/images/thumb-up-fill.svg'
import dislike from '~/assets/images/thumb-down-line.svg'
import dislikeFill from '~/assets/images/thumb-down-fill.svg'
import markdownit from 'markdown-it'
import { tokensToAST } from './type'
import Render from './render.vue'
import type Chat from '~/utils/chat'
import emitter from '../mitt'
const props = defineProps<{
  item: Ans
  chat: Chat
  isLogin: boolean
}>()

const { item } = toRefs(props)
const emits = defineEmits(['ok'])
const current = ref(0)

const md = markdownit({
  html: true,
  linkify: true,
  typographer: true,
})

// 在tokensToAST处理前修改tokens，将a标签添加target="_blank"和rel="noopener noreferrer"
md.core.ruler.after('inline', 'add_target_to_links', (state) => {
  const tokens = state.tokens

  for (let i = 0; i < tokens.length; i++) {
    if (tokens[i].type === 'inline' && tokens[i].children) {
      const children = tokens[i].children

      for (let j = 0; j < children.length; j++) {
        if (children[j].type === 'link_open') {
          const targetIndex = children[j].attrIndex('target')
          const relIndex = children[j].attrIndex('rel')

          if (targetIndex < 0) {
            children[j].attrPush(['target', '_blank'])
          } else {
            children[j].attrs[targetIndex][1] = '_blank'
          }

          if (relIndex < 0) {
            children[j].attrPush(['rel', 'noopener noreferrer'])
          } else {
            children[j].attrs[relIndex][1] = 'noopener noreferrer'
          }
        }
      }
    }
  }

  return true
})

const nodeList = computed(() => {
  const parse = md.parse(item.value.answer, {})
  return tokensToAST(parse)
})

const setFav = async (fav?: string) => {
  const data: obj = {
    message_id: item.value.message_id,
    user: item.value.user,
    agentId: props.chat.shop_id,
  }

  if (fav) {
    data.rating = fav
  }
  const body = getshopsign(data, 'post')
  const [err, res] = await try_http('/mall/p/llm/agent/feedbacks', {
    method: 'post',
    body,
    headers: {
      grantType: 'sign',
    },
  })
  if (!err) {
    item.value.fav = fav
    console.log('res', JSON.parse(res.data))
  }
}

const onLogin = () => {
  // const route = useRoute()
  // navigateTo({ path: '/login', query: { redirect: route.fullPath } })
  emitter.emit('login')
}
</script>

<style lang="less" scoped>
.tail::after {
  animation: blink 1s steps(5, start) infinite;
  content: '_';
  font-weight: 700;
  margin-left: 3px;
  vertical-align: baseline;
}

@keyframes blink {
  to {
    visibility: hidden;
  }
}

:deep(p) {
  img {
    max-width: 50%;
  }
}

:deep(table) {
  border-collapse: collapse;
  margin: 12px 0;
  width: 100%;

  th,
  td {
    border: 1px solid #373742;
    padding: 12px;
    text-align: left;

    img {
      max-width: 100%;
      max-height: 250px;
      object-fit: contain;
    }
  }

  th {
    background-color: #2a2a35;
    font-weight: bold;
  }

  tr:nth-child(even) {
    background-color: rgba(42, 42, 53, 0.5);
  }

  tr:hover {
    background-color: rgba(42, 42, 53, 0.8);
  }
}

.replay {
  :deep(li) {
    list-style: disc;
  }

  :deep(ul) {
    margin-left: 1em;
  }
}
</style>
