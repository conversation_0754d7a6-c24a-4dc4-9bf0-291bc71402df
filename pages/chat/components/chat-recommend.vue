<template>
  <div class="flex flex-wrap gap-4 bg-primaryBg pl-16 pr-4 pt-0 pb-3 bg-opacity-75">
    <div
      class="flex h-8 items-center rounded-10 px-2 overflow-hidden cursor-pointer text-(#fff 14px) transition-all duration-200 hover:(color-#f94c30 border-#f94c30)"
      border="1px solid #A3A3A4"
      v-for="item in recommends"
      :key="item"
      @click="emits('ask', item)"
    >
      <div>{{ item }}</div>
      <div class="i-a-arrow-right-outlined text-lg"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
withDefaults(
  defineProps<{
    recommends: string[]
  }>(),
  {
    recommends: () => [],
  },
)

const emits = defineEmits<{
  ask: [string]
}>()
</script>
