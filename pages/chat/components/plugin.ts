import type MarkdownIt from 'markdown-it/index.js'
import Card from './card.vue'
import CardList from './card-list.vue'
import { createVNode, render } from 'vue'

export type Card = {
  display?: string
  type?: Nullable<string>
  code?: Nullable<string>
  id?: Nullable<string>
  content?: string
  site?: Nullable<string>
}

type State = {
  count: number
  map: Map<number, string>
  cache: Map<string, HTMLElement>
}

const GroupReg = /group-(.*)/

export const linkRule = (md: MarkdownIt, state: State, prefix) => {
  let props: Card = {}
  const cache = state.cache

  const _render = (mountId: string, props: Card) => {
    const { type, code, id, content, site } = props
    if (!cache.has(mountId)) {
      const app = createVNode(Card, {
        type,
        code,
        content,
        id,
        site,
      })
      const ctx = useNuxtApp().vueApp._context
      app.appContext = ctx
      const el = document.createElement('span')
      render(app, el)
      cache.set(mountId, el)
      // const app = cache.get(mountId)
    }

    setTimeout(() => {
      // const app = cache.get(mountId)
      // const el = document.querySelector(`[data-id="${mountId}"]`)
      // if (el && app) {
      //   render(app, el)
      // }
      const el = cache.get(mountId)

      if (el) {
        const root = document.querySelector(`[data-id="${mountId}"]`)
        root?.appendChild(el)
      }
    })
  }

  const defaultRender =
    md.renderer.rules.link_open ||
    function (tokens, idx, options, env, self) {
      return self.renderToken(tokens, idx, options)
    }

  md.renderer.rules.link_open = function (tokens, idx, options, env, self) {
    const token = tokens[idx]
    const href = token.attrGet('href')
    state.count++

    if (href) {
      const search = new URLSearchParams(href)
      const display = search.get('display')
      if (display) {
        props.display = display
        props.type = search.get('type')
        props.code = search.get('code')
        props.id = search.get('id')
        props.site = search.get('site')
        props.content = tokens[idx + 1].content
        if (!state.map.has(state.count)) {
          state.map.set(state.count, genUUID())
        }
        const dataId = state.map.get(state.count)!
        tokens[idx + 1].content = ''
        return `<span data-id="${dataId}">`
      }
    }
    // console.log('%c Line:15 🥛 query', 'color:#0CB8E4', query)
    const hrefIndex = tokens[idx].attrIndex('target')
    if (hrefIndex < 0) {
      tokens[idx].attrPush(['target', '_blank'])
    } else {
      tokens[idx].attrs![hrefIndex][1] = 'blank'
    }

    return defaultRender(tokens, idx, options, env, self)
  }

  md.renderer.rules.link_close = (tokens, idx, options, env, self) => {
    const id = state.map.get(state.count)
    if (id) {
      _render(id, props)
      return `</span>`
    }
    return self.renderToken(tokens, idx, options)
  }

  const fenceRender = md.renderer.rules.fence!

  md.renderer.rules.fence = (tokens, idx, options, env, self) => {
    const token = tokens[idx]
    const info = token.info
    const match = info.match(GroupReg)
    if (match) {
      const _match = match[1]
      let type = ''
      switch (_match) {
        case 'brands':
          type = 'brand'
          break
        case 'prods':
          type = 'product'
          break
        case 'categories':
          type = 'category'
          break
      }

      const content = token.content
      if (/^\[.*\]/.test(content)) {
        try {
          const ids = JSON.parse(token.content)

          const dataId = prefix + token.map?.join(',')
          _renderList(type, dataId, ids, state.cache)
          return `<div data-id="${dataId}"></div>`
        } catch (error) {}
      }
    }
    return fenceRender(tokens, idx, options, env, self)
  }
}

function _renderList(type: string, mountId: string, ids: string[], cache: Map<string, HTMLElement>) {
  setTimeout(() => {
    if (!cache.has(mountId)) {
      const app = createVNode(CardList, {
        type,
        ids,
      })
      const ctx = useNuxtApp().vueApp._context
      app.appContext = ctx
      const el = document.createElement('div')
      render(app, el)
      cache.set(mountId, el)
    }
    const el = cache.get(mountId)

    if (el) {
      const root = document.querySelector(`[data-id="${mountId}"]`)
      root?.appendChild(el)
      // if (root) {
      //   if (!root.childElementCount) {
      //   }
      // }
    }
  })
}
