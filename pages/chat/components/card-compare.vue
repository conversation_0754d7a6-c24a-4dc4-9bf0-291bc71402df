<template>
  <a-spin v-if="loading"></a-spin>
  <div class="w-full overflow-x-auto my-20px" v-else-if="params.length">
    <div border="1px solid #38383C" class="divide-y divide-#38383C inline-block">
      <div class="flex divide-x divide-#38383C">
        <div class="w-280px flex-center bg-#2D2D35">对比项目</div>
        <div class="w-280px bg-#2D2D35 overflow-hidden" v-for="(item, i) in compareList" :key="item.partNumber">
          <div class="w-full text-center py-20px">
            <img :src="useImage(item.image)" class="w-100px h-100px" alt="" />
            <div class="truncate button-link" @click="goSku(i)">{{ item.partNumber }}</div>
          </div>
        </div>
      </div>

      <div class="flex divide-x divide-#38383C" v-for="p in params" :key="p.v">
        <div class="w-280px flex-center bg-#2D2D35">{{ p.l }}</div>
        <div
          class="w-280px flex-center"
          :class="{
            'text-primary': !isSame(compareList, p.v),
          }"
          v-for="item in compareList"
          :key="item.partNumber"
        >
          {{ item.p[p.v] }}
        </div>
      </div>
    </div>
  </div>
  <a-alert type="error" v-else message="型号加载错误，无法对比"></a-alert>
</template>

<script setup>
import emitter from '../mitt'

const props = defineProps({
  ids: {
    type: Array,
    default: () => [],
  },
})

const { ids } = toRefs(props)
const compareList = ref([])
const params = ref([])
const loading = ref(true)
onMounted(() => {
  getParams()
})

const getParams = async () => {
  loading.value = true
  try {
    const res = await Promise.all(
      ids.value.map((item) => {
        const [partId, skuCode] = item.split(':')
        return http('/api/selection/part-parameters', {
          method: 'post',
          body: {
            partId,
            skuCode,
          },
        })
      }),
    )

    const list = []
    const set = new Set()
    const p = []
    compareList.value = res.forEach((r) => {
      const item = r.data
      const o = {
        image: item.image,
        partNumber: item.partNumber,
        p: {},
      }
      list.push(o)
      item.specValues.forEach((s) => {
        if (!set.has(s.specCodeDisplayName)) {
          set.add(s.specCodeDisplayName)
          p.push({ l: s.specCodeDisplayName, v: s.specCode })
        }
        o.p[s.specCode] = s.specValueDisplayName
      })
    })
    params.value = p
    compareList.value = list
  } catch (err) {}
  loading.value = false
}

const isSame = (list, prop) => {
  const set = new Set()
  list.forEach((item) => {
    set.add(item.p[prop])
  })
  return set.size == 1
}

const goSku = (index) => {
  const [partId, skuCode] = ids.value[index].split(':')
  emitter.emit('goSku', {
    partId,
    skuCode,
  })
}
</script>
