<template>
  <card-product-item
    :loading="loading"
    :info="info"
    :is-sku="isSku"
    @click-item="toProduct"
    @click-brand="toBrand"
    @click-customer="addTag"
  ></card-product-item>
</template>

<script setup lang="ts">
import emitter from '../mitt'
import cardProductItem from './card-product-item.vue'
const props = defineProps<{
  id?: string
}>()

const { id } = toRefs(props)

const current = computed(() => {
  const [spu, typeCode] = (id.value || '').split(':')
  return {
    spu,
    typeCode,
  }
})

const isSku = computed(() => has(current.value.typeCode))

const loading = ref(false)

const info = ref<obj>({})

const getInfo = () => {
  if (!current.value.spu) return
  if (!isSku.value) {
    getProductInfo()
  } else {
    getSkuInfo()
  }
}

async function getSkuInfo() {
  loading.value = true
  const [err, res] = await try_http('/api/selection/skus', {
    method: 'post',
    body: [
      {
        skuCode: current.value.typeCode,
        partId: current.value.spu,
      },
    ],
  })
  loading.value = false
  if (!err) {
    const [skuInfo] = res.data
    info.value = skuInfo || {}
  }
}

async function getProductInfo() {
  loading.value = true
  const [err, res] = await try_http('/api/selection/part-details', {
    params: {
      id: current.value.spu,
    },
  })
  loading.value = false
  if (!err) {
    console.log('res', res)
    info.value = res.data
  }
}

const bomai = useRuntimeConfig().public.VITE_BOMAI_URL

function toProduct() {
  let url
  const brandId = info.value.website?.websiteCode
  if (brandId) {
    url = `${bomai}/brand/${brandId}/${current.value.spu}`
  } else {
    url = `${bomai}/parts/${current.value.spu}`
  }
  if (isSku.value) {
    url = url + `?typeCode=${current.value.typeCode}`
  }
  window.open(url, '__target')
}

function toBrand({ brandCode }) {
  const bomai = useRuntimeConfig().public.VITE_BOMAI_URL
  if (brandCode) {
    window.open(`${bomai}/brand/${brandCode}`, '__target')
  }
}

function login() {}

const addTag = () => {
  emitter.emit('addTag', {
    _id: id.value,
    isSku: isSku.value,
    ...info.value,
  })
}
onMounted(() => {
  getInfo()
})
</script>
