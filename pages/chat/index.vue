<template>
  <div class="flex h-full">
    <!-- Mobile Overlay -->
    <div
      v-if="showMobileDrawer"
      class="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
      @click="showMobileDrawer = false"
    ></div>

    <!-- Left Panel - Agent List & Conversation History -->
    <div
      class="bg-primaryBg border-r border-#353640 flex flex-col transition-transform duration-300 ease-in-out z-50"
      :class="{
        'fixed inset-y-0 left-0 transform md:relative md:transform-none': true,
        'translate-x-0': showMobileDrawer || !isMobile,
        '-translate-x-full': !showMobileDrawer && isMobile,
      }"
      :style="{ width: isMobile ? '300px' : width + 'px' }"
    >
      <!-- Header -->
      <div class="h-60px px-4 flex items-center justify-between border-b border-#353640">
        <!-- Desktop back button -->
        <nuxt-link
          v-if="!isMobile && !isIframe"
          to="/"
          class="flex items-center text-white text-16px p-1 transition-colors"
        >
          <div class="text-white i-a-arrow-left-outlined mr-2"></div>
          <h3 class="inline-block text-white text-16px font-medium" :class="{ 'flex-1 text-center': isMobile }">
            会话列表
          </h3>
        </nuxt-link>
        <!-- Mobile close button -->
        <div v-if="isMobile" class="flex flex-1 items-center justify-between">
          <h3 class="inline-block text-white text-16px font-medium" :class="{ 'flex-1 text-center': isMobile }">
            会话列表
          </h3>
          <div
            @click="showMobileDrawer = false"
            class="i-a-close-outlined text-white text-18px p-1 transition-colors md:hidden"
          ></div>
        </div>
      </div>

      <!-- Agent List & Conversations -->
      <scroll-bar class="flex-1">
        <div class="divide-y divide-#353640">
          <div v-for="agent in conversationList" :key="agent.agent_id" class="border-b border-#353640">
            <!-- Agent Header -->
            <div class="flex px-3 items-center h-50px group cursor-pointer transition-colors">
              <div
                class="text-white text-12px transition-transform cursor-pointer p-1 mr-2"
                :class="{ 'rotate-90': expandedAgents.has(agent.agent_id) }"
                @click="toggleAgent(agent)"
              >
                <caret-right-outlined />
              </div>
              <a-avatar :src="useObs(agent.agent_img)" :size="32" @click="toggleAgent(agent)"></a-avatar>
              <div class="flex-1 mx-3 overflow-hidden" @click="toggleAgent(agent)">
                <div class="text-(white 14px) font-400 truncate">{{ agent.agent_name }}</div>
                <div class="text-(#aaaaaa 12px) truncate">{{ agent.description }}</div>
              </div>
              <div class="flex items-center gap-2">
                <span class="text-(#aaaaaa 12px)">{{ agent.data.length }}</span>
              </div>
              <a-dropdown>
                <more-outlined class="text-(white 16px) cursor-pointer hidden ml-2" group-hover="block" @click.stop />
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="startNewChat(agent)">
                      <plus-outlined />
                      开启新会话
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>

            <!-- Conversation List -->
            <div v-if="expandedAgents.has(agent.agent_id) && agent.data.length" class="bg-#1a1a22">
              <div
                v-for="conversation in agent.data"
                :key="conversation.id"
                class="h-44px px-6 pl-12 flex items-center cursor-pointer group transition-colors"
                :class="{
                  'bg-primary hover:bg-primary': currentSession?.id === conversation.id,
                  'hover:bg-#2A2A33': currentSession?.id !== conversation.id,
                }"
                @click="selectConversation(agent, conversation)"
              >
                <div class="flex-1 overflow-hidden">
                  <a-input
                    class="max-w-200px!"
                    v-model:value="conversation.name"
                    v-if="conversation.edit"
                    @blur="updateConversationTitle(agent, conversation)"
                    @click.stop
                    ref="inputRef"
                    @press-enter="() => (conversation.edit = false)"
                    size="small"
                  ></a-input>
                  <div class="text-(white 13px) font-400 truncate" v-else>{{ conversation.name }}</div>
                  <div class="text-(#aaaaaa 11px) truncate">{{ conversation.introduction }}</div>
                </div>
                <div class="text-(#aaaaaa 11px)" group-hover="hidden">
                  {{ dayjs(conversation.created_at * 1000).format('HH:mm') }}
                </div>
                <a-dropdown>
                  <more-outlined class="text-(white 14px) cursor-pointer hidden ml-2" @click.stop group-hover="block" />
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="selectConversation(agent, conversation)">
                        <template #icon>
                          <caret-right-outlined />
                        </template>
                        继续会话
                      </a-menu-item>
                      <a-menu-item @click="conversation.edit = true">
                        <template #icon>
                          <edit-outlined />
                        </template>
                        编辑标题
                      </a-menu-item>
                      <a-menu-item @click="removeConversation(agent, conversation)">
                        <template #icon>
                          <delete-outlined />
                        </template>
                        删除会话
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </div>
          </div>
        </div>
      </scroll-bar>
    </div>

    <!-- Resize Handle (Desktop only) -->
    <div
      v-if="!isMobile"
      class="w-1px bg-#353640 hover:bg-primary cursor-col-resize transition-colors relative group"
      @mousedown="startWatch"
    >
      <div class="absolute inset-y-0 -left-1 -right-1"></div>
    </div>

    <!-- Right Panel - Chat Interface -->
    <div class="flex flex-col flex-1" :style="{ marginLeft: isMobile ? '0' : '0' }">
      <!-- Chat Header -->
      <div class="w-full h-60px bg-primaryBg flex justify-center items-center border-b border-#353640">
        <div class="max-w-1280px w-full h-full flex justify-center items-center text-white relative px-4">
          <!-- Mobile menu button -->
          <button
            v-if="isMobile && isLogin"
            @click="showMobileDrawer = true"
            class="absolute left-4 text-white text-18px p-1 hover:text-primary transition-colors md:hidden bg-transparent"
          >
            <div class="i-a-menu-outlined"></div>
          </button>

          <!-- Mobile back button (for non-logged users or when no history available) -->
          <nuxt-link
            v-if="isMobile && (!isLogin || !conversationList.length)"
            to="/"
            class="absolute left-4 text-white text-18px p-1 hover:text-primary transition-colors md:hidden"
          >
            <div class="i-a-arrow-left-outlined"></div>
          </nuxt-link>

          <template v-if="chat.agent.id">
            <div bg-white w-10 h-10 rounded-full>
              <a-avatar :src="useImage(convertImage(chat.agent.agentImg))" :size="40"></a-avatar>
            </div>
            <div mx-3>{{ chat.agent.agentName }}</div>
            <a-tag color="#F94C30" v-if="chat.shop_id == '0'">官方</a-tag>
          </template>
        </div>
      </div>

      <!-- Chat Messages -->
      <scroll-bar defer class="flex flex-col-reverse py-4px! scroll-bar">
        <div class="flex-1"></div>
        <div class="w-full max-w-1280px mx-auto px-4px" v-for="item in chat.chatList" :key="item.id">
          <chat-user :ask="item.ask" v-if="item.type == 'user'"></chat-user>
          <chat-reply
            :item="item"
            v-else-if="item.type == 'answer'"
            :chat="chat"
            :shop_id="chat.shop_id"
            :isLogin="isLogin"
          ></chat-reply>
          <chat-recommend
            :recommends="item.recommends"
            v-else-if="item.type == 'recommend'"
            @ask="recommendAsk"
          ></chat-recommend>
        </div>
      </scroll-bar>

      <!-- Chat Input Area -->
      <div class="max-w-1280px w-full mx-auto p-1 relative">
        <a-button
          class="relative my-10px left-1/2 translate-x--1/2"
          type="primary"
          shape="round"
          @click="newChat"
          v-if="chat.chatList.length"
        >
          <plus-outlined />
          新会话
        </a-button>

        <ask-input :sending="chat.generating" :show-upload="chat.shop_id !== '0'" @send="send">
          <template #top>
            <div class="flex flex-wrap px-1 gap-10px" v-if="chat.tags">
              <div
                v-for="item in chat.tags"
                :key="item._id"
                class="bg-#2A2A33 flex items-center text-12px h-24px px-4px"
              >
                <img class="w-16px h-16px" :src="useImage(item.imageUrl)" alt="" />
                <a-popover>
                  <div class="mx-4px">{{ item.isSku ? item.skuCode : item.productName }}</div>
                  <template #content>
                    <card-product-item :isSku="item.isSku" :info="item"></card-product-item>
                  </template>
                </a-popover>
                <div class="i-a-close-outlined cursor-pointer" @click="() => chat.removeTag(item)"></div>
              </div>
            </div>
          </template>
        </ask-input>
        <div class="h-20px line-height-24px text-center text-12px text-#7f7f7f">内容由AI大模型生成，请谨慎甄别</div>
      </div>
    </div>

    <side-widget v-if="!isIframe" :style="{ zIndex: isMobile && showMobileDrawer ? 25 : 60 }"></side-widget>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import chatUser from './components/chat-user.vue'
import chatReply from './components/chat-reply.vue'
import chatRecommend from './components/chat-recommend.vue'
import Chat from '~/utils/chat'
import emitter from './mitt'
import cardProductItem from './components/card-product-item.vue'

// Type definitions
interface ConversationItem {
  id: string
  name: string
  introduction: string
  created_at: number
  edit?: boolean
}

interface AgentData {
  agent_id: string
  agent_name: string
  agent_img: string
  description: string
  data: ConversationItem[]
}

// Composables and reactive data
const route = useRoute()
const user = computed(() => userStore().user)
const { user: userRef } = storeToRefs(userStore())
const isLogin = useLoginState()

// Mobile responsiveness
const isMobile = ref(false)
const showMobileDrawer = ref(false)

// Panel resizing (desktop only)
const { width, startWatch } = useWidth(320) // Initial width 320px

// Conversation management
const conversationList = ref<AgentData[]>([])
const expandedAgents = ref(new Set<string>())
const currentAgent = ref<AgentData | null>(null)
const currentSession = ref<ConversationItem | null>(null)
const inputRef = ref()

// Get URL parameters
const session_id = route.query.session_id as string
const shop_id = route.query.shop_id as string

// Initialize chat instance
const chat = ref(
  new Chat({
    base: useRuntimeConfig().public.VITE_API_BASE,
    shop_id: shop_id || '0',
    session_id,
    user_id: isLogin.value ? user.value.userId : 'xiaoyan_' + genUUID(),
    afterClose() {
      const ask = route.query.ask
      if (isLogin.value && (ask || !route.query.session_id)) {
        history.replaceState(null, '', `/chat?shop_id=${chat.value.shop_id}&session_id=${chat.value.session_id}`)
      }
    },
  })
)

// Mobile detection and window resize handling
const checkMobile = () => {
  if (import.meta.client) {
    isMobile.value = window.innerWidth < 768
    if (!isMobile.value) {
      showMobileDrawer.value = false
      // Remove body scroll prevention when switching to desktop
      document.body.style.overflow = ''
    }
  }
}

// Prevent body scroll when mobile drawer is open
watch(showMobileDrawer, (isOpen) => {
  if (import.meta.client && isMobile.value) {
    document.body.style.overflow = isOpen ? 'hidden' : ''
  }
})

// Load conversation list
const getConversationList = async () => {
  const [err, res] = await try_http('/mall/p/llm/agent/conversationsAll', {
    query: {
      user: user.value.userId,
      limit: 100,
    },
  })
  if (!err) {
    conversationList.value = res.data

    // Auto-expand and select current agent if session_id is provided
    if (session_id && res.data.length) {
      const targetAgent = res.data.find((agent) => agent.data.some((conv) => conv.id === session_id))
      if (targetAgent) {
        currentAgent.value = targetAgent
        expandedAgents.value.add(targetAgent.agent_id)
        const targetSession = targetAgent.data.find((conv) => conv.id === session_id)
        if (targetSession) {
          currentSession.value = targetSession
        }
      }
    } else if (res.data.length && shop_id) {
      // Select agent based on shop_id
      const targetAgent = res.data.find((agent) => agent.agent_id === shop_id)
      if (targetAgent) {
        currentAgent.value = targetAgent
        if (!session_id) {
          expandedAgents.value.add(targetAgent.agent_id)
        }
      }
    }
  }
}

// Agent and conversation management methods
const toggleAgent = (agent: AgentData) => {
  if (expandedAgents.value.has(agent.agent_id)) {
    expandedAgents.value.delete(agent.agent_id)
  } else {
    expandedAgents.value.add(agent.agent_id)
  }
}

const selectAgent = async (agent: AgentData) => {
  currentAgent.value = agent
  currentSession.value = null

  // Update chat instance
  chat.value.shop_id = agent.agent_id
  chat.value.session_id = ''
  chat.value.newChat()

  // Update URL
  await navigateTo(
    {
      path: '/chat',
      query: {
        shop_id: agent.agent_id,
      },
    },
    { replace: true }
  )

  // Close mobile drawer on selection
  if (isMobile.value) {
    showMobileDrawer.value = false
  }

  // Reinitialize agent
  await chat.value.initAgent()
  chat.value.loadStart()
}

const selectConversation = async (agent: AgentData, conversation: ConversationItem) => {
  // PC端模式下，如果已经选中了相同的会话，则忽略点击事件
  if (!isMobile.value && currentSession.value?.id === conversation.id) {
    return
  }

  currentAgent.value = agent
  currentSession.value = conversation

  // Update chat instance
  chat.value.shop_id = agent.agent_id
  chat.value.session_id = conversation.id

  // Clear current and history lists but preserve session_id
  chat.value.tags = []
  chat.value.currentList = []
  chat.value.historyList = []

  // Update URL
  await navigateTo(
    {
      path: '/chat',
      query: {
        shop_id: agent.agent_id,
        session_id: conversation.id,
      },
    },
    { replace: true }
  )

  // Close mobile drawer on selection
  if (isMobile.value) {
    showMobileDrawer.value = false
  }

  // Load conversation
  await chat.value.initAgent()
  await chat.value.loadHistory()
}

const startNewChat = async (agent: AgentData) => {
  await selectAgent(agent)
}

const updateConversationTitle = async (agent: AgentData, conversation: ConversationItem) => {
  const [err] = await try_http(`/mall/p/llm/agent/conversations/name`, {
    method: 'post',
    body: {
      name: conversation.name,
      agentId: agent.agent_id,
      user: user.value.userId,
      conversation_id: conversation.id,
    },
  })
  if (!err) {
    conversation.edit = false
  }
}

const removeConversation = (agent: AgentData, conversation: ConversationItem) => {
  Modal.confirm({
    title: '提示',
    content: '是否删除该会话?',
    onOk: async () => {
      const [err] = await try_http(`/mall/p/llm/agent/conversations/${conversation.id}`, {
        method: 'delete',
        body: {
          agentId: agent.agent_id,
          user: user.value.userId,
        },
      })
      if (!err) {
        message.success('操作成功')

        // If deleting current session, clear it
        if (currentSession.value?.id === conversation.id) {
          currentSession.value = null
          await selectAgent(agent)
        }

        // Refresh conversation list
        getConversationList()
      }
    },
  })
}

// Chat functionality
const scrollToBottom = () => {
  nextTick(() => {
    const scrollBar = document.querySelector('.scroll-bar')
    scrollBar?.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  })
}

const send = async (message: string, opts) => {
  const wasNewSession = !chat.value.session_id && isLogin.value

  chat.value.ask(message, opts)
  scrollToBottom()

  // 如果是新会话且用户已登录，监听会话创建完成
  if (wasNewSession) {
    // 等待会话创建完成
    const checkSessionCreated = () => {
      return new Promise((resolve) => {
        const originalAfterClose = chat.value.afterClose
        chat.value.afterClose = () => {
          // 恢复原来的 afterClose 回调
          chat.value.afterClose = originalAfterClose
          originalAfterClose?.()
          resolve(true)
        }
      })
    }

    // 等待会话创建完成后刷新会话列表
    await checkSessionCreated()

    if (chat.value.session_id) {
      // 刷新会话列表
      await getConversationList()

      // 自动选中新创建的会话
      const targetAgent = conversationList.value.find((agent) => agent.agent_id === chat.value.shop_id)
      if (targetAgent) {
        const newSession = targetAgent.data.find((conv) => conv.id === chat.value.session_id)
        if (newSession) {
          currentAgent.value = targetAgent
          currentSession.value = newSession
          expandedAgents.value.add(targetAgent.agent_id)

          // 更新 URL
          await navigateTo(
            {
              path: '/chat',
              query: {
                shop_id: chat.value.shop_id,
                session_id: chat.value.session_id,
              },
            },
            { replace: true }
          )
        }
      }
    }
  }
}

const recommendAsk = (_ask) => {
  chat.value.ask(_ask)
  scrollToBottom()
}

const newChat = async () => {
  if (currentAgent.value) {
    await selectAgent(currentAgent.value)
  } else {
    history.replaceState(null, '', `/chat?shop_id=${chat.value.shop_id}`)
    delete route.query.session_id
    chat.value.newChat()
    chat.value.loadStart()
  }
}

const loadhistory = async () => {
  if (session_id) {
    await chat.value.loadHistory()
  }
}

const isIframe = computed(() => {
  if (import.meta.server) return false
  return window.top !== window.self
})

// Lifecycle hooks
onMounted(async () => {
  // Check mobile on mount
  checkMobile()

  // Add resize listener
  if (import.meta.client) {
    window.addEventListener('resize', checkMobile)
  }

  // Load conversation list if user is logged in
  if (isLogin.value) {
    await getConversationList()
  }

  await chat.value.initAgent()
  await loadhistory()

  const ask = route.query.ask
  if (ask) {
    chat.value.ask(ask as string)
  } else {
    if (!session_id) {
      chat.value.loadStart()
    }
  }

  emitter.all.clear()

  emitter.on('addTag', (e) => {
    chat.value.addTag(e)
  })

  emitter.on('addForm', (e) => {
    chat.value.ask(e, { isForm: true })
  })

  emitter.on('goSku', ({ skuCode, partId }) => {
    const bomai = useRuntimeConfig().public.VITE_BOMAI_URL
    if (chat.value.shop_id == '0') {
      window.open(`${bomai}/parts/${partId}?typeCode=${skuCode}`)
    } else {
      window.open(`${bomai}/brand/${chat.value.shop_id}/${partId}?typeCode=${skuCode}`)
    }
  })

  emitter.on('login', () => {
    if (isIframe.value) {
      window.parent.postMessage(
        {
          type: 'login',
          from: 'chat',
          shop_id: chat.value.shop_id,
        },
        '*'
      )
    } else {
      navigateTo({ path: '/login', query: { redirect: route.fullPath } })
    }
  })

  if (isIframe.value) {
    window.parent.postMessage(
      {
        type: 'loaded',
        from: 'chat',
        shop_id: chat.value.shop_id,
      },
      '*'
    )
  }
})

onBeforeUnmount(() => {
  chat.value.control?.abort()
  emitter.all.clear()

  // Remove resize listener and cleanup body styles
  if (import.meta.client) {
    window.removeEventListener('resize', checkMobile)
    document.body.style.overflow = '' // Restore body scroll
  }
})

useEventListener(window, 'message', (e) => {
  const data = e.data
  if (isIframe.value && typeof data === 'object' && data.from === 'bomai' && data.shopId == chat.value.shop_id) {
    if (data.type === 'setTag') {
      chat.value.tags = data.tag ? [data.tag] : []
    }
  }
})
</script>

<style lang="less" scoped>
:deep(a) {
  color: theme('colors.primary');
}

.rotate-90 {
  transform: rotate(90deg);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  /* Ensure mobile drawer is properly positioned */
  .fixed.inset-y-0.left-0 {
    top: 0;
    bottom: 0;
  }

  /* Prevent body scroll when drawer is open */
  body.drawer-open {
    overflow: hidden;
  }
}

/* Transition animations */
.transition-transform {
  transition: transform 0.3s ease-in-out;
}

/* Ensure proper z-indexing */
.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}
</style>
