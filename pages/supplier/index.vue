<template>
  <!-- 固定背景 -->
  <div class="page-background"></div>

  <!-- 页面内容容器 -->
  <div class="page-content">
    <!-- 英雄区 -->
    <section class="supplier-hero">
      <div class="box-container">
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">成为研选工场供应商</h1>
            <p class="hero-subtitle">加入新一代人工智能供应链平台，轻松触达百万级客户</p>
            <div class="hero-actions">
              <a href="#core-services" class="btn btn-gradient btn-large">供应商服务</a>
              <a href="#premium-services" class="btn btn-outline-white btn-large">品牌商专属服务</a>
              <a href="#join-form" class="btn btn-outline-white btn-large">立即加入</a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心服务（统一服务） -->
    <section class="core-services" id="core-services">
      <div class="box-container">
        <div class="section-header">
          <h2>核心服务体系</h2>
          <p>从询价开始，全链路数字化系统支撑，更有AI营销助手大麦，自动参与平台订单的报价，自主接单。</p>
        </div>

        <div class="benefits-section">
          <!-- 基础服务 -->
          <div class="basic-services">
            <div class="benefit-cards">
              <div class="benefit-card">
                <div class="card-header">
                  <div class="benefit-icon">
                    <i class="fas i-fa-share-alt"></i>
                  </div>
                </div>
                <h3>销售渠道拓展</h3>
                <p>接入研选工场多元化销售渠道，线上线下全覆盖，利用平台优势触达百万客户资源</p>
              </div>

              <div class="benefit-card">
                <div class="card-header">
                  <div class="benefit-icon">
                    <i class="fas i-fa-robot"></i>
                  </div>
                </div>
                <h3>人工智能营销辅助</h3>
                <p>AI助手，时刻替你盯着平台的询价单，自主为你生成报价单，从平台获取订单。</p>
              </div>
            </div>
          </div>

          <!-- 小彩AI助手 - 重点介绍 -->
          <div class="xiaocai-spotlight">
            <div class="spotlight-content">
              <div class="content-left">
                <div class="spotlight-header">
                  <h3>大麦 - 供应商智能助手</h3>
                  <p class="spotlight-subtitle">专属AI助手，免费为所有入驻供应商提供智能业务辅助服务</p>
                </div>

                <div class="spotlight-features">
                  <div class="xiaocai-features">
                    <div class="feature-group">
                      <h4>核心功能</h4>
                      <div class="feature-list">
                        <div class="feature-highlight">
                          <i class="fas i-fa-calculator"></i>
                          <div class="feature-info">
                            <span class="feature-name">智能报价计算</span>
                            <span class="feature-desc">基于市场数据自动生成报价建议</span>
                          </div>
                        </div>
                        <div class="feature-highlight">
                          <i class="fas i-fa-file-invoice"></i>
                          <div class="feature-info">
                            <span class="feature-name">订单处理跟踪</span>
                            <span class="feature-desc">全流程订单状态智能监控</span>
                          </div>
                        </div>
                        <div class="feature-highlight">
                          <i class="fas i-fa-shipping-fast"></i>
                          <div class="feature-info">
                            <span class="feature-name">发货提醒管理</span>
                            <span class="feature-desc">智能发货时间节点提醒</span>
                          </div>
                        </div>
                        <div class="feature-highlight">
                          <i class="fab i-fab-weixin"></i>
                          <div class="feature-info">
                            <span class="feature-name">微信无缝对接</span>
                            <span class="feature-desc">直接连接微信生态，一键分享产品和订单信息</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="content-right">
                <div class="image-placeholder xiaocai"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 品牌商专属增值服务 -->
    <section class="premium-services-section" id="premium-services">
      <div class="box-container">
        <div class="section-header">
          <div class="premium-badge">
            <i class="fas i-fa-crown"></i>
            品牌商专属
          </div>
          <h2>品牌商专属增值服务</h2>
          <p>为品牌商量身定制的高端服务，全面提升品牌影响力与市场竞争力</p>
        </div>

        <div class="premium-grid">
          <!-- 基础品牌服务 -->
          <div class="basic-premium-services">
            <div class="premium-cards">
              <div class="premium-card highlight">
                <div class="premium-icon">
                  <i class="fas i-fa-crown"></i>
                </div>
                <div class="premium-content">
                  <h3>专属品牌展示页面</h3>
                  <p>打造独一无二的品牌形象展示空间，提升品牌知名度和市场影响力，让您的品牌在众多竞品中脱颖而出</p>
                  <ul class="premium-features">
                    <li>定制化品牌页面设计</li>
                    <li>产品展示与品牌故事</li>
                    <li>专业品牌形象管理</li>
                  </ul>
                </div>
              </div>

              <div class="premium-card">
                <div class="premium-icon">
                  <i class="fas i-fa-bullhorn"></i>
                </div>
                <div class="premium-content">
                  <h3>主动品牌推广</h3>
                  <p>平台主动向下游客户推送您的品牌信息，通过精准营销策略，扩大品牌影响力，获取更多优质客户</p>
                  <ul class="premium-features">
                    <li>首页推荐位展示</li>
                    <li>定向客户推送</li>
                    <li>营销数据分析报告</li>
                  </ul>
                </div>
              </div>

              <div class="premium-card">
                <div class="premium-icon">
                  <i class="fas i-fa-mobile-alt"></i>
                </div>
                <div class="premium-content">
                  <h3>品牌定制小程序</h3>
                  <p>为您的品牌量身打造专属小程序，提供完整的产品展示、客户管理和营销推广功能，打造私域流量池</p>
                  <ul class="premium-features">
                    <li>定制品牌小程序开发</li>
                    <li>产品展示与订购系统</li>
                    <li>营销推广功能模块</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- 大麦AI客服 - 重点介绍 -->
          <div class="damai-spotlight">
            <div class="spotlight-content">
              <div class="content-left">
                <div class="image-placeholder damai"></div>
              </div>

              <div class="content-right">
                <div class="spotlight-header">
                  <div class="ai-badge premium">
                    <i class="fas i-fa-robot"></i>
                    AI客服
                  </div>
                  <h3>大麦（专属版） - 品牌AI客服</h3>
                  <p class="spotlight-subtitle">为品牌商量身定制的AI客服机器人，打造专属品牌客服体验</p>
                </div>

                <div class="spotlight-features">
                  <div class="damai-features">
                    <div class="feature-group">
                      <h4>核心功能</h4>
                      <div class="feature-list">
                        <div class="feature-highlight">
                          <i class="fas i-fa-paint-brush"></i>
                          <div class="feature-info">
                            <span class="feature-name">自定义形象名称</span>
                            <span class="feature-desc">完全自定义客服头像、名称和话术风格</span>
                          </div>
                        </div>
                        <div class="feature-highlight">
                          <i class="fas i-fa-book"></i>
                          <div class="feature-info">
                            <span class="feature-name">专属知识库训练</span>
                            <span class="feature-desc">基于品牌产品和服务的专业知识库</span>
                          </div>
                        </div>
                        <div class="feature-highlight">
                          <i class="fas i-fa-users"></i>
                          <div class="feature-info">
                            <span class="feature-name">客户问题智能解答</span>
                            <span class="feature-desc">7×24小时专业客户咨询服务</span>
                          </div>
                        </div>
                        <div class="feature-highlight">
                          <i class="fas i-fa-search-dollar"></i>
                          <div class="feature-info">
                            <span class="feature-name">主动挖掘商机</span>
                            <span class="feature-desc">智能识别客户需求，主动推荐合适产品和解决方案</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- <div class="premium-action">
        <div class="action-content">
          <h3>查看品牌馆</h3>
          <p>了解已入驻品牌商的展示效果，感受专属品牌页面的魅力</p>
          <a href="/brand-hall" class="btn btn-gradient btn-large">进入品牌馆</a>
        </div>
      </div> -->
      </div>
    </section>

    <!-- 统一入驻流程 -->
    <section class="join-process">
      <div class="box-container">
        <div class="section-header">
          <h2>入驻流程</h2>
          <p>三步完成，快速开启合作</p>
        </div>
        <div class="process-steps">
          <div class="step-item">
            <div class="step-number">01</div>
            <div class="step-content">
              <h3>提交申请</h3>
              <p>选择供应商类型，填写基本信息</p>
            </div>
          </div>
          <div class="step-arrow">
            <i class="fas i-fa-arrow-right"></i>
          </div>
          <div class="step-item">
            <div class="step-number">02</div>
            <div class="step-content">
              <h3>资质审核</h3>
              <p>专业团队快速审核，1-2工作日</p>
            </div>
          </div>
          <div class="step-arrow">
            <i class="fas i-fa-arrow-right"></i>
          </div>
          <div class="step-item">
            <div class="step-number">03</div>
            <div class="step-content">
              <h3>开始合作</h3>
              <p>开启AI供应链之旅</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 入驻表单 -->
    <section class="join-form-section" id="join-form">
      <div class="box-container">
        <div class="form-wrapper">
          <!-- 成功状态展示 -->
          <div v-if="isSubmitSuccess" class="success-state">
            <div class="success-animation">
              <div class="success-icon">
                <div class="success-circle">
                  <div class="success-checkmark">
                    <div class="success-checkmark-stem"></div>
                    <div class="success-checkmark-kick"></div>
                  </div>
                </div>
              </div>

              <div class="success-content">
                <h2 class="success-title">申请提交成功！</h2>
                <p class="success-subtitle">感谢您的信任，我们已收到您的入驻申请</p>

                <!-- <div class="success-info">
                  <div class="info-card">
                    <h3>您的申请信息</h3>
                    <div class="info-item">
                      <span class="label">供应商类型：</span>
                      <span class="value">{{ submittedData.supplierType }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">企业名称：</span>
                      <span class="value">{{ submittedData.companyName }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">联系人：</span>
                      <span class="value">{{ submittedData.contactName }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">联系电话：</span>
                      <span class="value">{{ submittedData.phone }}</span>
                    </div>
                    <div v-if="submittedData.selectedBrands?.length > 0" class="info-item">
                      <span class="label">主营品牌：</span>
                      <div class="brands-list">
                        <span v-for="brand in submittedData.selectedBrands" :key="brand" class="brand-chip">
                          {{ brand }}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div class="next-steps">
                    <h3>接下来会发生什么？</h3>
                    <div class="steps-timeline">
                      <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                          <h4>资质审核</h4>
                          <p>我们的专业团队将在1-2个工作日内审核您的申请</p>
                        </div>
                      </div>
                      <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                          <h4>专人联系</h4>
                          <p>审核通过后，我们会通过电话联系您确认合作详情</p>
                        </div>
                      </div>
                      <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                          <h4>开始合作</h4>
                          <p>完成签约后，即可开启AI供应链之旅</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div> -->

                <div class="success-actions">
                  <button @click="goToHome" class="btn btn-gradient btn-large">
                    <i class="fas i-fa-home"></i>
                    返回首页
                  </button>
                  <!-- <button @click="backToForm" class="btn btn-outline-white btn-large">
                    <i class="fas i-fa-plus"></i>
                    继续申请
                  </button> -->
                </div>

                <!-- <div class="success-footer">
                  <p>如有疑问，请联系我们的客服团队</p>
                  <div class="contact-info">
                    <span>
                      <i class="fas i-fa-phone"></i>
                      ************
                    </span>
                    <span>
                      <i class="fas i-fa-envelope"></i>
                      <EMAIL>
                    </span>
                  </div>
                </div> -->
              </div>
            </div>
          </div>

          <!-- 原始表单 -->
          <div v-else>
            <div class="form-header">
              <h2>立即申请入驻</h2>
            </div>
            <form class="supplier-form" @submit.prevent="submitForm">
              <div class="supplier-type-select">
                <label>供应商类型 *</label>
                <div class="type-options">
                  <label v-for="type in supplierTypes" :key="type.id" class="type-option">
                    <input type="radio" :value="type.id" v-model="formData.supplierType" name="supplierType" />
                    <span class="option-content">
                      <i :class="type.icon"></i>
                      <span>{{ type.name }}</span>
                    </span>
                  </label>
                </div>
              </div>

              <div class="form-group">
                <label>企业名称 *</label>
                <div class="brand-selector" ref="companyRef">
                  <div class="search-input-wrapper">
                    <input
                      type="text"
                      v-model="companyName"
                      @input="handleCompanyFilter"
                      @focus="handleCompanyFocus"
                      placeholder="搜索并选择企业名称"
                      class="brand-search-input"
                    />
                    <i class="fas i-fa-search search-icon"></i>
                  </div>
                  <div v-if="isCompanyEdit && companyInputName" class="brand-dropdown">
                    <div v-if="isCompanySearching" class="brand-option">搜索中...</div>
                    <template v-else>
                      <div
                        v-for="item in companyList"
                        :key="item.name"
                        @click="setCompany(item)"
                        class="brand-option"
                        :class="{ selected: formData.companyName.name == item.name }"
                      >
                        {{ item.name }}
                        <i v-if="formData.companyName.name == item.name" class="fas i-fa-check"></i>
                      </div>
                      <div v-if="!companyList.length && companyInputName" class="brand-option">没有找到匹配的企业</div>
                    </template>
                  </div>
                </div>
              </div>

              <div class="form-group">
                <label>联系人姓名 *</label>
                <input type="text" v-model="formData.contactName" placeholder="请输入联系人姓名" />
              </div>

              <div class="form-group">
                <label>联系电话 *</label>
                <input type="tel" v-model="formData.phone" placeholder="请输入联系电话" />
              </div>

              <div class="form-group" v-if="formData.supplierType == 1">
                <label>主营品牌 *</label>
                <div class="brand-selector" ref="brandRef">
                  <div class="selected-brands" v-if="formData.selectedBrands.length > 0">
                    <span v-for="brand in formData.selectedBrands" :key="brand" class="brand-tag">
                      {{ brand.name }}
                      <i class="fas i-fa-times" @click="removeBrand(brand)"></i>
                    </span>
                  </div>
                  <div class="search-input-wrapper">
                    <input
                      type="text"
                      v-model="brandSearchQuery"
                      @input="filterBrands"
                      @focus="showBrandDropdown = true"
                      placeholder="搜索并选择品牌"
                      class="brand-search-input"
                    />
                    <i class="fas i-fa-search search-icon"></i>
                  </div>
                  <div v-if="showBrandDropdown" class="brand-dropdown">
                    <div v-if="isBrandSearching" class="brand-option">搜索中...</div>
                    <template v-else>
                      <div
                        v-if="filteredBrands.length > 0"
                        v-for="brand in filteredBrands"
                        :key="brand.brandId"
                        @click="selectBrand(brand, formData.supplierType == 3 ? 0 : 1)"
                        class="brand-option"
                        :class="{ selected: brandSet.has(brand.name) }"
                      >
                        {{ brand.name }}
                        <i v-if="brandSet.has(brand.name)" class="fas i-fa-check"></i>
                      </div>
                      <div v-else class="brand-option">没有找到匹配的品牌</div>
                    </template>
                  </div>
                </div>
              </div>

              <div class="form-group" v-if="formData.supplierType == 3">
                <label>主营品牌 *</label>
                <div class="brand-selector" ref="suggestRef">
                  <div class="search-input-wrapper">
                    <input
                      v-model="formData.brandName"
                      placeholder="请输入品牌"
                      @input="filterBrands"
                      @focus="showBrandSuggestion = true"
                    />
                    <div
                      v-if="showBrandSuggestion && has(formData.brandName) && filteredBrands.length"
                      class="brand-dropdown"
                    >
                      <div
                        v-for="brand in filteredBrands"
                        :key="brand.brandId"
                        @click="selectBrand(brand, formData.supplierType == 3 ? 0 : 1)"
                        class="brand-option"
                        :class="{ selected: brandSet.has(brand.name) }"
                      >
                        {{ brand.name }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- <div class="form-group premium-service-checkbox" v-if="formData.supplierType === 'brand'">
            <label class="checkbox-label">
              <input type="checkbox" v-model="formData.interestedInPremium">
              <span class="checkmark"></span>
              我对品牌专属增值服务感兴趣，希望获得详细介绍
            </label>
          </div> -->

              <!-- 协议和注册确认 -->
              <div class="form-group agreement-checkboxes">
                <label class="checkbox-label">
                  <input type="checkbox" v-model="formData.agreeTerms" />
                  <span class="checkmark"></span>
                  同意
                  <NuxtLink
                    class="text-primary! agreement-link"
                    :to="{ path: '/agreement', query: { key: 'termOfService' } }"
                    target="_blank"
                  >
                    用户协议
                  </NuxtLink>
                  、
                  <NuxtLink
                    class="text-primary! agreement-link"
                    :to="{ path: '/agreement', query: { key: 'privacyPolicy' } }"
                    target="_blank"
                  >
                    隐私政策
                  </NuxtLink>
                  与
                  <NuxtLink
                    class="text-primary! agreement-link"
                    :to="{ path: '/agreement', query: { key: 'disclaimer' } }"
                    target="_blank"
                  >
                    免责声明
                  </NuxtLink>
                </label>
                <label class="checkbox-label disabled">
                  <input type="checkbox" v-model="formData.autoRegister" required disabled checked />
                  <span class="checkmark"></span>
                  提交申请自动成为研选工场会员
                </label>
              </div>

              <div class="form-actions">
                <button type="submit" class="btn btn-gradient btn-large">提交申请</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </section>
  </div>
  <!-- 结束页面内容容器 -->
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'

definePageMeta({
  layout: 'page',
})

const supplierTypes = ref([
  {
    id: '3',
    name: '品牌商',
    desc: '品牌推广与市场拓展',
    icon: 'fas i-fa-crown',
  },
  {
    id: '1',
    name: '贸易商',
    desc: '快速周转、渠道整合',
    icon: 'fas i-fa-exchange-alt',
  },
  {
    id: '2',
    name: '加工商',
    desc: '产能提升与定制服务',
    icon: 'fas i-fa-cogs',
  },
])

const filteredBrands = ref([])
const brandSearchQuery = ref('')
const showBrandDropdown = ref(false)
const isBrandSearching = ref(false)

const formData = ref({
  supplierType: '3',
  companyName: {},
  contactName: '',
  phone: '',
  selectedBrands: [],
  interestedInPremium: false,
  agreeTerms: false,
  autoRegister: true,
  brandName: '',
  brandId: null,
})

// 添加成功状态管理
const isSubmitSuccess = ref(false)
const submittedData = ref({})

const brandSet = computed(() => new Set(formData.value.selectedBrands.map((item) => item.name)))

const companyRef = ref()
const companyInputName = ref('')
const isCompanyEdit = ref(false)
const isCompanySearching = ref(false)
const companyName = computed({
  get() {
    return isCompanyEdit.value ? companyInputName.value : formData.value.companyName.name
  },
  set(val) {
    companyInputName.value = val
  },
})

let fetchId = 0
const fetchCompany = useDebounceFn(async () => {
  isCompanySearching.value = true
  fetchId++
  const current = fetchId
  try {
    const res = await $fetch('/bussiness', {
      method: 'post',
      body: {
        word: companyInputName.value,
      },
    })
    if (fetchId == current) {
      companyList.value = res.result?.items || []
    }
  } finally {
    isCompanySearching.value = false
  }
}, 500)
const companyList = ref([])
const handleCompanyFocus = () => {
  isCompanyEdit.value = true
}
const handleCompanyFilter = () => {
  if (companyName.value.trim() === '') {
    companyList.value = []
  } else {
    fetchCompany()
  }
}
const afterCompanySelect = () => {
  isCompanyEdit.value = false
  companyInputName.value = ''
}
onClickOutside(companyRef, () => {
  afterCompanySelect()
})
const setCompany = (company) => {
  formData.value.companyName = company
  afterCompanySelect()
}

const brandRef = ref()
onClickOutside(brandRef, () => {
  showBrandDropdown.value = false
})
let count = 0
const fetchBrands = useDebounceFn(async (name) => {
  isBrandSearching.value = true
  count++
  const current = count
  const [err, res] = await try_http('/mall/brand/list', {
    query: {
      name,
    },
  })
  if (err) return
  if (current == count) {
    filteredBrands.value = res.data || []
  }
  isBrandSearching.value = false
}, 300)

const filterBrands = (e) => {
  const val = e.target.value
  if (val.trim() === '') {
    fetchBrands('')
  } else {
    fetchBrands(val)
  }
  showBrandDropdown.value = true
}

const showBrandSuggestion = ref(false)

// mode 0 单选 1 多选
const selectBrand = (brand, mode = 1) => {
  if (mode == 1) {
    const idx = formData.value.selectedBrands.findIndex((item) => item.name == brand.name)
    if (idx > -1) {
      formData.value.selectedBrands.splice(idx, 1)
    } else {
      formData.value.selectedBrands.push(brand)
    }
  } else {
    formData.value.brandName = brand.name
    formData.value.brandId = brand.brandId
    showBrandSuggestion.value = false
  }
}

const suggestRef = ref()
onClickOutside(suggestRef, () => {
  showBrandSuggestion.value = false
})

const removeBrand = (brand) => {
  const index = formData.value.selectedBrands.indexOf(brand)
  if (index > -1) {
    formData.value.selectedBrands.splice(index, 1)
  }
}

const submitForm = async () => {
  // 表单提交逻辑
  if (!formData.value.companyName.name) {
    message.error('请输入企业名称')
    return
  }
  if (!formData.value.contactName) {
    message.error('请输入联系人姓名')
    return
  }
  if (!formData.value.phone) {
    message.error('请输入联系电话')
    return
  }
  if (eq(formData.value.supplierType, 1) && !formData.value.selectedBrands.length) {
    message.error('请选择主营品牌')
    return
  }
  if (eq(formData.value.supplierType, 3) && !formData.value.brandName) {
    message.error('请选择主营品牌')
    return
  }
  if (!formData.value.agreeTerms) {
    message.error('请同意用户协议、隐私政策与免责声明')
    return
  }

  const { supplierType, companyName, contactName, phone, selectedBrands } = formData.value
  const data = {
    type: 2,
    supplierType,
    adminUserName: contactName,
    adminUserMobile: phone,
    creditCode: companyName.creditCode,
    merchantName: companyName.name,
  }
  if (supplierType == 1) {
    data.brandCategorySignings = selectedBrands.map((item) => {
      return {
        brandName: item.name,
        brandId: item.brandId,
      }
    })
  } else if (supplierType == 3) {
    const brand = { brandName: formData.value.brandName }
    if (formData.value.brandId) {
      brand.brandId = formData.value.brandId
    }
    data.brandCategorySignings = [brand]
  }

  const [err] = await try_http('/mall/p/shopCompany/naked/register', {
    method: 'post',
    body: data,
  })
  if (err) return

  // 显示成功状态
  isSubmitSuccess.value = true

  // 清空表单数据
  resetForm()
}

// 重置表单
const resetForm = () => {
  formData.value = {
    supplierType: '3',
    companyName: {},
    contactName: '',
    phone: '',
    selectedBrands: [],
    interestedInPremium: false,
    agreeTerms: false,
    autoRegister: true,
  }
  brandSearchQuery.value = ''
  companyInputName.value = ''
  showBrandDropdown.value = false
  isCompanyEdit.value = false
}

// 返回表单
const backToForm = () => {
  isSubmitSuccess.value = false
  submittedData.value = {}
}

// 前往首页
const goToHome = () => {
  navigateTo('/')
}

watch(
  () => formData.value.supplierType,
  (val) => {
    if (val == 1) {
      fetchBrands()
    }
  }
)
</script>

<style lang="less" scoped>
// 供应商页面样式
// 使用指定的配色方案：#1a1a2b(深蓝), #f94c30(主色), #ffca46(辅色), 黑白灰
.box-container {
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;
  padding: 0 80px;
}

@media (max-width: 1200px) {
  .box-container {
    padding: 0 40px;
  }

  .section-title h2 {
    font-size: 32px;
  }

  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 992px) {
  .box-container {
    padding: 0 30px;
  }

  .section-title h2 {
    font-size: 28px;
  }

  .section-title p {
    font-size: 16px;
  }

  .btn {
    padding: 12px 20px;
    font-size: 15px;
  }

  .btn-big {
    padding: 15px 25px;
    font-size: 16px;
  }

  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .grid-2 {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .box-container {
    padding: 0 20px;
  }

  .section-title h2 {
    font-size: 24px;
  }

  .section-title p {
    font-size: 15px;
  }

  .btn {
    padding: 10px 18px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .box-container {
    padding: 0 15px;
  }

  .section-title h2 {
    font-size: 22px;
  }

  .section-title p {
    font-size: 14px;
  }
}

/* 固定背景 */
.page-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #1a1a2b 0%, #2a2a3d 100%);
  z-index: -1;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(249, 76, 48, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 202, 70, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(249, 76, 48, 0.08) 0%, transparent 50%);
    pointer-events: none;
  }
}

/* 页面内容容器 */
.page-content {
  position: relative;
  z-index: 1;
}

/* 供应商英雄区 */
.supplier-hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  color: #ffffff;
  overflow: hidden;
  background: transparent;

  .box-container {
    position: relative;
    z-index: 2;
  }

  .hero-content {
    text-align: center;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 10;
  }

  .hero-title {
    font-size: 56px;
    font-weight: 700;
    margin-bottom: 24px;
    line-height: 1.2;
    background: linear-gradient(135deg, #ffffff 0%, #ffca46 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .hero-subtitle {
    font-size: 20px;
    margin-bottom: 60px;
    color: rgba(255, 255, 255, 0.8);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .hero-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
  }
}

/* 核心服务区 - 统一背景样式 */
.core-services {
  padding: 80px 0;
  background: transparent;
  color: #ffffff;
  position: relative;

  .box-container {
    position: relative;
    z-index: 2;
  }

  .section-header {
    text-align: center;
    margin-bottom: 60px;

    h2 {
      font-size: 42px;
      color: #ffffff;
      margin-bottom: 16px;
      font-weight: 700;
      background: linear-gradient(135deg, #ffffff 0%, #ffca46 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    p {
      font-size: 18px;
      color: rgba(255, 255, 255, 0.8);
      max-width: 600px;
      margin: 0 auto;
    }
  }
}

/* 品牌商专属增值服务区 */
.premium-services-section {
  padding: 80px 0;
  background: transparent;
  position: relative;
  overflow: hidden;

  .section-header {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 2;

    .premium-badge {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
      color: #ffffff;
      padding: 8px 20px;
      border-radius: 25px;
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 20px;
      box-shadow: 0 5px 15px rgba(249, 76, 48, 0.3);

      i {
        font-size: 16px;
      }
    }

    h2 {
      font-size: 42px;
      color: #ffffff;
      margin-bottom: 16px;
      font-weight: 700;
      background: linear-gradient(135deg, #ffffff 0%, #ffca46 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    p {
      font-size: 18px;
      color: rgba(255, 255, 255, 0.8);
      max-width: 800px;
      margin: 0 auto;
      line-height: 1.6;
    }
  }

  .premium-grid {
    position: relative;
    z-index: 2;

    .basic-premium-services {
      margin-bottom: 60px;

      .premium-cards {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;
      }
    }
  }

  .premium-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 25px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
    }

    &.highlight {
      transform: translateY(-5px);
      box-shadow: 0 30px 80px rgba(0, 0, 0, 0.4), 0 0 0 2px rgba(249, 76, 48, 0.5);
      background: rgba(255, 255, 255, 0.15);
    }

    &:hover {
      transform: translateY(-10px);
      box-shadow: 0 30px 80px rgba(0, 0, 0, 0.4), 0 0 0 2px rgba(249, 76, 48, 0.5);
      background: rgba(255, 255, 255, 0.15);
      border-color: rgba(249, 76, 48, 0.5);
    }

    .premium-icon {
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 25px;
      box-shadow: 0 10px 25px rgba(249, 76, 48, 0.3);

      i {
        font-size: 32px;
        color: #ffffff;
      }
    }

    .premium-content {
      h3 {
        font-size: 24px;
        color: #ffffff;
        margin-bottom: 15px;
        font-weight: 700;
      }

      p {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.6;
        margin-bottom: 20px;
      }

      .premium-features {
        list-style: none;
        padding: 0;
        margin-bottom: 25px;

        li {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: 8px;
          position: relative;
          padding-left: 20px;

          &::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #ffca46;
            font-weight: bold;
          }
        }
      }
    }
  }

  .premium-action {
    margin-top: 60px;
    text-align: center;
    position: relative;
    z-index: 2;

    .action-content {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-radius: 25px;
      padding: 50px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      max-width: 600px;
      margin: 0 auto;
      border: 1px solid rgba(255, 255, 255, 0.2);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
      }

      h3 {
        font-size: 28px;
        color: #ffffff;
        margin-bottom: 15px;
        font-weight: 700;
        background: linear-gradient(135deg, #ffffff 0%, #ffca46 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      p {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 30px;
        line-height: 1.6;
      }
    }
  }

  // 大麦AI客服重点展示
  .damai-spotlight {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border-radius: 30px;
    padding: 50px 40px;
    border: 2px solid rgba(249, 76, 48, 0.3);
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 6px;
      background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
    }

    .spotlight-header {
      text-align: left;
      margin-bottom: 30px;

      .ai-badge {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
        color: #ffffff;
        padding: 8px 20px;
        border-radius: 25px;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(249, 76, 48, 0.3);

        &.premium {
          background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
          box-shadow: 0 8px 25px rgba(249, 76, 48, 0.4);
        }

        i {
          font-size: 16px;
        }
      }

      h3 {
        font-size: 32px;
        color: #ffffff;
        margin-bottom: 15px;
        font-weight: 700;
        background: linear-gradient(135deg, #ffffff 0%, #ffca46 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .spotlight-subtitle {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.6;
        margin: 0;
      }
    }

    .spotlight-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 40px;
      align-items: stretch;
      min-height: 400px;

      .content-left {
        display: flex;
        align-items: stretch;
        justify-content: center;

        .image-placeholder {
          width: 100%;
          height: 100%;
          background: rgba(255, 255, 255, 0.05);
          border: 2px dashed rgba(249, 76, 48, 0.4);
          border-radius: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          &:hover {
            border-color: rgba(249, 76, 48, 0.6);
            background: rgba(255, 255, 255, 0.08);
          }

          .placeholder-content {
            text-align: center;
            color: rgba(255, 255, 255, 0.6);

            i {
              font-size: 48px;
              margin-bottom: 15px;
              display: block;
              color: rgba(249, 76, 48, 0.6);
            }

            p {
              font-size: 16px;
              margin: 0;
              font-weight: 500;
            }
          }
        }
      }

      .content-right {
        display: flex;
        flex-direction: column;
      }
    }

    .spotlight-character {
      .character-display {
        text-align: center;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 20px;
        padding: 30px 20px;
        border: 1px solid rgba(255, 255, 255, 0.1);

        .character-avatar {
          width: 120px;
          height: 120px;
          margin: 0 auto 20px;
          position: relative;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
            border: 4px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
          }

          .character-ring {
            position: absolute;
            top: -8px;
            left: -8px;
            width: 136px;
            height: 136px;
            border: 3px solid #f94c30;
            border-radius: 50%;
            border-top-color: transparent;
            border-right-color: transparent;
            animation: spin 4s linear infinite;

            &.premium {
              border-color: #ffca46;
              border-top-color: transparent;
              border-right-color: transparent;
              animation: spin 3s linear infinite reverse;
            }
          }

          .character-glow {
            position: absolute;
            top: -12px;
            left: -12px;
            width: 144px;
            height: 144px;
            background: radial-gradient(circle, rgba(249, 76, 48, 0.2) 0%, transparent 70%);
            border-radius: 50%;
            animation: pulse-glow 3s ease-in-out infinite;

            &.premium {
              background: radial-gradient(circle, rgba(255, 202, 70, 0.3) 0%, transparent 70%);
            }
          }

          &.xiaocai-character:hover img {
            transform: scale(1.05);
            border-color: #f94c30;
            box-shadow: 0 0 30px rgba(249, 76, 48, 0.6);
          }

          &.damai-character:hover img {
            transform: scale(1.05);
            border-color: #ffca46;
            box-shadow: 0 0 30px rgba(255, 202, 70, 0.6);
          }

          .premium-crown {
            position: absolute;
            top: -10px;
            right: -10px;
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #ffca46 0%, #f94c30 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: float 2s ease-in-out infinite;
            box-shadow: 0 5px 15px rgba(255, 202, 70, 0.5);

            i {
              font-size: 14px;
              color: #ffffff;
            }
          }
        }

        .character-info {
          h4 {
            font-size: 20px;
            color: #ffffff;
            margin-bottom: 8px;
            font-weight: 700;
          }

          .character-desc {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 15px;
            line-height: 1.4;
          }

          .character-tags {
            display: flex;
            gap: 8px;
            justify-content: center;
            flex-wrap: wrap;

            .char-tag {
              font-size: 10px;
              background: rgba(249, 76, 48, 0.2);
              color: #f94c30;
              padding: 4px 10px;
              border-radius: 12px;
              font-weight: 600;
              border: 1px solid rgba(249, 76, 48, 0.3);
              transition: all 0.3s ease;

              &:hover {
                background: rgba(249, 76, 48, 0.3);
                transform: translateY(-1px);
              }
            }

            &.premium .char-tag {
              background: rgba(255, 202, 70, 0.2);
              color: #ffca46;
              border-color: rgba(255, 202, 70, 0.3);

              &:hover {
                background: rgba(255, 202, 70, 0.3);
              }
            }
          }
        }
      }
    }

    .spotlight-features {
      .damai-features {
        .feature-group {
          margin-bottom: 40px;

          h4 {
            font-size: 20px;
            color: #ffffff;
            margin-bottom: 25px;
            font-weight: 600;
          }

          .feature-list {
            display: flex;
            flex-direction: column;
            gap: 20px;

            .feature-highlight {
              display: flex;
              align-items: flex-start;
              gap: 15px;
              padding: 20px;
              background: rgba(249, 76, 48, 0.1);
              border-radius: 15px;
              border: 1px solid rgba(249, 76, 48, 0.3);
              transition: all 0.3s ease;

              &:hover {
                background: rgba(249, 76, 48, 0.15);
                transform: translateX(5px);
                border-color: rgba(249, 76, 48, 0.5);
              }

              i {
                font-size: 20px;
                color: #f94c30;
                flex-shrink: 0;
                margin-top: 2px;
              }

              .feature-info {
                .feature-name {
                  display: block;
                  font-size: 16px;
                  color: #ffffff;
                  font-weight: 600;
                  margin-bottom: 5px;
                }

                .feature-desc {
                  font-size: 14px;
                  color: rgba(255, 255, 255, 0.8);
                  line-height: 1.4;
                }
              }
            }
          }
        }

        .damai-metrics {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 20px;

          .metric-item {
            text-align: center;
            padding: 20px 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;

            &:hover {
              background: rgba(249, 76, 48, 0.1);
              transform: translateY(-5px);
              border-color: rgba(249, 76, 48, 0.3);
            }

            .metric-value {
              display: block;
              font-size: 24px;
              font-weight: 700;
              color: #ffca46;
              margin-bottom: 8px;
            }

            .metric-label {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.8);
              line-height: 1.3;
            }
          }
        }
      }
    }

    .spotlight-demo {
      .damai-demo {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        padding: 25px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

        .demo-header {
          display: flex;
          align-items: flex-start;
          gap: 15px;
          margin-bottom: 20px;
          padding-bottom: 20px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.2);
          position: relative;

          .demo-avatar {
            width: 60px;
            height: 60px;
            position: relative;
            flex-shrink: 0;

            &.customizable {
              &::after {
                content: '✨';
                position: absolute;
                top: -5px;
                right: -5px;
                font-size: 16px;
                z-index: 5;
                animation: float 2s ease-in-out infinite;
              }
            }

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 50%;
              position: relative;
              z-index: 3;
              border: 3px solid rgba(255, 255, 255, 0.2);
              transition: all 0.3s ease;
            }

            .avatar-ring {
              position: absolute;
              top: -5px;
              left: -5px;
              width: 70px;
              height: 70px;
              border: 2px solid #f94c30;
              border-radius: 50%;
              border-top-color: transparent;
              border-right-color: transparent;
              animation: spin 3s linear infinite;
              z-index: 1;

              &.premium {
                border-color: #ffca46;
                border-top-color: transparent;
                border-right-color: transparent;
                animation: spin 2s linear infinite reverse;
              }
            }

            .avatar-glow {
              position: absolute;
              top: -8px;
              left: -8px;
              width: 76px;
              height: 76px;
              background: radial-gradient(circle, rgba(249, 76, 48, 0.3) 0%, transparent 70%);
              border-radius: 50%;
              animation: pulse-glow 2s ease-in-out infinite;
              z-index: 0;

              &.premium {
                background: radial-gradient(circle, rgba(255, 202, 70, 0.4) 0%, transparent 70%);
                animation: pulse-glow 1.5s ease-in-out infinite;
              }
            }

            &.xiaocai-avatar:hover img {
              transform: scale(1.1);
              border-color: #f94c30;
              box-shadow: 0 0 20px rgba(249, 76, 48, 0.6);
            }

            &.damai-avatar:hover img {
              transform: scale(1.1);
              border-color: #ffca46;
              box-shadow: 0 0 20px rgba(255, 202, 70, 0.6);
            }

            .premium-crown {
              position: absolute;
              top: -8px;
              right: -8px;
              width: 24px;
              height: 24px;
              background: linear-gradient(135deg, #ffca46 0%, #f94c30 100%);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              z-index: 4;
              animation: float 2s ease-in-out infinite;
              box-shadow: 0 3px 10px rgba(255, 202, 70, 0.5);

              i {
                font-size: 12px;
                color: #ffffff;
              }
            }

            i {
              font-size: 24px;
              color: #ffffff;
            }
          }

          .demo-info {
            flex: 1;

            .info-content {
              h5 {
                font-size: 18px;
                color: #ffffff;
                margin-bottom: 8px;
                font-weight: 700;
                display: flex;
                align-items: center;
                gap: 8px;

                .ai-badge-mini {
                  font-size: 10px;
                  background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
                  color: #ffffff;
                  padding: 2px 6px;
                  border-radius: 8px;
                  font-weight: 600;
                  animation: glow-pulse 2s ease-in-out infinite;
                }

                .custom-tag {
                  font-size: 10px;
                  background: rgba(255, 202, 70, 0.2);
                  color: #ffca46;
                  padding: 3px 8px;
                  border-radius: 10px;
                  font-weight: 600;
                  border: 1px solid rgba(255, 202, 70, 0.3);

                  &.premium {
                    background: linear-gradient(135deg, #ffca46 0%, #f94c30 100%);
                    color: #ffffff;
                    animation: premium-shine 3s ease-in-out infinite;
                    box-shadow: 0 2px 8px rgba(255, 202, 70, 0.4);
                  }
                }
              }

              .status-line {
                display: flex;
                flex-direction: column;
                gap: 4px;
                margin-bottom: 10px;

                .demo-status {
                  font-size: 12px;
                  color: #2ecc71;
                  font-weight: 600;
                  display: flex;
                  align-items: center;
                  gap: 5px;

                  &.premium {
                    color: #ffca46;
                  }

                  .pulse {
                    animation: status-pulse 1.5s ease-in-out infinite;
                  }

                  .sparkle {
                    animation: sparkle-rotate 2s linear infinite;
                    color: #ffca46;
                  }
                }

                .typing-indicator {
                  font-size: 10px;
                  color: rgba(255, 255, 255, 0.6);
                  font-style: italic;
                  animation: typing-dots 1.5s ease-in-out infinite;
                }

                .brand-indicator {
                  font-size: 10px;
                  color: rgba(255, 202, 70, 0.8);
                  font-weight: 500;
                  background: rgba(255, 202, 70, 0.1);
                  padding: 2px 6px;
                  border-radius: 6px;
                  border: 1px solid rgba(255, 202, 70, 0.2);
                }
              }

              .capability-tags {
                display: flex;
                gap: 6px;
                flex-wrap: wrap;

                .tag {
                  font-size: 9px;
                  background: rgba(249, 76, 48, 0.2);
                  color: #f94c30;
                  padding: 3px 8px;
                  border-radius: 12px;
                  font-weight: 500;
                  border: 1px solid rgba(249, 76, 48, 0.3);
                  transition: all 0.3s ease;

                  &:hover {
                    background: rgba(249, 76, 48, 0.3);
                    transform: translateY(-1px);
                  }
                }

                &.premium .tag {
                  background: rgba(255, 202, 70, 0.2);
                  color: #ffca46;
                  border-color: rgba(255, 202, 70, 0.3);

                  &:hover {
                    background: rgba(255, 202, 70, 0.3);
                    transform: translateY(-1px);
                  }
                }
              }
            }
          }
        }

        .demo-chat {
          margin-bottom: 20px;

          .chat-bubble {
            margin-bottom: 12px;
            padding: 12px 16px;
            border-radius: 15px;
            max-width: 90%;

            &.ai-bubble {
              &.damai-bubble {
                background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
                color: #ffffff;
                margin-right: auto;
                border-bottom-left-radius: 4px;

                p {
                  margin: 0;
                  font-size: 13px;
                  line-height: 1.4;
                }
              }
            }

            &.user-bubble {
              background: rgba(255, 255, 255, 0.2);
              color: #ffffff;
              margin-left: auto;
              border-bottom-right-radius: 4px;

              p {
                margin: 0;
                font-size: 13px;
              }
            }
          }
        }

        .demo-actions {
          text-align: center;

          .demo-btn {
            background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
            color: #ffffff;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;

            &.premium {
              background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
              box-shadow: 0 5px 15px rgba(249, 76, 48, 0.3);
            }

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(249, 76, 48, 0.4);
            }

            i {
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

/* 优势卡片 - 核心服务样式 */
.benefits-section {
  margin-bottom: 80px;

  .basic-services {
    margin-bottom: 60px;

    .benefit-cards {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 30px;
    }
  }

  .benefit-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
    }

    &:hover {
      transform: translateY(-10px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
      border-color: rgba(249, 76, 48, 0.5);
      background: rgba(255, 255, 255, 0.15);
    }

    .card-header {
      position: relative;
      margin-bottom: 20px;
    }

    .benefit-icon {
      width: 80px;
      height: 80px;
      margin: 0 auto 20px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);

      i {
        font-size: 32px;
        color: #ffffff;
      }
    }

    h3 {
      font-size: 22px;
      color: #ffffff;
      margin-bottom: 12px;
      font-weight: 600;
    }

    p {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.6;
      margin-bottom: 20px;
    }

    .benefit-metrics {
      .metric {
        background: rgba(249, 76, 48, 0.2);
        color: #ffca46;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 600;
        display: inline-block;
        border: 1px solid rgba(255, 202, 70, 0.3);
      }
    }
  }

  // 小彩AI助手重点展示
  .xiaocai-spotlight {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border-radius: 30px;
    padding: 50px 40px;
    border: 2px solid rgba(249, 76, 48, 0.3);
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 6px;
      background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
    }

    .spotlight-header {
      text-align: left;
      margin-bottom: 30px;

      .ai-badge {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
        color: #ffffff;
        padding: 8px 20px;
        border-radius: 25px;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(249, 76, 48, 0.3);

        i {
          font-size: 16px;
        }
      }

      h3 {
        font-size: 32px;
        color: #ffffff;
        margin-bottom: 15px;
        font-weight: 700;
        background: linear-gradient(135deg, #ffffff 0%, #ffca46 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .spotlight-subtitle {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.6;
        margin: 0;
      }
    }

    .spotlight-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 40px;
      align-items: stretch;
      min-height: 400px;

      .content-left {
        display: flex;
        flex-direction: column;
      }

      .content-right {
        display: flex;
        align-items: stretch;
        justify-content: center;

        .image-placeholder {
          width: 100%;
          height: 100%;
          background: rgba(255, 255, 255, 0.05);
          border: 2px dashed rgba(249, 76, 48, 0.4);
          border-radius: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          &:hover {
            border-color: rgba(249, 76, 48, 0.6);
            background: rgba(255, 255, 255, 0.08);
          }

          .placeholder-content {
            text-align: center;
            color: rgba(255, 255, 255, 0.6);

            i {
              font-size: 48px;
              margin-bottom: 15px;
              display: block;
              color: rgba(249, 76, 48, 0.6);
            }

            p {
              font-size: 16px;
              margin: 0;
              font-weight: 500;
            }
          }
        }
      }
    }

    .spotlight-character {
      .character-display {
        text-align: center;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 20px;
        padding: 30px 20px;
        border: 1px solid rgba(255, 255, 255, 0.1);

        .character-avatar {
          width: 120px;
          height: 120px;
          margin: 0 auto 20px;
          position: relative;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
            border: 4px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
          }

          .character-ring {
            position: absolute;
            top: -8px;
            left: -8px;
            width: 136px;
            height: 136px;
            border: 3px solid #f94c30;
            border-radius: 50%;
            border-top-color: transparent;
            border-right-color: transparent;
            animation: spin 4s linear infinite;

            &.premium {
              border-color: #ffca46;
              border-top-color: transparent;
              border-right-color: transparent;
              animation: spin 3s linear infinite reverse;
            }
          }

          .character-glow {
            position: absolute;
            top: -12px;
            left: -12px;
            width: 144px;
            height: 144px;
            background: radial-gradient(circle, rgba(249, 76, 48, 0.2) 0%, transparent 70%);
            border-radius: 50%;
            animation: pulse-glow 3s ease-in-out infinite;

            &.premium {
              background: radial-gradient(circle, rgba(255, 202, 70, 0.3) 0%, transparent 70%);
            }
          }

          &.xiaocai-character:hover img {
            transform: scale(1.05);
            border-color: #f94c30;
            box-shadow: 0 0 30px rgba(249, 76, 48, 0.6);
          }

          &.damai-character:hover img {
            transform: scale(1.05);
            border-color: #ffca46;
            box-shadow: 0 0 30px rgba(255, 202, 70, 0.6);
          }

          .premium-crown {
            position: absolute;
            top: -10px;
            right: -10px;
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #ffca46 0%, #f94c30 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: float 2s ease-in-out infinite;
            box-shadow: 0 5px 15px rgba(255, 202, 70, 0.5);

            i {
              font-size: 14px;
              color: #ffffff;
            }
          }
        }

        .character-info {
          h4 {
            font-size: 20px;
            color: #ffffff;
            margin-bottom: 8px;
            font-weight: 700;
          }

          .character-desc {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 15px;
            line-height: 1.4;
          }

          .character-tags {
            display: flex;
            gap: 8px;
            justify-content: center;
            flex-wrap: wrap;

            .char-tag {
              font-size: 10px;
              background: rgba(249, 76, 48, 0.2);
              color: #f94c30;
              padding: 4px 10px;
              border-radius: 12px;
              font-weight: 600;
              border: 1px solid rgba(249, 76, 48, 0.3);
              transition: all 0.3s ease;

              &:hover {
                background: rgba(249, 76, 48, 0.3);
                transform: translateY(-1px);
              }
            }

            &.premium .char-tag {
              background: rgba(255, 202, 70, 0.2);
              color: #ffca46;
              border-color: rgba(255, 202, 70, 0.3);

              &:hover {
                background: rgba(255, 202, 70, 0.3);
              }
            }
          }
        }
      }
    }

    .spotlight-features {
      .xiaocai-features {
        .feature-group {
          margin-bottom: 40px;

          h4 {
            font-size: 20px;
            color: #ffffff;
            margin-bottom: 25px;
            font-weight: 600;
          }

          .feature-list {
            display: flex;
            flex-direction: column;
            gap: 20px;

            .feature-highlight {
              display: flex;
              align-items: flex-start;
              gap: 15px;
              padding: 20px;
              background: rgba(249, 76, 48, 0.1);
              border-radius: 15px;
              border: 1px solid rgba(249, 76, 48, 0.3);
              transition: all 0.3s ease;

              &:hover {
                background: rgba(249, 76, 48, 0.15);
                transform: translateX(5px);
                border-color: rgba(249, 76, 48, 0.5);
              }

              i {
                font-size: 20px;
                color: #f94c30;
                flex-shrink: 0;
                margin-top: 2px;
              }

              .feature-info {
                .feature-name {
                  display: block;
                  font-size: 16px;
                  color: #ffffff;
                  font-weight: 600;
                  margin-bottom: 5px;
                }

                .feature-desc {
                  font-size: 14px;
                  color: rgba(255, 255, 255, 0.8);
                  line-height: 1.4;
                }
              }
            }
          }
        }

        .xiaocai-metrics {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 20px;

          .metric-item {
            text-align: center;
            padding: 20px 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;

            &:hover {
              background: rgba(249, 76, 48, 0.1);
              transform: translateY(-5px);
              border-color: rgba(249, 76, 48, 0.3);
            }

            .metric-value {
              display: block;
              font-size: 24px;
              font-weight: 700;
              color: #ffca46;
              margin-bottom: 8px;
            }

            .metric-label {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.8);
              line-height: 1.3;
            }
          }
        }
      }
    }

    .spotlight-demo {
      .xiaocai-demo {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        padding: 25px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

        .demo-header {
          display: flex;
          align-items: flex-start;
          gap: 15px;
          margin-bottom: 20px;
          padding-bottom: 20px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.2);
          position: relative;

          .demo-avatar {
            width: 60px;
            height: 60px;
            position: relative;
            flex-shrink: 0;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 50%;
              position: relative;
              z-index: 3;
              border: 3px solid rgba(255, 255, 255, 0.2);
              transition: all 0.3s ease;
            }

            .avatar-ring {
              position: absolute;
              top: -5px;
              left: -5px;
              width: 70px;
              height: 70px;
              border: 2px solid #f94c30;
              border-radius: 50%;
              border-top-color: transparent;
              border-right-color: transparent;
              animation: spin 3s linear infinite;
              z-index: 1;

              &.premium {
                border-color: #ffca46;
                border-top-color: transparent;
                border-right-color: transparent;
                animation: spin 2s linear infinite reverse;
              }
            }

            .avatar-glow {
              position: absolute;
              top: -8px;
              left: -8px;
              width: 76px;
              height: 76px;
              background: radial-gradient(circle, rgba(249, 76, 48, 0.3) 0%, transparent 70%);
              border-radius: 50%;
              animation: pulse-glow 2s ease-in-out infinite;
              z-index: 0;

              &.premium {
                background: radial-gradient(circle, rgba(255, 202, 70, 0.4) 0%, transparent 70%);
                animation: pulse-glow 1.5s ease-in-out infinite;
              }
            }

            &.xiaocai-avatar:hover img {
              transform: scale(1.1);
              border-color: #f94c30;
              box-shadow: 0 0 20px rgba(249, 76, 48, 0.6);
            }

            &.damai-avatar:hover img {
              transform: scale(1.1);
              border-color: #ffca46;
              box-shadow: 0 0 20px rgba(255, 202, 70, 0.6);
            }

            .premium-crown {
              position: absolute;
              top: -8px;
              right: -8px;
              width: 24px;
              height: 24px;
              background: linear-gradient(135deg, #ffca46 0%, #f94c30 100%);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              z-index: 4;
              animation: float 2s ease-in-out infinite;
              box-shadow: 0 3px 10px rgba(255, 202, 70, 0.5);

              i {
                font-size: 12px;
                color: #ffffff;
              }
            }

            i {
              font-size: 24px;
              color: #ffffff;
            }
          }

          .demo-info {
            flex: 1;

            .info-content {
              h5 {
                font-size: 18px;
                color: #ffffff;
                margin-bottom: 8px;
                font-weight: 700;
                display: flex;
                align-items: center;
                gap: 8px;

                .ai-badge-mini {
                  font-size: 10px;
                  background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
                  color: #ffffff;
                  padding: 2px 6px;
                  border-radius: 8px;
                  font-weight: 600;
                  animation: glow-pulse 2s ease-in-out infinite;
                }

                .custom-tag {
                  font-size: 10px;
                  background: rgba(255, 202, 70, 0.2);
                  color: #ffca46;
                  padding: 3px 8px;
                  border-radius: 10px;
                  font-weight: 600;
                  border: 1px solid rgba(255, 202, 70, 0.3);

                  &.premium {
                    background: linear-gradient(135deg, #ffca46 0%, #f94c30 100%);
                    color: #ffffff;
                    animation: premium-shine 3s ease-in-out infinite;
                    box-shadow: 0 2px 8px rgba(255, 202, 70, 0.4);
                  }
                }
              }

              .status-line {
                display: flex;
                flex-direction: column;
                gap: 4px;
                margin-bottom: 10px;

                .demo-status {
                  font-size: 12px;
                  color: #2ecc71;
                  font-weight: 600;
                  display: flex;
                  align-items: center;
                  gap: 5px;

                  &.premium {
                    color: #ffca46;
                  }

                  .pulse {
                    animation: status-pulse 1.5s ease-in-out infinite;
                  }

                  .sparkle {
                    animation: sparkle-rotate 2s linear infinite;
                    color: #ffca46;
                  }
                }

                .typing-indicator {
                  font-size: 10px;
                  color: rgba(255, 255, 255, 0.6);
                  font-style: italic;
                  animation: typing-dots 1.5s ease-in-out infinite;
                }

                .brand-indicator {
                  font-size: 10px;
                  color: rgba(255, 202, 70, 0.8);
                  font-weight: 500;
                  background: rgba(255, 202, 70, 0.1);
                  padding: 2px 6px;
                  border-radius: 6px;
                  border: 1px solid rgba(255, 202, 70, 0.2);
                }
              }

              .capability-tags {
                display: flex;
                gap: 6px;
                flex-wrap: wrap;

                .tag {
                  font-size: 9px;
                  background: rgba(249, 76, 48, 0.2);
                  color: #f94c30;
                  padding: 3px 8px;
                  border-radius: 12px;
                  font-weight: 500;
                  border: 1px solid rgba(249, 76, 48, 0.3);
                  transition: all 0.3s ease;

                  &:hover {
                    background: rgba(249, 76, 48, 0.3);
                    transform: translateY(-1px);
                  }
                }

                &.premium .tag {
                  background: rgba(255, 202, 70, 0.2);
                  color: #ffca46;
                  border-color: rgba(255, 202, 70, 0.3);

                  &:hover {
                    background: rgba(255, 202, 70, 0.3);
                    transform: translateY(-1px);
                  }
                }
              }
            }
          }
        }

        .demo-chat {
          margin-bottom: 20px;

          .chat-bubble {
            margin-bottom: 12px;
            padding: 12px 16px;
            border-radius: 15px;
            max-width: 90%;

            &.ai-bubble {
              background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
              color: #ffffff;
              margin-right: auto;
              border-bottom-left-radius: 4px;

              p {
                margin: 0;
                font-size: 13px;
                line-height: 1.4;
              }
            }

            &.user-bubble {
              background: rgba(255, 255, 255, 0.2);
              color: #ffffff;
              margin-left: auto;
              border-bottom-right-radius: 4px;

              p {
                margin: 0;
                font-size: 13px;
              }
            }
          }
        }

        .demo-actions {
          text-align: center;

          .demo-btn {
            background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
            color: #ffffff;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(249, 76, 48, 0.4);
            }

            i {
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

/* 功能区 - 核心服务功能展示 */
.features-section {
  margin-bottom: 80px;

  .section-with-image {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 60px;
    align-items: start;
  }

  .features-content {
    h3 {
      font-size: 32px;
      color: #ffffff;
      margin-bottom: 40px;
      font-weight: 700;
      background: linear-gradient(135deg, #ffffff 0%, #ffca46 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .feature-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 25px;
    }

    .feature-item {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-radius: 15px;
      padding: 25px;
      display: flex;
      align-items: flex-start;
      gap: 20px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;
      border-left: 4px solid #f94c30;
      border: 1px solid rgba(255, 255, 255, 0.1);

      &:hover {
        transform: translateX(5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
        background: rgba(255, 255, 255, 0.15);
      }

      i {
        font-size: 28px;
        color: #f94c30;
        flex-shrink: 0;
        margin-top: 5px;
      }

      .feature-content {
        h4 {
          font-size: 18px;
          color: #ffffff;
          margin-bottom: 8px;
          font-weight: 600;
        }

        p {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
          line-height: 1.5;
        }
      }
    }
  }

  .features-image {
    .image-placeholder {
      width: 100%;
      height: 300px;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 2px dashed #f94c30;
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: rgba(255, 255, 255, 0.8);
      font-size: 16px;
      font-weight: 500;
    }
  }
}

/* 支持计划 - 核心服务扶持 */
.support-section {
  h3 {
    font-size: 32px;
    color: #ffffff;
    text-align: center;
    margin-bottom: 40px;
    font-weight: 700;
    background: linear-gradient(135deg, #ffffff 0%, #ffca46 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .support-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .support-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    position: relative;
    border: 2px solid rgba(249, 76, 48, 0.3);

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
      border-color: #f94c30;
      background: rgba(255, 255, 255, 0.15);
    }

    .support-icon {
      width: 70px;
      height: 70px;
      background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px;

      i {
        font-size: 28px;
        color: #ffffff;
      }
    }

    h4 {
      font-size: 20px;
      color: #ffffff;
      margin-bottom: 12px;
      font-weight: 600;
    }

    p {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.5;
      margin-bottom: 15px;
    }

    .support-tag {
      background: #f94c30;
      color: #ffffff;
      padding: 6px 12px;
      border-radius: 15px;
      font-size: 12px;
      font-weight: 600;
      display: inline-block;
    }
  }
}

/* 入驻流程 - 统一背景样式 */
.join-process {
  padding: 80px 0;
  background: transparent;
  color: #ffffff;
  position: relative;

  .section-header {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 2;

    h2 {
      font-size: 36px;
      color: #ffffff;
      margin-bottom: 16px;
      font-weight: 700;
      background: linear-gradient(135deg, #ffffff 0%, #ffca46 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    p {
      font-size: 18px;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .process-steps {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 1000px;
    margin: 0 auto;
    gap: 40px;
    position: relative;
    z-index: 2;
  }

  .step-item {
    text-align: center;
    flex: 1;

    .step-number {
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: bold;
      color: #ffffff;
      margin: 0 auto 20px;
      box-shadow: 0 10px 25px rgba(249, 76, 48, 0.3);
    }

    .step-content {
      h3 {
        font-size: 20px;
        color: #ffffff;
        margin-bottom: 8px;
        font-weight: 600;
      }

      p {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.4;
      }
    }
  }

  .step-arrow {
    color: rgba(255, 255, 255, 0.5);
    font-size: 20px;
    flex-shrink: 0;
  }
}

/* 入驻表单 */
.join-form-section {
  padding: 80px 0;
  background: transparent;
  position: relative;
  overflow: hidden;

  .form-wrapper {
    max-width: 800px;
    margin: 0 auto;
    background: linear-gradient(135deg, rgba(26, 26, 43, 0.7) 0%, rgba(42, 42, 61, 0.7) 100%);
    border-radius: 30px;
    padding: 60px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 202, 70, 0.3);
    backdrop-filter: blur(15px);
    position: relative;
    z-index: 2;
    border: 2px solid rgba(255, 202, 70, 0.3);
  }

  .form-header {
    text-align: center;
    margin-bottom: 50px;

    h2 {
      font-size: 36px;
      color: #ffffff;
      margin-bottom: 16px;
      font-weight: 700;
      background: linear-gradient(135deg, #ffffff 0%, #ffca46 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    p {
      font-size: 18px;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .supplier-form {
    .supplier-type-select {
      margin-bottom: 40px;

      label {
        display: block;
        font-size: 18px;
        color: #ffffff;
        margin-bottom: 20px;
        font-weight: 600;
      }

      .type-options {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
      }

      .type-option {
        cursor: pointer;

        input[type='radio'] {
          display: none;

          &:checked + .option-content {
            background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
            color: #ffffff;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(249, 76, 48, 0.3);
          }
        }
      }

      .option-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 25px 15px;
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 202, 70, 0.3);
        border-radius: 15px;
        transition: all 0.3s ease;
        text-align: center;
        color: #ffffff;

        &:hover {
          border-color: #ffca46;
          transform: translateY(-2px);
          background: rgba(255, 202, 70, 0.1);
        }

        i {
          font-size: 24px;
          margin-bottom: 8px;
          color: #ffca46;
        }

        span {
          font-size: 16px;
          font-weight: 600;
          color: #ffffff;
        }
      }
    }

    .form-group {
      margin-bottom: 25px;

      label {
        display: block;
        font-size: 16px;
        color: #ffffff;
        margin-bottom: 10px;
        font-weight: 600;
      }

      input,
      textarea {
        width: 100%;
        padding: 15px 20px;
        border-radius: 12px;
        border: 2px solid rgba(255, 202, 70, 0.3);
        background: rgba(255, 255, 255, 0.1);
        color: #ffffff;
        font-size: 16px;
        transition: all 0.3s ease;
        line-height: normal;

        &:focus {
          outline: none;
          border-color: #ffca46;
          background: rgba(255, 202, 70, 0.1);
          box-shadow: 0 0 0 3px rgba(255, 202, 70, 0.2);
        }

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }
      }

      textarea {
        resize: vertical;
        min-height: 120px;
        line-height: 1.6;
      }

      .checkbox-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
      }

      .brand-selector {
        position: relative;

        .selected-brands {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-bottom: 10px;

          .brand-tag {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
            color: #ffffff;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;

            i {
              cursor: pointer;
              font-size: 12px;
              padding: 2px;
              border-radius: 50%;
              transition: all 0.3s ease;

              &:hover {
                background: rgba(255, 255, 255, 0.2);
              }
            }
          }
        }

        .search-input-wrapper {
          position: relative;

          .brand-search-input {
            width: 100%;
            padding: 15px 45px 15px 20px;
            border-radius: 12px;
            border: 2px solid rgba(255, 202, 70, 0.3);
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            font-size: 16px;
            transition: all 0.3s ease;

            &:focus {
              outline: none;
              border-color: #ffca46;
              background: rgba(255, 202, 70, 0.1);
              box-shadow: 0 0 0 3px rgba(255, 202, 70, 0.2);
            }

            &::placeholder {
              color: rgba(255, 255, 255, 0.5);
            }
          }

          .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.5);
            font-size: 16px;
          }
        }

        .brand-dropdown {
          position: absolute;
          top: 100%;
          left: 0;
          right: 0;
          background: rgba(26, 26, 43, 0.95);
          backdrop-filter: blur(15px);
          border: 2px solid rgba(255, 202, 70, 0.3);
          border-radius: 12px;
          max-height: 200px;
          overflow-y: auto;
          z-index: 1000;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);

          .brand-option {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            color: #ffffff;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);

            &:hover {
              background: rgba(255, 202, 70, 0.1);
            }

            &.selected {
              background: rgba(249, 76, 48, 0.2);
              color: #ffca46;
            }

            &:last-child {
              border-bottom: none;
            }

            i {
              color: #ffca46;
              font-size: 14px;
            }
          }

          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
          }

          &::-webkit-scrollbar-thumb {
            background: rgba(255, 202, 70, 0.5);
            border-radius: 3px;

            &:hover {
              background: rgba(255, 202, 70, 0.7);
            }
          }
        }
      }
    }

    .premium-service-checkbox,
    .agreement-checkboxes {
      .checkbox-label {
        display: flex;
        align-items: center;
        gap: 12px;
        cursor: pointer;
        font-size: 16px;
        color: #ffffff;
        font-weight: 500;
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }

        &.disabled {
          opacity: 0.8;
          cursor: not-allowed;

          .checkmark {
            background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
            border-color: #ffca46;

            &::after {
              opacity: 1;
            }

            &:hover {
              border-color: #ffca46;
            }
          }
        }

        input[type='checkbox'] {
          display: none;

          &:checked + .checkmark {
            background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
            border-color: #ffca46;

            &::after {
              opacity: 1;
            }
          }
        }

        .checkmark {
          width: 20px;
          height: 20px;
          border: 2px solid rgba(255, 202, 70, 0.5);
          border-radius: 4px;
          position: relative;
          transition: all 0.3s ease;
          flex-shrink: 0;

          &::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ffffff;
            font-size: 12px;
            font-weight: bold;
            opacity: 0;
            transition: opacity 0.3s ease;
          }

          &:hover {
            border-color: #ffca46;
          }
        }
      }

      .agreement-link {
        color: #ffca46;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        position: relative;

        &:hover {
          color: #f94c30;
          text-decoration: underline;
        }

        &:visited {
          color: #ffca46;
        }
      }
    }

    .form-actions {
      text-align: center;
      margin-top: 40px;

      .form-note {
        margin-top: 20px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
      }
    }
  }
}

/* 按钮样式 */
.btn {
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 2px solid transparent;
  font-size: 16px;
  position: relative;
  overflow: hidden;

  &.btn-large {
    padding: 16px 32px;
    font-size: 18px;
    border-radius: 15px;
  }
}

.btn-gradient {
  background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
  color: #ffffff;
  border: none;

  &:hover {
    background: linear-gradient(135deg, #e03a20 0%, #f5b230 100%);
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(249, 76, 48, 0.4);
  }
}

.btn-outline-white {
  background-color: transparent;
  color: #ffffff;
  border: 2px solid rgba(255, 255, 255, 0.5);

  &:hover {
    background-color: #ffffff;
    color: #1a1a2b;
    border-color: #ffffff;
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(255, 255, 255, 0.3);
  }
}

.btn-primary {
  background-color: #f94c30;
  color: #ffffff;
  border-color: #f94c30;

  &:hover {
    background-color: #e03a20;
    border-color: #e03a20;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(249, 76, 48, 0.4);
  }
}

.btn-outline {
  background-color: transparent;
  color: #1a1a2b;
  border: 2px solid #cccccc;

  &:hover {
    background-color: #f94c30;
    color: #ffffff;
    border-color: #f94c30;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(249, 76, 48, 0.4);
  }
}

/* 简单的淡入动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 酷炫动画效果 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse-glow {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

@keyframes glow-pulse {
  0%,
  100% {
    box-shadow: 0 0 5px rgba(249, 76, 48, 0.5);
  }
  50% {
    box-shadow: 0 0 15px rgba(249, 76, 48, 0.8);
  }
}

@keyframes premium-shine {
  0%,
  100% {
    box-shadow: 0 2px 8px rgba(255, 202, 70, 0.4);
  }
  50% {
    box-shadow: 0 4px 20px rgba(255, 202, 70, 0.8);
  }
}

@keyframes status-pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(0.9);
  }
}

@keyframes sparkle-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes typing-dots {
  0%,
  60%,
  100% {
    opacity: 0.4;
  }
  30% {
    opacity: 1;
  }
}

/* 页面加载动画 */
.supplier-hero {
  animation: fadeIn 0.8s ease-out;
}

.core-services .benefit-card,
.features-section .feature-item,
.support-section .support-item,
.premium-services-section .premium-card,
.join-process .step-item {
  animation: fadeInUp 0.6s ease-out forwards;

  &:nth-child(1) {
    animation-delay: 0.1s;
  }
  &:nth-child(2) {
    animation-delay: 0.2s;
  }
  &:nth-child(3) {
    animation-delay: 0.3s;
  }
  &:nth-child(4) {
    animation-delay: 0.4s;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .supplier-hero .hero-content {
    gap: 60px;
  }

  .benefits-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 992px) {
  .supplier-hero {
    padding: 100px 0 60px;

    .hero-content {
      flex-direction: column;
      text-align: center;
      gap: 40px;
    }

    .hero-title {
      font-size: 40px;
    }
  }

  .benefits-grid,
  .cases-grid,
  .services-container {
    grid-template-columns: 1fr;
  }

  .process-timeline {
    grid-template-columns: 1fr;
  }

  .join-form .form-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .supplier-hero {
    .hero-title {
      font-size: 32px;
    }
  }

  .form-container {
    padding: 30px 20px;
  }

  .section-title h2 {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .supplier-hero {
    .hero-title {
      font-size: 28px;
    }
  }

  .process-step {
    flex-direction: column;
    text-align: center;
  }
}

/* 成功状态样式 */
.success-state {
  text-align: center;
  animation: fadeInUp 0.8s ease-out;
}

.success-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
}

.success-icon {
  margin-bottom: 20px;
}

.success-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  animation: success-circle-scale 0.6s ease-out;
  box-shadow: 0 0 0 4px rgba(255, 202, 70, 0.3), 0 10px 30px rgba(249, 76, 48, 0.4);

  &::before {
    content: '';
    position: absolute;
    width: 136px;
    height: 136px;
    border: 3px solid rgba(255, 202, 70, 0.5);
    border-radius: 50%;
    border-top-color: transparent;
    border-right-color: transparent;
    animation: spin 3s linear infinite;
  }
}

.success-checkmark {
  width: 60px;
  height: 60px;
  position: relative;
}

.success-checkmark-stem {
  position: absolute;
  width: 6px;
  height: 30px;
  background-color: #ffffff;
  left: 22px;
  top: 15px;
  border-radius: 3px;
  transform: rotate(45deg);
  animation: success-checkmark-stem 0.4s ease-out 0.6s both;
}

.success-checkmark-kick {
  position: absolute;
  width: 6px;
  height: 15px;
  background-color: #ffffff;
  left: 13px;
  top: 28px;
  border-radius: 3px;
  transform: rotate(-45deg);
  animation: success-checkmark-kick 0.3s ease-out 0.8s both;
}

.success-content {
  max-width: 800px;
  margin: 0 auto;
}

.success-title {
  font-size: 42px;
  color: #ffffff;
  margin-bottom: 16px;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff 0%, #ffca46 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.success-subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 50px;
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

.success-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-bottom: 50px;
  animation: fadeInUp 0.6s ease-out 0.5s both;
}

.info-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
  }

  h3 {
    font-size: 24px;
    color: #ffffff;
    margin-bottom: 25px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;

    &::before {
      content: '📋';
      font-size: 20px;
    }
  }

  .info-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 15px;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    .label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.7);
      min-width: 80px;
      font-weight: 500;
    }

    .value {
      font-size: 16px;
      color: #ffffff;
      font-weight: 600;
      flex: 1;
    }

    .brands-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      flex: 1;

      .brand-chip {
        background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
        color: #ffffff;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 600;
      }
    }
  }
}

.next-steps {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
  }

  h3 {
    font-size: 24px;
    color: #ffffff;
    margin-bottom: 25px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;

    &::before {
      content: '🚀';
      font-size: 20px;
    }
  }

  .steps-timeline {
    .timeline-item {
      display: flex;
      align-items: flex-start;
      gap: 20px;
      margin-bottom: 25px;
      position: relative;

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 15px;
        top: 40px;
        width: 2px;
        height: 35px;
        background: linear-gradient(180deg, #f94c30 0%, #ffca46 100%);
        border-radius: 1px;
      }

      &:last-child {
        margin-bottom: 0;
      }

      .timeline-dot {
        width: 30px;
        height: 30px;
        background: linear-gradient(135deg, #f94c30 0%, #ffca46 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        position: relative;
        box-shadow: 0 5px 15px rgba(249, 76, 48, 0.3);

        &::after {
          content: '';
          width: 10px;
          height: 10px;
          background: #ffffff;
          border-radius: 50%;
        }
      }

      .timeline-content {
        flex: 1;

        h4 {
          font-size: 18px;
          color: #ffffff;
          margin-bottom: 8px;
          font-weight: 600;
        }

        p {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
          line-height: 1.5;
          margin: 0;
        }
      }
    }
  }
}

.success-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 40px;
  animation: fadeInUp 0.6s ease-out 0.6s both;

  .btn {
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 180px;

    i {
      font-size: 16px;
    }
  }
}

.success-footer {
  text-align: center;
  animation: fadeInUp 0.6s ease-out 0.7s both;

  p {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 15px;
  }

  .contact-info {
    display: flex;
    gap: 30px;
    justify-content: center;
    flex-wrap: wrap;

    span {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.9);
      background: rgba(255, 255, 255, 0.1);
      padding: 8px 16px;
      border-radius: 20px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(249, 76, 48, 0.2);
        transform: translateY(-2px);
      }

      i {
        color: #ffca46;
        font-size: 14px;
      }
    }
  }
}

/* 成功状态动画 */
@keyframes success-circle-scale {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes success-checkmark-stem {
  0% {
    height: 0;
    opacity: 0;
  }
  100% {
    height: 30px;
    opacity: 1;
  }
}

@keyframes success-checkmark-kick {
  0% {
    height: 0;
    opacity: 0;
  }
  100% {
    height: 15px;
    opacity: 1;
  }
}

/* 成功状态响应式设计 */
@media (max-width: 992px) {
  .success-info {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .success-title {
    font-size: 32px;
  }

  .success-circle {
    width: 100px;
    height: 100px;

    &::before {
      width: 116px;
      height: 116px;
    }
  }

  .success-checkmark {
    width: 50px;
    height: 50px;
  }

  .success-checkmark-stem {
    height: 25px;
    left: 18px;
    top: 12px;
  }

  .success-checkmark-kick {
    height: 12px;
    left: 11px;
    top: 23px;
  }
}

@media (max-width: 768px) {
  .success-actions {
    flex-direction: column;
    align-items: center;

    .btn {
      width: 100%;
      max-width: 300px;
    }
  }

  .success-title {
    font-size: 28px;
  }

  .success-subtitle {
    font-size: 16px;
  }

  .contact-info {
    flex-direction: column;
    gap: 15px;
  }

  .info-card,
  .next-steps {
    padding: 20px;
  }
}

/* 响应式设计适配 */
@media (max-width: 992px) {
  .supplier-hero {
    .hero-title {
      font-size: 40px;
    }
  }

  .benefits-section .benefit-cards {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .benefits-section .xiaocai-spotlight .spotlight-content {
    grid-template-columns: 1fr;
    gap: 30px;

    .content-right {
      order: -1;

      .image-placeholder {
        height: 200px;
      }
    }

    .content-left .spotlight-header {
      text-align: center;
    }
  }

  .features-section .section-with-image {
    grid-template-columns: 1fr;
    gap: 40px;

    .features-image {
      order: -1;
    }
  }

  .premium-grid {
    grid-template-columns: 1fr !important;
    gap: 20px;
  }

  .premium-services-section .damai-spotlight .spotlight-content {
    grid-template-columns: 1fr;
    gap: 30px;

    .content-left {
      order: -1;

      .image-placeholder {
        height: 200px;
      }
    }

    .content-right .spotlight-header {
      text-align: center;
    }
  }

  .support-section .support-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .supplier-hero {
    .hero-title {
      font-size: 32px;
    }

    .hero-subtitle {
      font-size: 16px;
    }

    .hero-actions {
      flex-direction: column;
      align-items: center;
      gap: 15px;

      .btn {
        width: 80%;
        max-width: 300px;
      }
    }
  }

  .core-services .section-header h2,
  .premium-services-section .section-header h2 {
    font-size: 32px;
  }

  .benefits-section .xiaocai-spotlight {
    padding: 30px 20px;

    .spotlight-header {
      text-align: center;

      h3 {
        font-size: 24px;
      }
    }

    .content-right .image-placeholder {
      height: 180px;

      .placeholder-content i {
        font-size: 36px;
      }

      .placeholder-content p {
        font-size: 14px;
      }
    }
  }

  .premium-services-section .damai-spotlight {
    padding: 30px 20px;

    .spotlight-header {
      text-align: center;

      h3 {
        font-size: 24px;
      }
    }

    .content-left .image-placeholder {
      height: 180px;

      .placeholder-content i {
        font-size: 36px;
      }

      .placeholder-content p {
        font-size: 14px;
      }
    }
  }

  .join-form-section .form-wrapper {
    padding: 40px 30px;
  }
}

.xiaocai {
  background: url('/supplier/xiaocai.png') no-repeat center !important;
  background-size: contain !important;
}

.damai {
  background: url('/supplier/damai.png') no-repeat center !important;
  background-size: contain !important;
}
</style>
