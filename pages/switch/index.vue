<template>
  <NuxtLayout name="child-page" title="切换空间">
    <div w-full max-w-150>
      <div menu-item mb-12.5 @click="onSwitch('person')">
        <div w-full flex items-center justify-between>
          <div flex items-center>
            <div font-size-20px rounded-full mr-2>
              <a-avatar :src="user.pic">{{ user.nickName }}</a-avatar>
            </div>
            <span>{{ user.nickName }}</span>
          </div>
          <span v-if="isPerSpace" color-primary font-size-5 class="i-a-check-outlined"></span>
        </div>
      </div>

      <div
        v-for="(item, index) in companyList"
        :key="index"
        menu-item
        :class="{ 'border-b border-b-[#353541]': index < companyList.length - 1 }"
        @click="onSwitch('company', item)"
      >
        <div w-full flex items-center justify-between>
          <div flex items-center>
            <div font-size-20px rounded-full mr-2>
              <a-avatar :src="item.merchantLogo">{{ item.merchantShortName || item.merchantName }}</a-avatar>
            </div>
            <span class="mr-4">{{ item.merchantShortName || item.merchantName }}</span>
            <a-tag v-for="tip in getTip(item)" :key="tip">{{ tip }}</a-tag>
          </div>
          <span v-if="item.shopCompanyId == curCompanyId" color-primary font-size-5 class="i-a-check-outlined"></span>
        </div>
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup>
import {} from 'vue'
import { getMerchantByUser } from '~/api/mall-manage/index'

definePageMeta({
  layout: false,
  middleware: ['workspace'],
})

const route = useRoute()

const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)
const companyStoreObj = companyStore()
const { company } = storeToRefs(companyStoreObj)
const { setCompany } = companyStoreObj

const isPerSpace = computed(() => {
  return !company.value.shopCompanyId
})

const curCompanyId = computed(() => {
  return company.value.shopCompanyId
})
const loadingCompany = ref(false)

const companyList = ref([])

const init = () => {
  getMerchantByUser({
    userMobile: user.value.userMobile,
  })
    .then((res) => {
      if (res.data) {
        companyList.value = JSON.parse(res.data)
      }
    })
    .finally(() => {
      loadingCompany.value = false
    })
}

const onSwitch = (key, data = {}) => {
  http('/mall/shop/userSpace/saveOrUpdate', {
    method: 'post',
    params: {
      userMobile: user.value.userMobile,
      merchantId: key == 'person' ? 0 : data.shopCompanyId,
      merchantName: key == 'person' ? '个人空间' : data.merchantShortName || data.merchantName,
    },
  }).then((res) => {
    useMall(res, async (ret) => {
      message.success('切换成功')
      setCompany(data)
      // 更新菜单
      if (data.shopCompanyId) {
        //todo: 等品牌馆开了再说，现在爆买商城的菜单大家都一样，而且也不是在这里了
        // const menuData = await getAuthMenu(data.shopCompanyId)
        // await useMall(menuData, async (ret) => {
        //   useAuthMenu.setAuthMenu({
        //     ...ret,
        //     loadedMenu: true,
        //   })
        // })
      } else {
        // useAuthMenu.setAuthMenu({
        //   loadedMenu: false,
        // })
      }

      // const path = route.path.split('/')[2]
      // if (key == 'person') {
      //   goToMySpace({
      //     key: path == 'my-space' ? selectedKeys.value[0] : 'selection-library',
      //   })
      // } else {
      //   goToCompanySpace(
      //     path == 'company-space' && menuItemKeys.value.includes(selectedKeys.value[0])
      //       ? selectedKeys.value[0]
      //       : menuItemKeys.value[0] || 'no-menu',
      //     data.shopCompanyId,
      //   )
      //   // 更新询价宝
      //   updateAnsCount()
      // }
    })
  })
}

const getTip = (item) => {
  const { type } = item
  switch (type) {
    case 1:
      return ['设备商']
    case 2:
      return ['供应商']
    case 3:
      return ['设备商', '供应商']
    default:
      return []
  }
}

onMounted(() => {
  init()
})
</script>

<style scoped></style>
