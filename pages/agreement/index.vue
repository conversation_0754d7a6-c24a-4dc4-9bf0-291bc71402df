<template>
  <NuxtLayout name="child-page" :title="current.title" :show-back="false">
    <div w-full max-w-210>
      <div class="h-[calc(100vh-200px)]" px-4 py-2 line-height-8 color-gray bg-darkBg overflow-y-auto rounded-2px>
        <component :is="current.content" />
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup>
import {} from 'vue'
import { useRoute } from 'vue-router'

import termOfService from './components/termOfService.vue'
import privacyPolicy from './components/privacyPolicy.vue'
import disclaimer from './components/disclaimer.vue'
import registration from './components/registration.vue'
const route = useRoute()

const current = computed(() => {
  const key = route.query.key
  if (key === 'termOfService') {
    return {
      title: '用户协议',
      content: termOfService,
    }
  } else if (key === 'privacyPolicy') {
    return {
      title: '隐私政策',
      content: privacyPolicy,
    }
  } else if (key === 'disclaimer') {
    return {
      title: '免责声明',
      content: disclaimer,
    }
  } else if (key === 'registration') {
    return {
      title: '企业入驻协议',
      content: registration,
    }
  } else {
    return {
      title: '',
      content: null,
    }
  }
})
</script>

<style scoped></style>
