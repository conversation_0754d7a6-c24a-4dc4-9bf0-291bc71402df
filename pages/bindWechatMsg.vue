<template>
  <div h-full flex flex-col justify-center items-center>
    <img v-if="success" h-50px src="~/assets/images/success.png" />
    <img v-else h-50px src="~/assets/images/error.png" />
    <span class="text-white" style="font-size: 16px; margin: 10px">{{ message }}</span>
    <div v-if="!success" style="margin: 10px">
      <a-button type="primary" @click="handleReScan">重新扫码</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: false,
})

const route = useRoute()
const message = ref(route.query.message || ('Error' as string))
const success = ref<boolean>(false)
success.value = message.value == '绑定成功'

setTimeout(() => {
  if (success.value) {
    window.parent.postMessage('closeDialog', '*')
  }
}, 1500)

const handleReScan = () => {
  window.parent.postMessage('reScan', '*')
}
</script>
