import type { NuxtPage } from 'nuxt/schema'
import { loadEnv } from 'vite'

const envScript = (process.env as any).npm_lifecycle_script.split(' ')
const envName = envScript[envScript.length - 1] // 通过启动命令区分环境
const envData = loadEnv(envName, process.cwd())
console.log('%c Line:6 🥛 envData', 'color:#5B6910', envData)
// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devServer: {
    host: '0.0.0.0',
  },
  compatibilityDate: '2024-11-01',
  devtools: { enabled: true },
  app: {
    head: {
      script: [{ src: 'https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js', type: 'text/javascript' }],
    },
  },
  modules: [
    '@ant-design-vue/nuxt',
    '@unocss/nuxt',
    [
      '@pinia/nuxt',
      {
        autoImports: ['defineStore'],
      },
    ],
    '@vueuse/nuxt',
  ],
  antd: {
    extractStyle: true,
  },
  css: ['@/assets/less/style.less', '@/assets/less/antd.less'],
  imports: {
    dirs: ['./store', './utils', './hooks/**'],
  },
  hooks: {
    'pages:extend'(pages) {
      function removePagesMatching(pattern: RegExp, pages: NuxtPage[] = []) {
        const pagesToRemove: NuxtPage[] = []
        for (const page of pages) {
          if (page.file && pattern.test(page.file)) {
            pagesToRemove.push(page)
          } else {
            removePagesMatching(pattern, page.children)
          }
        }
        for (const page of pagesToRemove) {
          pages.splice(pages.indexOf(page), 1)
        }
      }
      removePagesMatching(/.*components.*\.(vue|ts|tsx)$|.*\.(ts|js)$/, pages)
    },
  },
  runtimeConfig: {
    public: envData,
  },
})
