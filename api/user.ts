import { http as Http } from '@/utils/http.js'
import md5 from 'md5'

export const convert = (data: { password?: string; newPassword?: string }) => {
  const { password, newPassword } = data
  let pwd = password || newPassword
  const key = password ? 'password' : 'newPassword'
  if (pwd) {
    const md = md5(pwd)
    const aes = aesEncode(pwd)
    data = Object.assign(data, { [key]: md, aesPassword: aes })
  }
  return data
}

/**
 * 用户注册
 * @param data
 * @returns
 */
export const registerCommit = (data: any) => {
  return Http.post('/user/register', convert(data))
}

/**
 * 邀请用户注册
 * @param data
 * @returns
 */
export const registerInviteCommit = (data: any) => {
  return Http.post('/user/register/invite-link', convert(data))
}

/**
 * 修改用户信息
 * @param data
 * @returns
 */
export const userUpdate = (data: any) => {
  return http('/mall/p/user/setUserInfo', {
    method: 'put',
    body: data,
  })
}

/**
 * 修改头像
 * @param data
 * @returns
 */
export const userUpdateAvatar = (data: any) => {
  return Http.post('/user/update/avatar', data)
}

/**
 * 获取验证码
 * @param data
 * @returns
 */
export const getCaptcha = (data: any) => {
  return Http.post('/user/send-verify-code', data)
}

/**
 * 获取验证码
 * @param data
 * @returns
 */
export const getCaptchaForRegister = (data: any) => {
  return Http.post('/user/register/send-verify-code', data)
}

/**
 * 获取用户详情
 * @returns
 */
export const getUserDetail = () => {
  return http('/mall/p/user/userInfo')
}

/**
 * 忘记密码
 * @param data
 * @returns
 */
export const forgotPassword = (data: any) => {
  return http('/mall/user/resetPwd', {
    method: 'put',
    body: data,
  })
}

/**
 * 修改密码
 * @param data
 * @returns
 */
export const resetPassword = (data: any) => {
  return http('/mall/p/user/updatePwd', {
    method: 'put',
    body: data,
  })
}

/**
 * 重置手机号
 * @param data
 * @returns
 */
export const resetPhone = (data: any) => {
  return Http.post('/user/reset/phone', convert(data))
}

/**
 * 修改手机号
 * @param data
 * @returns
 */
export const updatePhone = (data: any) => {
  return Http.post('/user/modify-phone', convert(data))
}

/**
 * 绑定手机号
 * @param data
 * @returns
 */
export const setPhone = (data: any) => {
  return Http.post('/user/bind-phone', convert(data))
}

/**
 * 解绑微信
 * @param data
 * @returns
 */
export const unBindWx = () => {
  return Http.post('/user/unbind-wechat')
}

/**
 * 注销账号
 * @param data
 * @returns
 */
export const logOff = () => {
  return Http.delete('/user/unsubscribe')
}

/**
 * 获取商品收藏列表
 * @param params
 * @returns
 */
export const getFavourite = (params: any) => {
  return Http.get('/mall/p/my-favorites/getPersonalFavoritesByFolderIdAndUserId', params)
}

/**
 * 收藏商品
 * @param data
 * @returns
 */
export const setFavourite = (data: any) => {
  return Http.post('/mall/p/my-favorites', data)
}

/**
 * 取消商品收藏
 * @param params
 * @returns
 */
export const disFavourite = (params: any) => {
  return Http.delete('/mall/p/my-favorites', params)
}

/**
 * 检查商品是否收藏
 * @param partId
 * @returns
 */
export const checkFavourite = (partId: string) => {
  return Http.get<boolean>(`/my-favorite/check/${partId}`)
}


/**
 * 获取品牌列表
 * @returns
 */
export const getFavBrandList = (params: any) => {
  return Http.get('/mall/p/shop/collection/my', params)
}

/**
 * 收藏品牌
 * @param shopId
 * @returns
 */
export const setFavBrand = (shopId: any) => {
  return Http.post('/mall/p/shop/collection/' + shopId)
}

/**
 * 取消收藏品牌
 * @param shopId
 * @returns
 */
export const disFavBrand = (shopId: any) => {
  return Http.delete('/mall/p/shop/collection/' + shopId)
}
/**
 * 提交反馈
 * @param data
 * @returns
 */
export const commitFeedback = (data: any) => {
  return Http.post('/user-feedback', data)
}

/**
 * 获取邮箱验证码
 */
export const getEmailCode = (data) => {
  return Http.post('/user/send-email-verify-code', data)
}

/**
 * 绑定邮箱
 */
export const bindEmail = (data) => {
  return http('/mall/p/user/updateEmail', {
    method: 'put',
    body: data,
  })
}

/**
 * 获取邀请码
 * @returns
 */
export const getInviteCode = () => {
  return http('/mall/p/inviter-cipher/my')
}

/**
 * 获取积分记录
 * @returns
 */
export const getScoreLogs = (data: any) => {
  return Http.get('/user/scoreLogs', data)
}

/**
 * 获取总积分
 * @returns
 */
export const getTotalScore = () => {
  return Http.get('/user/total-score')
}

/**
 * 查询当前用户收到的公告 需要登录
 * @returns
 */
export const getMyNotices = () => {
  return http('/mall/p/notice/topNoticeList/0')
}

/**
 * 查询公告详情 不需要登录
 * @returns
 */
export const getNoticeDetails = (noticeId: any) => {
  return http(`/mall/notice/info/${noticeId}`)
}

/**
 * 查询公告 不需要登录
 * @returns
 */
export const getPublicNotices = () => {
  return http('/mall/notice/topNoticeList/0')
}

/**
 * 查询积分规则
 * @returns
 */
export const getScoreConfig = () => {
  return Http.get('/user/score-config')
}

/**
 * 发送邮箱验证码
 */
export const sendEmailReg = (params) => {
  return Http.post('/user/register/send-email-verify-code', params)
}

/**
 * 申请推广会员
 */
export const upgradeApply = (memberLevel: string) => Http.put('/user/member-level/upgrade-apply', { memberLevel })
