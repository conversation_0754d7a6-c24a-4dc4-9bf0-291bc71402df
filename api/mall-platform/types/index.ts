export enum ComApply {
  pass = 1,
  wait = 0,
  fail = -1,
}

export enum ComStatus {
  noApply,
  apply,
}

export enum ComResult {
  fail,
  pass,
}

export type CompItem = {
  businessLicense: string
  businessScope: string
  capital: number
  createTime: string
  createdBy: string
  creditCode: string
  firmName: string
  foundTime: string
  identityCardFront: string
  identityCardLater: string
  mainProduct: string
  merchantLogo: string
  merchantMail: string
  merchantName: string
  merchantPhone: string
  merchantShortName: string
  merchantSlogan: string
  merchantWebsite: string
  promotionalImg: string
  representative: string
  residence: string
  shopCompanyId: number
  shopId: number
  startTime: string
  status: ComApply
  updateTime: string
  userMobile: string
}

export type CompBind = {
  id: number
  merchantId: number
  userMobile: number
  auditStatus: ComStatus
  auditResult: ComResult
}
