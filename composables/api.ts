import { UseFetchOptions } from '#app'
import { Response } from '~/utils/request'

export function useCloudApi<T = any>(url: string | (() => string), options?: UseFetchOptions<Response<T>>) {
  return useFetch(url, {
    ...(options ?? {}),
    $fetch: useNuxtApp().$customApi,
  })
}

export function usePlatApi<T = any>(url: string | (() => string), options?: UseFetchOptions<Response<T>>) {
  return useFetch(url, {
    ...(options ?? {}),
    $fetch: useNuxtApp().$platApi,
  })
}
