export default () => {
    const copy = (text: string) => {
        const input = document.createElement('textarea')
        document.body.appendChild(input)
        input.setAttribute('readonly', 'readonly')
        Object.assign(input.style, {
            position: 'fixed',
            left: '-999px',
            opacity: 0
        })
        input.value = text
        input.select()
        input.setSelectionRange(0, text.length)
        try {
            document.execCommand('copy')
            document.body.removeChild(input)
        } catch (error) {
            console.log("%c Line:12 🍋 error", "color:#e41a6a", error);
            // message.error(error.toString())
        }

    }
    return copy
}