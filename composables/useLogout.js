export const useLogout = () => {
  const route = useRoute()
  const router = useRouter()
  const store = userStore()
  const logout = () => {
    Modal.confirm({
      title: '提示',
      content: '是否退出登录',
      async onOk() {
        await http('/mall/p/logOut', {
          method: 'post',
        })
        message.success('已退出登录')
        store.clearUser()

        // 黑名单路由列表
        const blackList = ['/userCenter', '/enterpriseCenter', '/switch', '/chat', '/history', '/message-center']
        const currentPath = route.path

        // 检查当前路由是否在黑名单或其子路由下
        const isInBlackList = blackList.some((path) => currentPath.startsWith(path))

        if (isInBlackList) {
          router.replace({
            path: '/',
          })
        }
      },
    })
  }

  return {
    logout,
  }
}
