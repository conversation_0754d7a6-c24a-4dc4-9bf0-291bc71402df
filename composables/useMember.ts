import { MemberLevel, userStore } from '~/store/user'

export const getStep = (level: MemberLevel) => {
  switch (level) {
    case MemberLevel.NORMAL:
      return 0
    case MemberLevel.AUTHENTICATION:
      return 1
    case MemberLevel.PROFESSIONAL:
      return 2
    case MemberLevel.GENERALIZATION:
      return 3
  }
}

// 判断是否达到某个会员等级
export const isLevel = (level: MemberLevel, target: MemberLevel) => getStep(level) >= getStep(target)

export default () => {
  const store = userStore()
  if (getStep(store.user.level) < getStep(MemberLevel.PROFESSIONAL)) {
    Modal.confirm({
      title: '请先升级为专业会员',
      onOk() {
        navigateTo({ path: '/my/member' })
      },
      okText: '去升级',
    })
    return false
  }
  return true
}
