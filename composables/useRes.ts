import type { Response } from '~/utils/http'

const useRes = async <T extends Response>(
  res: T,
  callbackSuccess?: (data: T extends { data: infer D } ? D : unknown) => any,
  callbackErr?: (_res: T) => void,
) => {
  if (res.code == 'ok') {
    await callbackSuccess?.(res.data)
    return true
  } else {
    if (callbackErr) {
      callbackErr(res)
    } else {
      if (import.meta.client) {
        message.destroy()
        message.error(res.message)
      }
    }
    return false
  }
}

export default useRes

export const useMall = async <T extends MallResponse>(
  res: T,
  callbackSuccess?: (data: T extends { data: infer D } ? D : unknown) => any,
  callbackErr?: (_res: T) => void,
) => {
  if (res.success) {
    await callbackSuccess?.(res.data)
    return true
  } else {
    if (callbackErr) {
      callbackErr(res)
    } else {
      if (import.meta.client) {
        message.destroy()
        message.error(res.msg)
      }
    }
    return false
  }
}
