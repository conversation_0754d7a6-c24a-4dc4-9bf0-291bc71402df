export const useWidth = (init: number) => {
  const originWidth = ref(init)
  const isDragging = ref(false)
  let start = ref(0)
  let dx = ref(0)

  const move = (e) => {
    dx.value = e.x - start.value
  }
  const up = () => {
    isDragging.value = false
    originWidth.value += dx.value
    dx.value = 0
    document.removeEventListener('mousemove', move)
    document.removeEventListener('mouseup', up)
  }
  const startWatch = (e) => {
    isDragging.value = true
    start.value = e.x
    document.addEventListener('mousemove', move)
    document.addEventListener('mouseup', up)
  }

  const width = computed(() => {
    if (isDragging.value) {
      return originWidth.value + dx.value
    }
    return originWidth.value
  })
  return {
    width,
    startWatch
  }
}