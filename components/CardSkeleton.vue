<template>
  <div class="grid gap-20 grid-cols-1" sm="grid-cols-2" md="grid-cols-3" lg="grid-cols-4" xl="grid-cols-5">
    <div v-for="item in count" :key="item" text-center>
      <a-skeleton-button active block />
      <br />
      <br />
      <a-skeleton-image w-full />
      <br />
      <br />
      <a-skeleton-button active block />
    </div>
  </div>
</template>

<script lang="ts" setup>
withDefaults(
  defineProps<{
    count?: number
  }>(),
  {
    count: 20,
  },
)
</script>
