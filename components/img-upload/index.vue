<template>
  <a-upload-dragger
    v-model:file-list="fileList"
    list-type="picture-card"
    :show-upload-list="false"
    inline-block
    :class="{ 'w-full': type === 'image', 'w-40': type === 'avatar' }"
    h-40
    line-height-30
    p-0
    action=""
    :before-upload="beforeUpload"
    :customRequest="(file) => uploadImage(file)"
  >
    <img v-if="value" :src="value" w-full h-full max-h-32 object-contain />
    <div v-else>
      <plus-outlined></plus-outlined>
    </div>
  </a-upload-dragger>
</template>

<script setup>
import { uploadFile } from '~/api/common'

const emits = defineEmits(['change'])
const props = defineProps({
  value: String,
  beforeUpload: Function,
  type: {
    type: String,
    default: 'image',
    validator: (value) => ['avatar', 'image'].includes(value),
  },
})

const fileList = ref([])

const uploadImage = (data) => {
  uploadFile(data.file, 'Avatar')
    .then((res) => {
      if (res.code === 'ok') {
        emits('change', res.data)
      }
    })
    .catch((err) => {})
}
</script>
