<template>
  <div class="enterprise-modal" v-if="visible">
    <!-- 遮罩层 -->
    <div class="modal-overlay" @click="handleClose"></div>

    <!-- 弹窗主体 -->
    <div class="modal-container" data-aos="zoom-in" data-aos-duration="300">
      <!-- 关闭按钮 -->
      <button class="close-button" @click="handleClose">
        <i class="fas fa-times"></i>
      </button>

      <!-- 头部 -->
      <div class="modal-header">
        <div class="header-icon">
          <i class="fas fa-building"></i>
        </div>
        <h2 class="modal-title">企业入驻申请</h2>
        <p class="modal-subtitle">填写您的企业信息，我们会尽快联系您完成入驻流程</p>
      </div>

      <!-- 表单内容 -->
      <div class="modal-content">
        <form @submit.prevent="handleSubmit" class="enterprise-form">
          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-building"></i>
              企业名称
              <span class="required">*</span>
            </label>
            <input
              type="text"
              v-model="form.companyName"
              class="form-input"
              placeholder="请输入您的企业全称"
              required
            />
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-phone"></i>
              联系电话
              <span class="required">*</span>
            </label>
            <input type="tel" v-model="form.phone" class="form-input" placeholder="请输入联系电话" required />
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-envelope"></i>
              企业邮箱
              <span class="required">*</span>
            </label>
            <input type="email" v-model="form.email" class="form-input" placeholder="请输入企业邮箱地址" required />
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-industry"></i>
              主营业务
              <span class="required">*</span>
            </label>
            <textarea
              v-model="form.business"
              class="form-textarea"
              placeholder="请简要描述您的主营业务和产品"
              rows="4"
              required
            ></textarea>
          </div>

          <!-- 提交按钮 -->
          <div class="form-actions">
            <button type="button" class="btn btn-secondary" @click="handleClose">取消</button>
            <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
              <i class="fas fa-paper-plane" v-if="!isSubmitting"></i>
              <i class="fas fa-spinner fa-spin" v-else></i>
              {{ isSubmitting ? '提交中...' : '提交申请' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['close', 'submit'])

// 表单数据
const form = ref({
  companyName: '',
  phone: '',
  email: '',
  business: '',
})

// 提交状态
const isSubmitting = ref(false)

const handleClose = () => {
  emit('close')
}

const handleSubmit = async () => {
  try {
    isSubmitting.value = true

    // 这里可以添加表单验证逻辑
    if (!form.value.companyName.trim()) {
      throw new Error('请输入企业名称')
    }
    if (!form.value.phone.trim()) {
      throw new Error('请输入联系电话')
    }
    if (!form.value.email.trim()) {
      throw new Error('请输入企业邮箱')
    }
    if (!form.value.business.trim()) {
      throw new Error('请输入主营业务')
    }

    // 发送提交事件
    emit('submit', { ...form.value })

    // 重置表单
    form.value = {
      companyName: '',
      phone: '',
      email: '',
      business: '',
    }

    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    // 这里可以添加错误提示
  } finally {
    isSubmitting.value = false
  }
}

// 监听visible变化，控制body滚动
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }
  }
)
</script>

<style lang="less" scoped>
.enterprise-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;

  .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.75);
    backdrop-filter: blur(8px);
  }

  .modal-container {
    position: relative;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    background: linear-gradient(135deg, var(--secondary) 0%, var(--dark) 100%);
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
  }

  .close-button {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--light);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(249, 76, 48, 0.2);
      color: var(--primary);
      transform: scale(1.1);
    }

    i {
      font-size: 16px;
    }
  }

  .modal-header {
    text-align: center;
    padding: 40px 40px 30px;
    background: linear-gradient(135deg, rgba(249, 76, 48, 0.1) 0%, transparent 100%);

    .header-icon {
      width: 80px;
      height: 80px;
      margin: 0 auto 20px;
      background: linear-gradient(135deg, var(--primary) 0%, #e03a20 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      animation: pulse 2s infinite;

      i {
        font-size: 36px;
        color: var(--light);
      }
    }

    .modal-title {
      font-size: 32px;
      font-weight: 700;
      color: var(--light);
      margin-bottom: 12px;
      background: linear-gradient(45deg, var(--light), var(--primary));
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .modal-subtitle {
      font-size: 16px;
      color: var(--light-gray);
      margin: 0;
    }
  }

  .modal-content {
    padding: 20px 40px 40px;
    max-height: 60vh;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--primary);
      border-radius: 3px;

      &:hover {
        background: #e03a20;
      }
    }
  }

  .enterprise-form {
    .form-group {
      margin-bottom: 24px;

      .form-label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 600;
        color: var(--light);
        margin-bottom: 8px;

        i {
          color: var(--primary);
          font-size: 14px;
          width: 16px;
        }

        .required {
          color: var(--primary);
          font-weight: 700;
        }
      }

      .form-input,
      .form-textarea {
        width: 100%;
        padding: 14px 16px;
        background: rgba(255, 255, 255, 0.05);
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        color: var(--light);
        font-size: 16px;
        transition: all 0.3s ease;
        resize: none;

        &::placeholder {
          color: var(--light-gray);
        }

        &:focus {
          outline: none;
          border-color: var(--primary);
          background: rgba(255, 255, 255, 0.08);
          box-shadow: 0 0 0 4px rgba(249, 76, 48, 0.1);
        }

        &:hover {
          border-color: rgba(255, 255, 255, 0.2);
        }
      }

      .form-textarea {
        font-family: inherit;
        line-height: 1.5;
      }
    }

    .form-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin-top: 32px;

      .btn {
        padding: 14px 32px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 16px;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 140px;
        justify-content: center;

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        &.btn-secondary {
          background: rgba(255, 255, 255, 0.1);
          color: var(--light);
          border: 1px solid rgba(255, 255, 255, 0.2);

          &:hover:not(:disabled) {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
          }
        }

        &.btn-primary {
          background: linear-gradient(135deg, var(--primary) 0%, #e03a20 100%);
          color: var(--light);
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }

          &:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(249, 76, 48, 0.4);

            &::before {
              left: 100%;
            }
          }

          i {
            font-size: 14px;
          }
        }
      }
    }
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enterprise-modal {
    .modal-container {
      width: 95%;
      max-height: 95vh;
    }

    .modal-header {
      padding: 30px 20px 20px;

      .header-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 16px;

        i {
          font-size: 28px;
        }
      }

      .modal-title {
        font-size: 24px;
      }

      .modal-subtitle {
        font-size: 14px;
      }
    }

    .modal-content {
      padding: 20px 20px 30px;
    }

    .enterprise-form {
      .form-actions {
        flex-direction: column;
        gap: 12px;

        .btn {
          width: 100%;
        }
      }
    }
  }
}
</style>
