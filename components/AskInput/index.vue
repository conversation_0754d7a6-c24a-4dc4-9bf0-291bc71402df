<template>
  <div w-full py-2>
    <div v-if="fileList.length > 0" class="py-3 mt-2 flex gap-2">
      <div v-for="(url, index) in fileList" :key="index" class="relative w-16 h-16 rounded overflow-hidden group">
        <img :src="url" class="w-full h-full object-cover" />
        <div
          class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center"
        >
          <CloseCircleOutlined class="text-white text-lg cursor-pointer" @click="fileList.splice(index, 1)" />
        </div>
      </div>
    </div>
    <div w-full text-white bg="#353541" py-3 rounded-2px min-h-100px flex flex-col>
      <slot name="top" />
      <div class="flex-1">
        <a-textarea
          v-model:value="value"
          v-bind="$attrs"
          ref="inputRef"
          class="bg-transparent! placeholder-#7f7f7f text-#fff resize-none!"
          placeholder="随便问点什么..."
          :bordered="false"
          :auto-size="true"
          :disabled="sending"
          @press-enter="pressEnter"
        ></a-textarea>
      </div>

      <div class="flex px-3 items-center text-24px gap-2">
        <div class="flex-1"></div>

        <template v-if="showUpload">
          <a-upload
            :customRequest="customUpload"
            :before-upload="beforeUpload"
            accept="image/*"
            :showUploadList="false"
            :maxCount="5"
          >
            <a-tooltip :title="`上传图片 (${fileList.length}/5)`">
              <a-button type="link" :icon="h(PictureOutlined)" />
            </a-tooltip>
          </a-upload>
        </template>

        <a-tooltip :title="sending ? '停止' : '发送'">
          <a-button
            type="primary"
            shape="circle"
            :icon="h(sending ? PauseOutlined : SendOutlined)"
            @click.stop="handleSend"
          />
        </a-tooltip>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { h } from 'vue'
import { PauseOutlined, SendOutlined, PictureOutlined, CloseCircleOutlined } from '@ant-design/icons-vue'
import { uploadFile } from '~/api/common'

const inputRef = ref()
const value = defineModel<string>()
const fileList = ref<string[]>([])

onMounted(() => {
  inputRef.value?.focus()
})

const props = defineProps<{
  sending?: boolean
  showUpload?: boolean
}>()

const emits = defineEmits(['send', 'uploadSuccess'])

const customUpload = async (options: any) => {
  try {
    const res = await uploadFile(options.file, 'AgentPic')
    fileList.value.push(res.data)
    emits('uploadSuccess', fileList.value)
  } catch (error) {
    console.error('上传失败：', error)
  }
}

const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    message.error('只能上传图片文件！')
  }
  return isImage
}

const formatMessage = (text: string, images: string[]) => {
  if (!images.length) return text

  const attachments = JSON.stringify(images)
  return `<SYSTEM_REFER>\n<attachments>${attachments}</attachments>\n</SYSTEM_REFER>\n${text}`
}

const handleSend = () => {
  const formattedMessage = formatMessage(value.value || '', fileList.value)
  const files = fileList.value
  nextTick(() => {
    value.value = ''
    fileList.value = []
    if (files.length) {
      emits('send', formattedMessage, { files })
    } else {
      emits('send', formattedMessage)
    }
  })
}

const pressEnter = (e: KeyboardEvent) => {
  e.preventDefault()
  handleSend()
}
</script>

<style scoped>
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}
</style>
