<template>
  <div class="fixed left-0 top-0 w-full">
    <div class="container ">
      <div class="navbar">
        <div class="logo">
          <img src="~/assets/images/logo_rect_white.png" alt="logo"
            class="max-w-full max-h-full cursor-pointer object-cover" @click="navigateTo('/')" />
        </div>
        <div class="text opacity-0 text-16px py-10px leading-[1.5]">123</div>
      </div>
    </div>
  </div>
</template>

<script setup>
const containerClasses = 'px-20px md:px-40px lg:px-80px'
</script>

<style scoped>
.container {
  width: 100%;
  max-width: 1920px;
  padding: 0 80px;
}

.logo {
  width: 200px;
  height: 32px;
}

@media (max-width: 1200px) {
  .container {
    padding: 0 40px;
  }
}

@media (max-width: 992px) {
  .logo {
    width: 160px;
  }

  .navbar {
    padding: 15px 0;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .text {
    font-size: 14px;
    padding: 8px 16px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .logo {
    width: 130px;
  }
}

.navbar {
  display: flex;
  align-items: center;
  padding: 20px 0;
}
</style>