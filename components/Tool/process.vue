<template>
  <div class="w-full overflow-hidden">
    <tool-info :title="title" :loading="current < 1" />
    <div mt="4">
      <div mb="2">
        <div mb="1" text="14px">{{ currentProcess?.name || '' }}</div>
        <a-progress :percent="Math.round(current * 100)" />
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  processes: {
    type: Array,
    required: true,
  },
})

// 使用 defineModel 定义 current
const current = defineModel({
  default: 0,
})

// 当前进度
const progress = ref(0)
// 当前阶段
const currentProcess = ref(null)

// 获取当前阶段
const getCurrentProcess = (progress) => {
  return props.processes.find((process, index) => {
    const prevEnd = index === 0 ? 0 : props.processes[index - 1].end
    return progress >= prevEnd && progress <= process.end
  })
}

// 计算当前阶段的增长速率
const calculateStepRate = (process, index) => {
  const start = index === 0 ? 0 : props.processes[index - 1].end
  const range = process.end - start
  return (range / process.maxTimeMs) * 16.7 // 假设 60fps，每帧约16.7ms
}

// 更新进度
const updateProgress = () => {
  if (current.value >= 1) {
    current.value = 1
    return
  }

  const currentStage = getCurrentProcess(current.value)
  if (!currentStage) return
  currentProcess.value = currentStage

  const currentIndex = props.processes.indexOf(currentStage)
  const stepRate = calculateStepRate(currentStage, currentIndex)

  current.value += stepRate
}

// 启动进度更新
let animationFrameId = null
const startProgress = () => {
  const animate = () => {
    if (current.value >= 1) {
      progress.value = 1
      currentProcess.value = props.processes[props.processes.length - 1]
      cancelAnimationFrame(animationFrameId)
      return
    }

    updateProgress()
    animationFrameId = requestAnimationFrame(animate)
  }
  animate()
}

// 组件挂载时初始化
onMounted(() => {
  currentProcess.value = props.processes[0]
  startProgress()
})

// 组件卸载时清理
onUnmounted(() => {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
  }
})
</script>
