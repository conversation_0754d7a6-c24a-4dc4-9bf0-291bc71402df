<template>
  <div class="modal-overlay" v-if="visible">
    <div class="modal-content" data-aos="zoom-in">
      <div class="modal-header">
        <h3>申请试用</h3>
        <button class="close-button" @click="onClose">
          <i class="fas i-fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <form @submit.prevent="handleSubmit" class="trial-form">
          <div class="form-group relative" ref="companyRef">
            <label for="company">公司名称</label>
            <input
              type="text"
              id="company"
              v-model="company"
              placeholder="请输入公司名称"
              required
              @focus="handleFocus"
              @input="fetchCompany"
            />
            <div
              class="absolute top-full mt-2 w-full left-0 bg-dark-200 rounded-lg shadow-xl z-100 border border-dark-100"
              v-if="isInput && currentInput"
            >
              <div v-if="loading" class="p-4 text-light-gray">搜索中...</div>
              <template v-else>
                <div
                  v-if="companyList.length"
                  class="max-h-400px overflow-y-auto w-full [&::-webkit-scrollbar]:(w-1.5) [&::-webkit-scrollbar-track]:(bg-white/5 rounded-sm) [&::-webkit-scrollbar-thumb]:(bg-primary rounded-sm hover:bg-[#e03a20])"
                >
                  <div
                    v-for="item in companyList"
                    :key="item.name"
                    class="p-4 cursor-pointer hover:bg-dark-100 text-light hover:text-primary transition-colors duration-200"
                    @click="selectCompany(item)"
                  >
                    {{ item.name }}
                  </div>
                </div>
                <div v-else class="p-4 text-light-gray">没有找到匹配的企业</div>
              </template>
            </div>
          </div>
          <div class="form-group">
            <label for="name">姓名</label>
            <input type="text" id="name" v-model="form.name" placeholder="请输入您的姓名" required />
          </div>
          <div class="form-group">
            <label for="phone">联系电话</label>
            <input type="tel" id="phone" v-model="form.phone" placeholder="请输入联系电话" required />
          </div>
          <div class="form-group">
            <label for="position">职位</label>
            <input type="text" id="position" v-model="form.position" placeholder="请输入您的职位" required />
          </div>
          <button type="submit" class="submit-button">
            提交申请
            <loading-outlined v-if="submitLoading"></loading-outlined>
          </button>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const visible = ref(false)

const emit = defineEmits(['close'])
const isInput = ref(false)
const current = ref({})
const currentInput = ref('')
const selectCompany = ({ name, creditCode }) => {
  current.value = { name, creditCode }
  isInput.value = false
}
const company = computed({
  get() {
    if (isInput.value) {
      return currentInput.value
    }
    return current.value.name
  },
  set(val) {
    currentInput.value = val
  },
})

let _resolve
const user = userStore()
const open = (name, creditCode) => {
  const { realName, nickName, userMobile, position } = user.user
  current.value = {
    name,
    creditCode,
  }
  form.value.name = realName || nickName
  form.value.phone = userMobile
  form.value.position = position
  visible.value = true
  return new Promise((resolve) => {
    _resolve = resolve
  })
}

const form = ref({
  company: '',
  name: '',
  phone: '',
  position: '',
})

const handleFocus = () => {
  isInput.value = true
  currentInput.value = ''
}

const loading = ref(false)
const companyList = ref([])
let count = 0
const fetchCompany = useDebounceFn(async () => {
  loading.value = true
  count++
  const fetchId = count
  const [err, res] = await _try(() =>
    $fetch('/bussiness', {
      method: 'post',
      body: {
        word: currentInput.value,
      },
    }),
  )
  if (err) return
  if (fetchId == count) {
    loading.value = false
    companyList.value = res.result?.items || []
  }
}, 250)

const companyRef = ref()
onClickOutside(companyRef, () => {
  isInput.value = false
})

const onClose = () => {
  visible.value = false
}

const submitLoading = ref(false)
const handleSubmit = async () => {
  submitLoading.value = true
  const [err] = await try_http('/mall/p/standard-customer', {
    method: 'post',
    body: {
      creditCode: current.value.creditCode,
      merchantName: current.value.name,
    },
  })
  submitLoading.value = false
  if (err) return
  message.success('申请成功')
  visible.value = false
}

defineExpose({
  open,
})
</script>

<style lang="less" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: var(--dark);
  border-radius: 16px;
  width: 90%;
  max-width: 500px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 24px 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;

  h3 {
    color: var(--light);
    font-size: 24px;
    font-weight: 600;
    margin: 0;
  }

  .close-button {
    background: none;
    border: none;
    color: var(--light-gray);
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: var(--light);
    }

    i {
      font-size: 20px;
    }
  }
}

.modal-body {
  padding: 32px;
}

.trial-form {
  .form-group {
    margin-bottom: 24px;

    label {
      display: block;
      color: var(--light);
      font-size: 16px;
      margin-bottom: 8px;
      font-weight: 500;
    }

    input {
      width: 100%;
      padding: 12px 16px;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      color: var(--light);
      font-size: 16px;
      transition: all 0.3s ease;

      &::placeholder {
        color: rgba(255, 255, 255, 0.3);
      }

      &:focus {
        outline: none;
        border-color: var(--primary);
        background: rgba(255, 255, 255, 0.08);
        box-shadow: 0 0 0 3px rgba(249, 76, 48, 0.15);
      }
    }
  }

  .submit-button {
    width: 100%;
    padding: 14px 24px;
    background: linear-gradient(45deg, var(--primary), #e03a20);
    border: none;
    border-radius: 8px;
    color: var(--light);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(249, 76, 48, 0.4);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

@media (max-width: 768px) {
  .modal-content {
    width: 95%;
  }

  .modal-header {
    padding: 20px 24px;

    h3 {
      font-size: 20px;
    }
  }

  .modal-body {
    padding: 24px;
  }
}
</style>
