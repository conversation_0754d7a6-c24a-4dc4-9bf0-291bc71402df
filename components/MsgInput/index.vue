<template>
  <a-input placeholder="验证码" v-bind="$attrs">
    <template #suffix>
      <a-button size="small" :loading="loading" @click="send" type="text" :disabled="sendType === SendType.sending">
        <span class="text-primary">
          {{ msg }}
        </span>
      </a-button>
    </template>
  </a-input>
</template>

<script setup lang="ts">
enum SendType {
  send,
  sending,
  resend,
}

const props = defineProps<{
  msgKey: string
  api: AsyncFunction
  timeout?: number
}>()

const { msgKey, api, timeout } = toRefs(props)
const loading = ref(false)
const prefix = '_msg_key__'
const prefixKey = computed(() => prefix + msgKey.value)

const overtime = computed(() => timeout.value ?? 60)
const count = ref(overtime.value)

const sendType = ref<SendType>(SendType.send)

const send = async () => {
  if (sendType.value == SendType.send || sendType.value == SendType.resend) {
    loading.value = true
    try {
      await api.value()
      message.success('验证码发送成功')
      calcTime()
    } catch (err) {
      console.log('%c Line:39 🥛 err', 'color:#7A9C72', err)
    }
    loading.value = false
  }
}

let timer: NodeJS.Timeout

const cookieTime = useCookie(prefixKey.value)
const calcTime = () => {
  sendType.value = SendType.sending
  const lasttime = cookieTime.value
  if (!lasttime) {
    cookieTime.value = new Date().getTime() + ''
    count.value = overtime.value
  }
  if (import.meta.client) {
    timer = setInterval(() => {
      if (count.value > 1) {
        count.value--
      } else {
        clearInterval(timer)
        count.value = 0
        sendType.value = SendType.resend
        cookieTime.value = null
      }
    }, 1000)
  }
}

const init = () => {
  const lasttime = cookieTime.value
  if (lasttime) {
    const duration = new Date().getTime() - Number(lasttime)
    if (duration >= overtime.value * 1000) {
      timer && clearInterval(timer)
      sendType.value = SendType.send
      cookieTime.value = null
    } else {
      count.value = overtime.value - Math.floor(duration / 1000)
      calcTime()
    }
  }
}
init()

const msg = computed(() => {
  switch (sendType.value) {
    case SendType.send:
      return '获取验证码'
    case SendType.sending:
      return `重新获取(${count.value})`
    case SendType.resend:
      return '重新获取'
  }
})
</script>
