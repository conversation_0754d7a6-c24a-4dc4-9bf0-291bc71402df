<template>
  <div
    class="fixed left-0 top-1/2 translate-y--1/2 w-48px bg-darkBg rounded-r-2px transition-transform duration-300 ease-in-out group"
    :class="{ '-translate-x-full': !isExpanded }"
    :style="{ zIndex: ($attrs.style as any)?.zIndex || 30 }"
  >
    <!-- 切换按钮 -->
    <div
      class="absolute -right-6 top-1/2 -translate-y-1/2 w-6 h-12 bg-darkBg rounded-r-2px flex items-center justify-center cursor-pointer hover:bg-opacity-80 transition-all duration-300"
      :class="{ 'opacity-0 group-hover:opacity-100': isExpanded, 'opacity-100': !isExpanded }"
      @click="toggleExpanded"
    >
      <div class="text-white text-xs transition-transform duration-300" :class="{ 'rotate-180': isExpanded }">
        <i class="i-a-right-outlined"></i>
      </div>
    </div>

    <div v-for="(itemGroup, index) in computedWidgets" :key="index">
      <div class="flex-center h-50px relative" v-for="item in itemGroup" :key="item.icon || item.type">
        <template v-if="item.type == 'logo'">
          <a-tooltip placement="right">
            <template #title>
              <span>首页</span>
            </template>
            <nuxt-link to="/">
              <div w-8 h-8 bg-white rounded-full overflow-hidden>
                <img w-8 src="~/assets/images/logo_squa_white.png" cursor-pointer />
              </div>
            </nuxt-link>
          </a-tooltip>
        </template>
        <template v-else-if="item.type == 'avatar'">
          <a-tooltip placement="rightTop" trigger="click" v-if="isLogin">
            <template #title>
              <div>
                <div font-size-4>{{ user.nickName }}</div>
                <div color-gray max-w-40 truncate :title="company.merchantName">{{ company.merchantName }}</div>
                <div px-1>
                  <div
                    v-for="(avatarMenuItem, index) in AvatarMenuItems"
                    :key="index"
                    h-5
                    my-1
                    flex
                    items-center
                    cursor-pointer
                  >
                    <template v-if="avatarMenuItem.type === 'link'">
                      <nuxt-link :to="avatarMenuItem.link" link flex items-center>
                        <span mr-1 :class="avatarMenuItem.icon"></span>
                        <span>{{ avatarMenuItem.title }}</span>
                      </nuxt-link>
                    </template>
                    <template v-else>
                      <div flex items-center link @click="avatarMenuItem.clickEvent">
                        <span mr-1 :class="avatarMenuItem.icon"></span>
                        <span>{{ avatarMenuItem.title }}</span>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </template>
            <a-avatar cursor-pointer :src="user.pic">{{ user.nickName }}</a-avatar>
          </a-tooltip>
          <a-avatar cursor-pointer v-else @click="navigateTo({ path: '/login' })">登录</a-avatar>
        </template>
        <template v-else>
          <a-tooltip placement="right">
            <template #title>
              <span>{{ item.tooltip }}</span>
            </template>
            <div class="relative">
              <div
                :class="item.icon"
                class="text-8"
                text-primaryText
                hover:color-primary
                transition-colors
                cursor-pointer
                @click="handleItemClick(item)"
              ></div>
              <div
                v-if="item.link === '/message-center' && unreadCount > 0"
                class="absolute -top-2 -right-2 min-w-4 h-4 px-1 rounded-full bg-red-500 text-white text-xs flex items-center justify-center"
              >
                {{ unreadCount > 99 ? '99+' : unreadCount }}
              </div>
            </div>
          </a-tooltip>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'

const companyStoreObj = companyStore()
const { company, isPersonal } = storeToRefs(companyStoreObj)

const messageStore = usemessageStore()
const unreadCount = computed(() => messageStore.msgCount)

const { manageBrand } = useManageBrand()
const bomai = useRuntimeConfig().public.VITE_BOMAI_URL

type WidgetItem = {
  type?: string
  tooltip?: string
  icon?: string
  link?: string | undefined
  auth?: boolean
  action?: () => void
}

// 企业工作区相关的widget
const enterpriseWidgets = computed(() => {
  const widgets: WidgetItem[] = []

  if (!isLogin.value) return widgets

  const { type } = company.value

  // 设备商工作区
  if (type == 1 || type == 3) {
    widgets.push({
      icon: 'i-a-appstore-outlined',
      tooltip: '设备商工作台',
      auth: true,
      action: () => {
        window.open(`${bomai}/workSpace`, '_blank')
      },
    })
  }

  if (isPersonal.value) {
    widgets.push({
      icon: 'i-a-appstore-outlined',
      tooltip: '个人工作台',
      auth: true,
      action: () => {
        window.open(`${bomai}/workSpace`, '_blank')
      },
    })
  }

  // 供应商管理后台/品牌馆
  if ([2, 3].includes(type)) {
    widgets.push(
      company.value.creditCode == '91320594MAE5L8PD96'
        ? {
            icon: 'i-a-bank-outlined',
            tooltip: '研选品牌馆',
            link: '/enterpriseCenter/library',
            auth: true,
          }
        : {
            icon: 'i-a-bank-outlined',
            tooltip: '供应商管理后台',
            auth: true,
            action: async () => {
              manageBrand()
            },
          },
    )
  }

  return widgets
})

const computedWidgets = computed(() => {
  const baseWidgets: Array<WidgetItem[]> = [
    [
      {
        type: 'logo',
        tooltip: '首页',
        link: '/',
      },
    ],
    [
      {
        icon: 'i-a-shop-outlined',
        tooltip: '研选产品库',
        link: useRuntimeConfig().public.VITE_BOMAI_URL as string,
      },
    ],
    [
      // {
      //   icon: 'i-a-message-outlined',
      //   tooltip: '历史对话',
      //   link: '/message-history',
      //   auth: true,
      // },
      {
        icon: 'i-a-star-outlined',
        tooltip: '收藏夹',
        link: '/userCenter/fav',
        auth: true,
      },
      {
        icon: 'i-a-bell-outlined',
        tooltip: '消息中心',
        link: '/message-center',
        auth: true,
      },
      {
        type: 'avatar',
      },
    ],
  ]

  // 如果有企业工作区widgets，插入到倒数第二组
  if (enterpriseWidgets.value.length > 0) {
    baseWidgets.splice(-1, 0, enterpriseWidgets.value)
  }

  if (isLogin.value) {
    return baseWidgets
  } else {
    return baseWidgets.map((item) => {
      const filter = item.filter((v) => !v.auth)
      return filter
    })
  }
})

type AvatarMenuItem = {
  type?: string
  title?: string
  icon?: string
  link?: string
  clickEvent?: (e: MouseEvent) => void
}

const onLogout = async () => {
  Modal.confirm({
    title: '提示',
    content: '是否退出登录',
    async onOk() {
      await http('/mall/p/logOut', {
        method: 'post',
      })
      message.success('已退出登录')
      store.clearUser()

      // 黑名单路由列表
      const blackList = ['/userCenter', '/enterpriseCenter', '/switch', '/chat', '/history', '/message-center']
      const currentPath = router.currentRoute.value.path

      // 检查当前路由是否在黑名单或其子路由下
      const isInBlackList = blackList.some((path) => currentPath.startsWith(path))

      if (isInBlackList) {
        router.push('/')
      }
    },
  })
}

const AvatarMenuItems: AvatarMenuItem[] = [
  {
    type: 'link',
    title: '个人中心',
    icon: 'i-a-user-outlined',
    link: '/userCenter',
  },
  // {
  //   type: 'link',
  //   title: '企业中心',
  //   icon: 'i-a-bank-outlined',
  //   link: '/enterpriseCenter',
  // },
  // {
  //   type: 'link',
  //   title: '切换空间',
  //   icon: 'i-a-swap-outlined',
  //   link: '/switch',
  // },
  {
    type: 'button',
    title: '退出登录',
    icon: 'i-a-logout-outlined',
    clickEvent: onLogout,
  },
]

const router = useRouter()
const go = (item: WidgetItem) => {
  if (item.link) {
    if (item.link.startsWith('http')) {
      window.open(item.link, '_blank')
    } else {
      router.push({ path: item.link })
    }
  }
}

const store = userStore()
const user = computed(() => store.user)
const isLogin = computed(() => user.value.userId)

const handleItemClick = (item: WidgetItem) => {
  if (item.action) {
    item.action()
  } else {
    go(item)
  }
}

// 添加展开/收起状态
const isExpanded = ref(true)

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}
</script>

<style lang="less" scoped>
/* Rounded corners */
.rounded-r-2px {
  border-radius: 0 2px 2px 0;
}
</style>
