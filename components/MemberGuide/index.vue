<template>
  <div class="membership-upgrade-modal" v-if="visible">
    <!-- 遮罩层 -->
    <div class="modal-overlay" @click="handleClose"></div>

    <!-- 弹窗主体 -->
    <div class="modal-container" data-aos="zoom-in" data-aos-duration="300">
      <!-- 关闭按钮 -->
      <button class="close-button" @click="handleClose">
        <i class="fas i-fa-times"></i>
      </button>

      <!-- 头部 -->
      <div class="modal-header">
        <h2 class="modal-title">升级企业会员</h2>
        <p class="modal-subtitle">成为设备商企业会员，解锁全平台功能，享受专属企业服务</p>
      </div>

      <!-- 主要内容 -->
      <div class="modal-content">
        <!-- 个人会员限制提示 -->
        <!-- <div class="limitation-notice">
          <div class="notice-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="notice-content">
            <h3>个人会员功能受限</h3>
            <p>当前您只能使用基础询价功能，且每日询价次数有限制</p>
          </div>
        </div> -->

        <!-- 限时免费促销区域 -->
        <!-- <div class="limited-time-promotion">
          <div class="promotion-header">
            <div class="promotion-tag">
              <i class="fas i-fa-fire"></i>
              限时免费
            </div>
            <div class="countdown-timer">
              <div class="timer-item">
                <span class="timer-number">{{ countdown.days }}</span>
                <span class="timer-label">天</span>
              </div>
              <div class="timer-separator">:</div>
              <div class="timer-item">
                <span class="timer-number">{{ countdown.hours }}</span>
                <span class="timer-label">时</span>
              </div>
              <div class="timer-separator">:</div>
              <div class="timer-item">
                <span class="timer-number">{{ countdown.minutes }}</span>
                <span class="timer-label">分</span>
              </div>
              <div class="timer-separator">:</div>
              <div class="timer-item">
                <span class="timer-number">{{ countdown.seconds }}</span>
                <span class="timer-label">秒</span>
              </div>
            </div>
          </div>

          <div class="pricing-display">
            <div class="original-price">
              <span class="price-label">原价</span>
              <span class="price-value">¥4,999</span>
              <div class="strike-through"></div>
            </div>
            <div class="current-price">
              <span class="price-label">限时价</span>
              <span class="price-value">¥0</span>
              <span class="free-badge">免费</span>
            </div>
          </div>

          <div class="promotion-notice">
            <i class="fas fa-exclamation-circle"></i>
            <span>活动仅限前1000名用户，先到先得！</span>
          </div>
        </div> -->

        <!-- 企业会员优势介绍 -->
        <div class="enterprise-advantages">
          <!-- 左右布局容器 -->
          <div class="advantages-container">
            <!-- 左侧：全流程采购管理能力 -->
            <div class="advantage-section left">
              <div class="advantage-header">
                <div class="advantage-icon procurement">
                  <i class="fas i-fa-cogs"></i>
                </div>
                <div class="advantage-title">
                  <h4>全流程采购管理能力</h4>
                  <p>从需求到交付，打造完整的采购管理闭环</p>
                </div>
              </div>

              <div class="advantage-features">
                <div class="feature-item">
                  <div class="feature-icon">
                    <i class="fas i-fa-list-alt"></i>
                  </div>
                  <div class="feature-content">
                    <h5>采购BOM管理</h5>
                    <p>支持物料清单的创建与管理</p>
                  </div>
                </div>

                <div class="feature-item">
                  <div class="feature-icon">
                    <i class="fas i-fa-handshake"></i>
                  </div>
                  <div class="feature-content">
                    <h5>询价 & 下单</h5>
                    <p>可直接发起询价并完成采购订单</p>
                  </div>
                </div>

                <div class="feature-item">
                  <div class="feature-icon">
                    <i class="fas i-fa-calculator"></i>
                  </div>
                  <div class="feature-content">
                    <h5>对账 & 付款</h5>
                    <p>提供财务对账和在线支付功能</p>
                  </div>
                </div>

                <!-- <div class="feature-item"> -->
                <!--   <div class="feature-icon"> -->
                <!--     <i class="fas i-fa-undo"></i> -->
                <!--   </div> -->
                <!--   <div class="feature-content"> -->
                <!--     <h5>退换货处理</h5> -->
                <!--     <p>支持售后问题高效解决</p> -->
                <!--   </div> -->
                <!-- </div> -->
              </div>
            </div>

            <!-- 右侧：企业级平台支持服务 -->
            <div class="advantage-section right">
              <div class="advantage-header">
                <div class="advantage-icon platform">
                  <i class="fas i-fa-building"></i>
                </div>
                <div class="advantage-title">
                  <h4>企业级平台支持服务</h4>
                  <p>专业服务团队，为企业采购保驾护航</p>
                </div>
              </div>

              <div class="advantage-features">
                <div class="feature-item">
                  <div class="feature-icon">
                    <i class="fas i-fa-calendar-check"></i>
                  </div>
                  <div class="feature-content">
                    <h5>账期结算</h5>
                    <p>灵活的企业账期支付，优化现金流</p>
                  </div>
                </div>

                <div class="feature-item">
                  <div class="feature-icon">
                    <i class="fas i-fa-robot"></i>
                  </div>
                  <div class="feature-content">
                    <h5>AI客服（小妍助手）</h5>
                    <p>智能解答采购问题</p>
                  </div>
                </div>

                <div class="feature-item">
                  <div class="feature-icon">
                    <i class="fas i-fa-user-tie"></i>
                  </div>
                  <div class="feature-content">
                    <h5>专属采购助理</h5>
                    <p>人工服务支持复杂需求</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="modal-footer">
        <div class="footer-content">
          <label class="no-reminder-checkbox">
            <input type="checkbox" v-model="noReminder" />
            <span class="checkmark"></span>
            <span class="checkbox-text">不再提示</span>
          </label>
          <button class="btn btn-primary" @click="handleUpgrade">立即升级</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'

// const props = defineProps({
//   visible: {
//     type: Boolean,
//     default: false,
//   },
// })
const visible = ref(false)

const showPop = () => {
  if (noReminder.value) return
  visible.value = true
}

const emit = defineEmits(['close', 'upgrade'])

// 不再提示状态
const noReminder = useCookie('_showGuide', { default: () => false })

// 倒计时状态
const countdown = ref({
  days: '00',
  hours: '00',
  minutes: '00',
  seconds: '00',
})

let countdownTimer = null

// 计算倒计时
const updateCountdown = () => {
  // 设置活动结束时间（当前时间 + 7天）
  const endTime = new Date()
  endTime.setDate(endTime.getDate() + 7)

  const now = new Date().getTime()
  const distance = endTime.getTime() - now

  if (distance > 0) {
    const days = Math.floor(distance / (1000 * 60 * 60 * 24))
    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((distance % (1000 * 60)) / 1000)

    countdown.value = {
      days: String(days).padStart(2, '0'),
      hours: String(hours).padStart(2, '0'),
      minutes: String(minutes).padStart(2, '0'),
      seconds: String(seconds).padStart(2, '0'),
    }
  } else {
    // 倒计时结束
    countdown.value = {
      days: '00',
      hours: '00',
      minutes: '00',
      seconds: '00',
    }
  }
}

const handleClose = () => {
  visible.value = false
}

const handleUpgrade = () => {
  emit('upgrade', { noReminder: noReminder.value })
  handleClose()
}

// 监听visible变化，控制body滚动
watch(visible, (newVal) => {
  if (newVal) {
    document.body.style.overflow = 'hidden'
    // 开始倒计时
    updateCountdown()
    countdownTimer = setInterval(updateCountdown, 1000)
  } else {
    document.body.style.overflow = ''
    // 清除倒计时
    if (countdownTimer) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
  }
})

onMounted(() => {
  // if (props.visible) {
  //   updateCountdown()
  //   countdownTimer = setInterval(updateCountdown, 1000)
  // }
})

defineExpose({
  showPop,
})

onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
</script>

<style lang="less" scoped>
.membership-upgrade-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;

  .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.75);
    backdrop-filter: blur(8px);
  }

  .modal-container {
    position: relative;
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    background: linear-gradient(135deg, var(--secondary) 0%, var(--dark) 100%);
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
  }

  .close-button {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--light);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(249, 76, 48, 0.2);
      color: var(--primary);
      transform: scale(1.1);
    }

    i {
      font-size: 16px;
    }
  }

  .modal-header {
    text-align: center;
    padding: 40px 40px 30px;
    background: linear-gradient(135deg, rgba(249, 76, 48, 0.1) 0%, transparent 100%);

    .header-icon {
      width: 80px;
      height: 80px;
      margin: 0 auto 20px;
      background: linear-gradient(135deg, var(--primary) 0%, #e03a20 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      animation: pulse 2s infinite;

      i {
        font-size: 36px;
        color: var(--light);
      }
    }

    .modal-title {
      font-size: 32px;
      font-weight: 700;
      color: var(--light);
      margin-bottom: 12px;
      background: linear-gradient(45deg, var(--light), var(--primary));
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .modal-subtitle {
      font-size: 16px;
      color: var(--light-gray);
      margin: 0;
    }
  }

  .modal-content {
    padding: 20px 40px 0;
    max-height: 60vh;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--primary);
      border-radius: 3px;

      &:hover {
        background: #e03a20;
      }
    }
  }

  .limitation-notice {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 12px;
    margin-bottom: 30px;

    .notice-icon {
      width: 48px;
      height: 48px;
      background: rgba(255, 193, 7, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      i {
        font-size: 20px;
        color: #ffc107;
      }
    }

    .notice-content {
      h3 {
        font-size: 18px;
        color: var(--light);
        margin: 0 0 8px;
        font-weight: 600;
      }

      p {
        font-size: 14px;
        color: var(--light-gray);
        margin: 0;
        line-height: 1.5;
      }
    }
  }

  .enterprise-advantages {
    .advantages-title {
      text-align: center;
      margin-bottom: 32px;

      h3 {
        font-size: 24px;
        font-weight: 700;
        color: var(--light);
        margin: 0 0 12px;
        background: linear-gradient(45deg, var(--light), var(--primary));
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      p {
        font-size: 16px;
        color: var(--light-gray);
        margin: 0;
        line-height: 1.5;
      }
    }

    .advantages-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;
      align-items: start;
    }

    .advantage-section {
      background: rgba(255, 255, 255, 0.03);
      border-radius: 16px;
      padding: 24px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;
      height: 100%;

      &:hover {
        background: rgba(255, 255, 255, 0.05);
        transform: translateY(-2px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      }

      &.left {
        border-color: rgba(249, 76, 48, 0.3);

        &:hover {
          border-color: rgba(249, 76, 48, 0.5);
          box-shadow: 0 8px 32px rgba(249, 76, 48, 0.15);
        }
      }

      &.right {
        border-color: rgba(255, 202, 70, 0.3);

        &:hover {
          border-color: rgba(255, 202, 70, 0.5);
          box-shadow: 0 8px 32px rgba(255, 202, 70, 0.15);
        }
      }

      .advantage-header {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-bottom: 24px;

        .advantage-icon {
          width: 64px;
          height: 64px;
          border-radius: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          position: relative;
          overflow: hidden;

          &.procurement {
            background: linear-gradient(135deg, #f94c30 0%, #e03a20 100%);
          }

          &.platform {
            background: linear-gradient(135deg, #ffca46 0%, #f59e0b 100%);
          }

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 3s infinite;
          }

          i {
            font-size: 28px;
            color: var(--light);
            z-index: 1;
          }
        }

        .advantage-title {
          flex: 1;

          h4 {
            font-size: 18px;
            font-weight: 600;
            color: var(--light);
            margin: 0 0 8px;
          }

          p {
            font-size: 14px;
            color: var(--light-gray);
            margin: 0;
            line-height: 1.4;
          }
        }
      }

      .advantage-features {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .feature-item {
          display: flex;
          align-items: flex-start;
          gap: 16px;
          padding: 16px;
          background: rgba(255, 255, 255, 0.02);
          border-radius: 12px;
          border: 1px solid rgba(255, 255, 255, 0.05);
          transition: all 0.3s ease;

          &:hover {
            background: rgba(249, 76, 48, 0.08);
            border-color: rgba(249, 76, 48, 0.2);
            transform: translateY(-1px);
          }

          .left & {
            &:hover {
              background: rgba(249, 76, 48, 0.08);
              border-color: rgba(249, 76, 48, 0.2);
            }
          }

          .right & {
            &:hover {
              background: rgba(255, 202, 70, 0.08);
              border-color: rgba(255, 202, 70, 0.2);
            }
          }

          .feature-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;

            i {
              font-size: 20px;
              color: var(--light-gray);
            }
          }

          .left & .feature-icon i {
            color: #f94c30;
          }

          .right & .feature-icon i {
            color: #ffca46;
          }

          .feature-content {
            flex: 1;

            h5 {
              font-size: 15px;
              font-weight: 600;
              color: var(--light);
              margin: 0 0 6px;
            }

            p {
              font-size: 13px;
              color: var(--light-gray);
              margin: 0;
              line-height: 1.4;
            }
          }
        }
      }
    }
  }

  .enterprise-benefits {
    margin-bottom: 20px;

    .benefits-title {
      font-size: 20px;
      font-weight: 600;
      color: var(--light);
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      gap: 10px;

      i {
        color: var(--primary);
        font-size: 18px;
      }
    }

    .benefits-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;

      .benefit-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px;
        background: rgba(255, 255, 255, 0.03);
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.05);
        transition: all 0.3s ease;

        &:hover {
          background: rgba(249, 76, 48, 0.1);
          border-color: var(--primary);
          transform: translateY(-2px);
        }

        .benefit-icon {
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, var(--primary) 0%, #e03a20 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          i {
            font-size: 16px;
            color: var(--light);
          }
        }

        .benefit-content {
          h4 {
            font-size: 14px;
            font-weight: 600;
            color: var(--light);
            margin: 0 0 4px;
          }

          p {
            font-size: 12px;
            color: var(--light-gray);
            margin: 0;
            line-height: 1.4;
          }
        }
      }
    }
  }

  .limited-time-promotion {
    background: linear-gradient(135deg, rgba(255, 69, 0, 0.15) 0%, rgba(255, 140, 0, 0.1) 100%);
    border: 2px solid rgba(255, 69, 0, 0.3);
    border-radius: 20px;
    padding: 16px;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    animation: promotionPulse 2s ease-in-out infinite;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
      animation: goldShimmer 3s infinite;
    }

    .promotion-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 36px;
      flex-wrap: wrap;
      gap: 16px;

      .promotion-tag {
        background: linear-gradient(135deg, #ff4500 0%, #ff8c00 100%);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 28px;
        display: inline-flex;
        align-items: center;
        gap: 6px;
        box-shadow: 0 2px 8px rgba(255, 69, 0, 0.3);
        transform: rotate(-5deg);
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          background: linear-gradient(135deg, #ffd700 0%, #ff8c00 100%);
          border-radius: 20px;
          z-index: -1;
          opacity: 0.7;
        }

        i {
          font-size: 12px;
          animation: fireFlicker 0.5s ease-in-out infinite alternate;
        }
      }

      .countdown-timer {
        display: flex;
        align-items: center;
        gap: 8px;

        .timer-item {
          background: rgba(0, 0, 0, 0.6);
          border-radius: 8px;
          padding: 8px 12px;
          text-align: center;
          min-width: 50px;

          .timer-number {
            display: block;
            font-size: 20px;
            font-weight: 700;
            color: #ffd700;
            line-height: 1;
          }

          .timer-label {
            display: block;
            font-size: 12px;
            color: var(--light-gray);
            margin-top: 2px;
          }
        }

        .timer-separator {
          font-size: 18px;
          font-weight: 700;
          color: #ffd700;
          animation: separatorBlink 1s ease-in-out infinite;
        }
      }
    }

    .pricing-display {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 80px;
      flex-wrap: wrap;

      .original-price {
        position: relative;
        text-align: center;
        opacity: 0.7;

        .price-label {
          display: block;
          font-size: 14px;
          color: var(--light-gray);
          margin-bottom: 4px;
        }

        .price-value {
          font-size: 32px;
          font-weight: 700;
          color: var(--light);
        }

        .strike-through {
          position: absolute;
          top: 65%;
          left: 0;
          right: 0;
          height: 5px;
          background: #ff4444;
          transform: translateY(-50%) rotate(15deg);
          border-radius: 2px;
        }
      }

      .current-price {
        text-align: center;
        position: relative;

        .price-label {
          display: block;
          font-size: 16px;
          color: #ffd700;
          margin-bottom: 4px;
          font-weight: 600;
        }

        .price-value {
          font-size: 48px;
          font-weight: 900;
          background: linear-gradient(45deg, #ffd700, #ffed4e);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
          animation: priceGlow 2s ease-in-out infinite;
        }

        .free-badge {
          position: absolute;
          top: -10px;
          right: -20px;
          background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
          color: white;
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 700;
          transform: rotate(15deg);
          box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
        }
      }
    }

    .promotion-features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
      margin-bottom: 20px;

      .feature-highlight {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 10px;
        border: 1px solid rgba(255, 215, 0, 0.2);

        i {
          color: #ffd700;
          font-size: 14px;
          width: 16px;
        }

        span {
          color: var(--light);
          font-size: 14px;
          font-weight: 500;
        }
      }
    }

    .promotion-notice {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 12px;
      background: rgba(255, 69, 0, 0.1);
      border: 1px solid rgba(255, 69, 0, 0.3);
      border-radius: 10px;
      color: #ff6b35;
      font-size: 14px;
      font-weight: 600;
      text-align: center;

      i {
        font-size: 16px;
        animation: noticeIcon 2s ease-in-out infinite;
      }
    }
  }

  .modal-footer {
    padding: 20px 40px;

    .footer-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 24px;
      flex-wrap: wrap;
      position: relative;
    }

    .no-reminder-checkbox {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      user-select: none;
      position: absolute;
      left: 0;

      input[type='checkbox'] {
        display: none;
      }

      .checkmark {
        width: 18px;
        height: 18px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 4px;
        background: transparent;
        position: relative;
        transition: all 0.3s ease;

        &::after {
          content: '';
          position: absolute;
          left: 5px;
          top: 2px;
          width: 5px;
          height: 8px;
          border: solid white;
          border-width: 0 2px 2px 0;
          transform: rotate(45deg);
          opacity: 0;
          transition: opacity 0.3s ease;
        }
      }

      input[type='checkbox']:checked + .checkmark {
        background: linear-gradient(135deg, var(--primary) 0%, #e03a20 100%);
        border-color: var(--primary);

        &::after {
          opacity: 1;
        }
      }

      &:hover .checkmark {
        border-color: var(--primary);
        background: rgba(249, 76, 48, 0.1);
      }

      .checkbox-text {
        color: var(--light-gray);
        font-size: 14px;
        font-weight: 500;
        transition: color 0.3s ease;
      }

      &:hover .checkbox-text {
        color: var(--light);
      }
    }

    .btn {
      padding: 14px 32px;
      border-radius: 25px;
      font-weight: 600;
      font-size: 16px;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 160px;
      justify-content: center;

      &.btn-secondary {
        background: rgba(255, 255, 255, 0.1);
        color: var(--light);
        border: 1px solid rgba(255, 255, 255, 0.2);

        &:hover {
          background: rgba(255, 255, 255, 0.15);
          transform: translateY(-2px);
        }
      }

      &.btn-primary {
        background: linear-gradient(135deg, var(--primary) 0%, #e03a20 100%);
        color: var(--light);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s;
        }

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 8px 25px rgba(249, 76, 48, 0.4);

          &::before {
            left: 100%;
          }
        }

        i {
          font-size: 14px;
        }
      }
    }
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes ctaShimmer {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes rocketFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-8px) rotate(5deg);
  }
}

@keyframes promotionPulse {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(255, 69, 0, 0.3);
  }
  50% {
    box-shadow:
      0 0 30px rgba(255, 69, 0, 0.5),
      0 0 40px rgba(255, 140, 0, 0.3);
  }
}

@keyframes goldShimmer {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes badgeBounce {
  0% {
    transform: translateY(0) scale(1);
  }
  100% {
    transform: translateY(-2px) scale(1.02);
  }
}

@keyframes fireFlicker {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@keyframes separatorBlink {
  0%,
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.3;
  }
}

@keyframes priceGlow {
  0%,
  100% {
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
    transform: scale(1);
  }
  50% {
    text-shadow:
      0 0 30px rgba(255, 215, 0, 0.8),
      0 0 40px rgba(255, 215, 0, 0.4);
    transform: scale(1.02);
  }
}

@keyframes noticeIcon {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes featureFloat {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

@keyframes advantageGlow {
  0%,
  100% {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  }
  50% {
    box-shadow:
      0 12px 40px rgba(249, 76, 48, 0.15),
      0 8px 32px rgba(0, 0, 0, 0.2);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .membership-upgrade-modal {
    .modal-container {
      width: 95%;
      max-height: 95vh;
    }

    .modal-header {
      padding: 30px 20px 20px;

      .header-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 16px;

        i {
          font-size: 28px;
        }
      }

      .modal-title {
        font-size: 24px;
      }

      .modal-subtitle {
        font-size: 14px;
      }
    }

    .modal-content {
      padding: 0 20px;
    }

    .enterprise-advantages {
      .advantages-title {
        margin-bottom: 24px;

        h3 {
          font-size: 20px;
        }

        p {
          font-size: 14px;
        }
      }

      .advantages-container {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .advantage-section {
        padding: 20px;

        .advantage-header {
          gap: 16px;
          margin-bottom: 20px;

          .advantage-icon {
            width: 56px;
            height: 56px;

            i {
              font-size: 24px;
            }
          }

          .advantage-title {
            h4 {
              font-size: 17px;
            }

            p {
              font-size: 13px;
            }
          }
        }

        .advantage-features {
          gap: 10px;

          .feature-item {
            padding: 14px;

            .feature-icon {
              width: 36px;
              height: 36px;

              i {
                font-size: 18px;
              }
            }

            .feature-content {
              h5 {
                font-size: 14px;
              }

              p {
                font-size: 12px;
              }
            }
          }
        }
      }
    }

    .benefits-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .upgrade-cta {
      padding: 20px;
      gap: 16px;

      .cta-icon {
        width: 50px;
        height: 50px;

        i {
          font-size: 20px;
        }
      }

      .cta-content {
        h3 {
          font-size: 18px;
        }

        p {
          font-size: 13px;

          .highlight {
            font-size: 14px;
          }
        }
      }
    }

    .limited-time-promotion {
      padding: 20px;

      .promotion-header {
        flex-direction: column;
        align-items: center;
        gap: 12px;

        .promotion-tag {
          font-size: 12px;
          padding: 6px 12px;
        }

        .countdown-timer {
          justify-content: center;
          flex-wrap: wrap;

          .timer-item {
            min-width: 40px;
            padding: 6px 8px;

            .timer-number {
              font-size: 16px;
            }

            .timer-label {
              font-size: 10px;
            }
          }

          .timer-separator {
            font-size: 14px;
          }
        }
      }

      .pricing-display {
        flex-direction: column;
        gap: 20px;

        .original-price .price-value {
          font-size: 24px;
        }

        .current-price .price-value {
          font-size: 36px;
        }
      }

      .promotion-features {
        grid-template-columns: 1fr;
        gap: 8px;

        .feature-highlight {
          padding: 10px 12px;
          font-size: 13px;
        }
      }

      .promotion-notice {
        font-size: 12px;
        padding: 10px;
      }
    }

    .modal-footer {
      padding: 20px;

      .footer-content {
        flex-direction: column;
        gap: 16px;
      }

      .btn {
        width: 100%;
        order: 2;
      }

      .no-reminder-checkbox {
        order: 1;
        justify-content: center;
      }
    }
  }
}

@media (max-width: 480px) {
  .membership-upgrade-modal {
    .enterprise-advantages {
      .advantages-title {
        margin-bottom: 20px;

        h3 {
          font-size: 18px;
        }

        p {
          font-size: 13px;
        }
      }

      .advantage-section {
        padding: 16px;

        .advantage-header {
          flex-direction: column;
          text-align: center;
          gap: 12px;
          margin-bottom: 16px;

          .advantage-icon {
            width: 48px;
            height: 48px;
            align-self: center;

            i {
              font-size: 20px;
            }
          }

          .advantage-title {
            h4 {
              font-size: 16px;
            }

            p {
              font-size: 12px;
            }
          }
        }

        .advantage-features {
          gap: 8px;

          .feature-item {
            padding: 12px;
            gap: 12px;

            .feature-icon {
              width: 32px;
              height: 32px;

              i {
                font-size: 16px;
              }
            }

            .feature-content {
              h5 {
                font-size: 13px;
                margin-bottom: 4px;
              }

              p {
                font-size: 11px;
              }
            }
          }
        }
      }
    }

    .limitation-notice {
      flex-direction: column;
      text-align: center;
      gap: 12px;
    }
  }
}
</style>
