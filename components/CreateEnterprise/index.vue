<template>
  <a-modal v-model:open="visible" :footer="null" :closable="true" :width="640">
    <div class="text-center p-5">
      <h2 class="mb-7.5 text-5.5 font-normal">请选择企业类型</h2>
      <div class="flex justify-around">
        <div
          class="w-50 p-5 border border-solid border-[#e8e8e8] cursor-pointer transition-all duration-300 hover:shadow-[0_4px_12px_rgba(0,0,0,0.1)] hover:border-primary"
          @click="selectType('manufacturer')"
        >
          <img src="~/assets/images/manufactor.png" alt="制造商图标" class="w-40 h-40 mb-2" />
          <p class="text-4 m-0">我是需求方</p>
        </div>
        <div
          class="w-50 p-5 border border-solid border-[#e8e8e8] cursor-pointer transition-all duration-300 hover:shadow-[0_4px_12px_rgba(0,0,0,0.1)] hover:border-primary"
          @click="selectType('supplier')"
        >
          <img src="~/assets/images/supplier.png" alt="供应商图标" class="w-40 h-40 mb-2" />
          <p class="text-4 m-0">我是供应方</p>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
const userStoreObj = userStore()
const { user } = storeToRefs(userStoreObj)

const router = useRouter()
const visible = ref(false)

const show = () => {
  visible.value = true
}

const selectType = (type) => {
  if (type === 'manufacturer') {
    auth('/enterpriseCenter/create')
  } else {
    const url = useRuntimeConfig().public.VITE_BOMAI_URL + '/brand-join/settle'
    window.open(url, '_blank')
  }
}

const auth = (url) => {
  if (!user.value.userMobile) {
    message.info('请先在 个人中心 - 账号绑定 中绑定手机号')
    return
  }
  router.push(url)
}

defineExpose({
  show,
})
</script>

<style scoped>
.enterprise-type-selection {
  text-align: center;
  padding: 20px;
}

.title {
  margin-bottom: 30px;
  font-size: 22px;
}

.selection-container {
  display: flex;
  justify-content: space-around;
}

.option {
  width: 200px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.option:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #1890ff;
}

.option img {
  width: 80px;
  height: 80px;
  margin-bottom: 15px;
}

.option p {
  font-size: 16px;
  margin: 0;
}
</style>
