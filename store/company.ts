import { defineStore } from 'pinia'

export const companyStore = defineStore('companyStore', () => {
  const company = ref<Obj>({})

  const _company_id = useCookie('_company', {
    domain: useRuntimeConfig().public.VITE_DOMAIN,
    default: () => '',
  })
  function setCompany(obj: any = {}) {
    if (!obj.shopCompanyId) {
      obj = {
        shopCompanyId: 0,
      }
    }
    company.value = obj
    _company_id.value = obj.shopCompanyId
  }

  function clear() {
    company.value = {}
    _company_id.value = ''
  }

  const isPersonal = computed(() => company.value.shopCompanyId == 0)

  return { setCompany, company, clear, isPersonal }
})
