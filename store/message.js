export const usemessageStore = defineStore('message', () => {
  const _count = ref(0)
  let load = false
  let timer = null

  const updatemsgCount = async () => {
    const user = userStore()
    if (!user.user.userId) return
    const [err, res] = await try_http('/mall/p/myNotifyLog/unReadCount', {
      params: {
        userId: user.user.userId,
      },
    })
    if (!err) {
      _count.value = res.data
      load = true
    }
  }
  const msgCount = computed(() => {
    const user = userStore()

    if (!load && user.user.userId) {
      updatemsgCount()

      if (timer) {
        clearInterval(timer)
        timer = null
      }
      if (import.meta.client) {
        timer = setInterval(() => {
          updatemsgCount()
        }, 1000 * 60)
      }
    }
    return _count.value
  })

  return {
    updatemsgCount,
    msgCount,
  }
})
