import { defineStore } from 'pinia'
import { authMenuStore } from './authMenu'
import { companyStore } from './company'

export enum ApplyStatus {
  NONE,
  PROCESSING,
  FINISH,
}

export enum MemberLevel {
  /**
   * 普通会员
   */
  NORMAL,
  /**
   *认证会员
   */
  AUTHENTICATION,
  /**
   * 推广会员
   */
  GENERALIZATION,
  /**
   * 专业会员
   */
  PROFESSIONAL,
}

export type User = {
  userId: string
  balance: number
  growth: number
  levelType: number
  mobile: string
  nickName: string
  pic: string
  userMemo: number
  score: number
  sex: string
  status: number
  unionId: string
  userMobile: string
  username: string
  userMail: string
  birthDate: string
  position: string
  personalProfile: string
  isSuperAdmin: number
  wxUnionId: string
  tuName: string
  realName: string
  identity: string
  company: string
  uniqueKey: string
  workPermit: string
  level: MemberLevel
  upgradeStatus: ApplyStatus
}
export const userStore = defineStore('userStore', () => {
  const user = ref({} as User)

  const token = useCookie(TOKEN, {
    domain: useRuntimeConfig().public.VITE_DOMAIN,
  })

  const _user = useCookie<obj>('_user', {})

  function setUser(obj: any) {
    user.value = obj
    _user.value = obj
  }

  function clearUser() {
    const company = companyStore()
    user.value = {} as User
    company.clear()
    authMenuStore().setAuthMenu({})
    token.value = undefined
  }
  return { setUser, clearUser, user }
})
