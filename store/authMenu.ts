export const authMenuStore = defineStore('authMenuStore', () => {
  const authMenu = ref<Obj>({})

  function setAuthMenu(obj: any) {
    authMenu.value = obj
    setMenuMap(obj.menuList || [])
  }
  
  function setMenuMap(list: Array) {
    // 企业空间内的菜单Key
    const menuItemKeys = []
    const dfs = (list) => {
      list.forEach(item => {
        if (item.type == 1 && !item.perms) {
          menuItemKeys.push(item.url)
        }
        if (item.list?.length) {
          dfs(item.list)
        }
      })
    }
    dfs(list)
    authMenu.value.menuItemKeys = menuItemKeys
  }
  
  return { setAuthMenu, authMenu }
})
