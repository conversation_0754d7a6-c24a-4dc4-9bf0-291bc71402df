<template>
  <a-config-provider
    :locale="zhCN"
    :theme="{
      algorithm: theme.darkAlgorithm,
      token: {
        borderRadius: 2,

        colorBgBase: '#1a1a22',
        colorPrimary: '#f94c30',
        colorInfo: '#f94c30',
        colorLink: '#F94C30',
        colorLinkActive: '#d4301e',
        colorLinkHover: '#ff7559',
        colorBgContainer: '#2a2a35',
        colorBgSpotlight: '#2a2a35',
        colorBgElevated: '#2a2a35',
      },
      components: {
        Slider: {
          colorPrimaryBorder: '#f94c30',
          colorPrimaryBorderHover: '#e86b52',
        },
        Menu: {
          colorItemTextHoverHorizontal: '#1677ff',
          controlItemBgActive: '#2b1513',
          colorPrimaryBorder: '#59251c',
          colorErrorBg: '#2c1618',
        },
      },
    }"
  >
    <a-extract-style>
      <nuxt-layout>
        <nuxt-page></nuxt-page>
      </nuxt-layout>
    </a-extract-style>
  </a-config-provider>
</template>
<script setup>
import dayjs from 'dayjs'
import { theme } from 'ant-design-vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import { useHead } from 'unhead'

dayjs.locale('zh-cn')

useHead({
  titleTemplate: (title) => {
    return title ? `${title} - 研选工场` : '研选工场'
  },
  script: [
    {
      src: 'https://hm.baidu.com/hm.js?719887b2162a5aea52260188f310f6ba',
    },
  ],
})

const user = userStore()
const company = companyStore()
const isLogin = useLoginState()
useEventListener('visibilitychange', async () => {
  if (!isLogin.value) return
  if (document.hidden) return
  const [err, res] = await try_http('/mall/shop/userSpace/getOneByCond', {
    params: {
      userMobile: user.user.userMobile,
    },
  })
  if (err) return
  if (company.company.shopCompanyId != res.data.merchantId) {
    message.warn('检测到您切换了企业空间，正在重新加载')
    setTimeout(() => {
      window.location.reload()
    }, 1000)
  }
})
</script>
