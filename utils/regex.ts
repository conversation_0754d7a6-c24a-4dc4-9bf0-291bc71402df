export const highlightExp = (input: string) => {
  const regex = /<em>(.*?)<\/em>/g
  const matches = [...input.matchAll(regex)]

  const result = matches.map((match) => match[1])
  return result
}

export const PASSWORD_REG = {
  reg: /^[a-zA-Z0-9~!@#$%^&*()_+-=]{6,32}$/,
  msg: '最短6位，最长32位，支持英文字母、数字以及特殊符号',
}
export const EMAIL_REG = {
  reg: /^([\.a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/,
  msg: '请输入正确的邮箱',
}
export const MOBILE_REG = {
  reg: /^1[1-9]{1}\d{9}$/,
  msg: '请输入正确的手机号',
}
export const USER_NAME_REG = {
  reg: /^[a-zA-Z0-9_\u4e00-\u9fa5\s]{1,31}$/,
  msg: '1-32位，支持中英文、数字和下划线，以中文或英文开头',
}

export const SOCIAL_CREDIT_CODE_REG = {
  reg: /[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}/,
  msg: '请输入正确的统一社会信用代码',
}

export const ID_CARD_REG = {
  reg: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
  msg: '请输入正确的身份证代码',
}

// 银行卡号
export const BANK_CARD_REG = {
  reg: /^([1-9]{1})(\d{11}|\d{15}|\d{16}|\d{17}|\d{18}|\d{19}|\d{20})$/,
  msg: '请输入正确的银行卡卡号',
}

export const FILENAME_REG = {
  reg: /^[\u4E00-\u9FA5a-zA-Z0-9_-]{1,64}$/,
  msg: '最长64个字符，只能包含数字、字母、中文、中划线、下划线',
}

// IP正则
export const HOST_REG =
  '(^(?=^.{3,255}$)(http(s)?://)?(www.)?[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:d+)*(/w+.w+)*$)|(^([1-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])(.([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])){3}$)'
// 端口正则
export const PORT_REG =
  /^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/
