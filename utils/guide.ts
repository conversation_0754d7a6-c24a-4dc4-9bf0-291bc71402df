import MemberGuide from '@/components/MemberGuide/index.vue'
import { createVNode, render } from 'vue'
const instance = ref<InstanceType<typeof MemberGuide>>()
const cbFn = ref<Function>()
export class Guide {
  static show(cb?: Function) {
    cbFn.value = cb
    if (!instance.value) {
      const el = document.createElement('div')
      const ctx = useNuxtApp().vueApp._context
      const Comp = defineComponent({
        setup() {
          return () =>
            h(MemberGuide, {
              ref: instance,
              onUpgrade: () => {
                cbFn.value?.()
              },
            })
        },
      })
      const node = createVNode(Comp)
      node.appContext = ctx
      render(node, el)
      document.body.appendChild(el)
    }
    nextTick(() => {
      instance.value?.showPop()
    })
  }
}
