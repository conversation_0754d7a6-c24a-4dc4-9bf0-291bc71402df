type ExtractPromise<T> = T extends Promise<infer P> ? P : unknown
/**
 * ### 使用方式
 * 包装异步函数，代替trycatch
 * 自动推导函数返回类型
 * ```
 *  const [err, res] = await _try(() => http<string>('/mall/test'))
 *  if (!err) {
 *    console.log('res', res)
 *  }
 * ```
 */
export const _try = async <T extends AsyncFunction>(
  func: T,
): Promise<[Error, undefined] | [undefined, ExtractPromise<ReturnType<T>>]> => {
  try {
    return [void 0, await func()]
  } catch (err) {
    return [err as any, void 0]
  }
}
