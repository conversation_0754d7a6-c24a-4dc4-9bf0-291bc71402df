import { fetchEventSource } from '@microsoft/fetch-event-source'

export type From = 'official' | 'third'

export type Ask = {
  id: string
  type: 'user'
  ask: string
}

export type Tool =
  | {
      type: 'tool'
      use: 'info'
      title: string
    }
  | {
      type: 'tool'
      use: 'processes'
      current: number
      title: string
      processes: Array<{
        name: string
        end: number
        maxTimeMs: number
      }>
    }

export type Ans = {
  id: string
  type: 'answer'
  answer: string
  ask: string
  isFinished: boolean
  isOpeningWords?: boolean
  message_id: string
  fav?: string
  user: string
  tool?: Tool
}

export type Recommend = {
  id: string
  type: 'recommend'
  recommends: string[]
}

export type ChatItem = Ask | Ans | Recommend
type ChatOption = {
  shop_id?: string
  session_id?: string
  user_id: string
  afterClose?: Function
  base: string
}

type AskOpts = {
  isForm?: boolean
  files?: string[]
}

class Chat {
  agent: obj = {}
  tags: obj[] = []
  shop_id = ''
  session_id = ''
  user_id = ''
  generating: boolean = false
  currentList: ChatItem[] = []
  historyList: ChatItem[] = []
  control?: AbortController // 通过信号来暂停请求
  currentAns = {} as Ans
  effect?: Function
  afterClose?: Function
  hasTitle = false
  base = ''
  constructor({ shop_id = '', session_id = '', user_id, base, afterClose }: ChatOption) {
    this.shop_id = shop_id
    this.session_id = session_id
    this.user_id = user_id
    this.effect = effect
    this.afterClose = afterClose
    this.base = base
  }

  async initAgent() {
    const [err, res] = await useCacheFetch(`agent-${this.shop_id}`, () =>
      try_http('/mall/p/llm/agent/getConfigByAgentId', {
        query: getshopsign(
          {
            agentId: this.shop_id,
          },
          'get'
        ),
        headers: {
          grantType: 'sign',
        },
      })
    )
    if (!err) {
      this.agent = JSON.parse(res.data)
    }
  }

  loadStart() {
    this.currentList.push({
      id: 'start' + genUUID(),
      type: 'answer',
      isFinished: true,
      answer: this.agent.openingWords || '',
      ask: '',
      message_id: '',
      fav: '',
      user: this.user_id,
      isOpeningWords: true,
    })
    if (this.agent.recommendQuestion) {
      this.currentList.push({
        id: 'recommend' + genUUID(),
        type: 'recommend',
        recommends: this.agent.recommendQuestion.split(','),
      })
    }
  }

  async loadHistory() {
    const query = getshopsign(
      {
        userId: this.user_id,
        conversationId: this.session_id,
        agentId: this.shop_id,
        limit: 100,
      },
      'get'
    )
    const [err, res] = await try_http('/mall/p/llm/agent/messages', {
      method: 'get',
      params: query,
      headers: {
        grantType: 'sign',
      },
    })
    if (!err) {
      const list: ChatItem[] = []
      const _data = JSON.parse(res.data)
      for (const item of _data.data) {
        const ask: Ask = {
          id: 'ask' + item.id,
          type: 'user',
          ask: item.query,
        }
        const ans: Ans = {
          id: 'ans' + item.id,
          type: 'answer',
          ask: item.query,
          answer: item.answer,
          isFinished: true,
          message_id: item.id,
          fav: item.feedback?.rating ?? '',
          user: this.user_id,
        }
        list.push(ask, ans)
      }
      this.historyList = list
    }
  }

  get chatList() {
    return [...this.historyList, ...this.currentList].slice().reverse()
  }

  removeRecommend() {
    const last = this.currentList.at(-1)
    if (last?.type == 'recommend') {
      this.currentList.pop()
    }
  }

  ask(query: string, opts: AskOpts = {}) {
    if (this.generating) {
      this.control?.abort()
      const item = this.currentList.at(-1) as Ans
      item.isFinished = true
      this.generating = false
      return
    } else if (!query) {
      return
    }
    this.removeRecommend()
    const ask: Ask = {
      id: 'ask' + genUUID(),
      type: 'user',
      ask: query,
    }
    this.currentList.push(ask)
    this.addAns(query, opts)
  }

  addAns(query: string, opts: AskOpts = {}) {
    this.currentAns = {
      id: 'ans' + genUUID(),
      type: 'answer',
      isFinished: false,
      answer: '',
      ask: query,
      message_id: '',
      fav: '',
      user: this.user_id,
    }
    this.currentList.push(this.currentAns)
    this.control = new AbortController()

    nextTick(() => {
      this.handleFetch(query, opts)
    })
  }

  handleFetch(ask: string, opts: AskOpts = {}) {
    this.generating = true
    const inputs: obj = {}
    if (this.tags.length) {
      inputs.prods = this.tags.map((item) => {
        const isSku = item.isSku
        if (!isSku) {
          return {
            prod_id: item.id,
            prod_name: item.productName,
            model_code: '',
            from_brand: item.website.websiteName,
            price: item.lowestPrice ? item.lowestPrice + '起' : '',
            delivery: getTradeTerm(item.lowestTradeTerm, '最快'),
          }
        } else {
          return {
            prod_id: item.id,
            prod_name: item.productName,
            model_code: item.skuCode,
            from_brand: item.brandName,
            price: has(item.price) ? item.price.toString() : '',
            delivery: getTradeTerm(item.tradeTerm),
          }
        }
      })
    }
    if (opts.files?.length) {
      inputs.pictures = opts.files
    }
    if (opts.isForm) {
      inputs.formSubmitted = true
    }
    const body = getshopsign(
      {
        inputs: JSON.stringify(inputs),
        query: ask,
        user: this.user_id,
        conversation_id: this.session_id,
        response_mode: 'streaming',
        agentId: this.shop_id,
      },
      'post'
    )

    this.control = new AbortController()
    fetchEventSource(`${this.base}/mall/p/llm/agent/streamingChat`, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        grantType: 'sign',
      },
      body: JSON.stringify(body),
      signal: this.control.signal,
      openWhenHidden: true,
      onmessage: (_msg) => {
        try {
          if (this.currentAns.tool) {
            if (this.currentAns.tool.use == 'info') {
              this.currentAns.tool = undefined
            } else {
              this.currentAns.tool.current = 1
              setTimeout(() => {
                this.currentAns.tool = undefined
              }, 500)
            }
          }

          const data = JSON.parse(_msg.data)
          if (!this.session_id) {
            this.session_id = data.conversation_id
            this.changeTitle()
          }
          if (data.event == 'message' || data.event == 'agent_message') {
            this.currentAns.answer += data.answer
            this.currentAns.message_id = data.message_id
          } else if (data.event == 'tool') {
            if (data.processes) {
              this.currentAns.tool = {
                type: 'tool',
                use: 'processes',
                title: data.title,
                processes: data.processes,
                current: 0,
              }
            } else {
              this.currentAns.tool = {
                type: 'tool',
                use: 'info',
                title: data.title,
              }
            }
          }
        } catch (err) {
          console.log('%c Line:41 🥛 err', 'color:#515720', err, _msg.data)
        }
      },
      onclose: () => {
        this.currentAns.isFinished = true
        this.generating = false
        this.getRecommned()
        this.afterClose?.()
      },
      onerror: (err) => {
        console.log('%c Line:167 🥛 err', 'color:#C542AC', err)
      },
    })
  }

  changeTitle() {
    setTimeout(async () => {
      const body = getshopsign(
        {
          name: this.currentAns.ask,
          agentId: this.shop_id,
          user: this.user_id,
          conversation_id: this.session_id,
        },
        'post'
      )
      await try_http(`/mall/p/llm/agent/conversations/name`, {
        method: 'post',
        body,
        headers: {
          grantType: 'sign',
        },
      })
    })
  }

  async getRecommned() {
    const body = getshopsign(
      {
        user: this.user_id,
        conversation_id: this.session_id,
        agentId: this.shop_id,
      },
      'post'
    )

    const [err, res] = await try_http('/mall/p/llm/agent/suggestedQuestions', {
      method: 'post',
      body,
      headers: {
        grantType: 'sign',
      },
    })
    if (!err) {
      let data = JSON.parse(res.data)
      if (data.length) {
        this.currentList.push({
          id: 'recommend' + genUUID(),
          type: 'recommend',
          recommends: data,
        })
      }
    }
  }

  addTag(tag: obj) {
    const find = this.tags.find((item) => item._id == tag._id)
    if (!find) {
      this.tags.push(tag)
    }
  }

  removeTag(tag: obj) {
    const idx = this.tags.findIndex((item) => item._id == tag._id)
    if (idx > -1) {
      this.tags.splice(idx, 1)
    }
  }

  newChat() {
    this.tags = []
    this.currentList = []
    this.historyList = []
    this.session_id = ''
  }
}

export default Chat
