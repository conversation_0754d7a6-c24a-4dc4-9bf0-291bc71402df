export default (price?: number | string, prefix = '¥') => {
  if (!has(price)) return
  const s = Number(price).toFixed(2)
  const [main, float] = s.split('.')
  const list: string[] = []
  let temp = ''
  const len = main.length
  for (let i = len - 1; i >= 0; i--) {
    const ch = main[i]
    if (temp.length < 3) {
      temp = ch + temp
    }
    if (temp.length == 3) {
      list.unshift(temp)
      temp = ''
    }
  }
  temp && list.unshift(temp)
  return prefix + list.join(',') + '.' + float
}
