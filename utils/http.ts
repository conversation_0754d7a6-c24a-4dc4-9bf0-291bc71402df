import type { NitroFetchOptions } from 'nitropack/types'

export type Response<T = any> = {
  code: string
  data: T
  message: string
}

export type MallResponse<T = any> = {
  code: string
  data: T
  msg: string
  sign: string
  success: boolean
}

function http<T = any>(url: `/mall${string}`, opts?: NitroFetchOptions<`/mall${string}`>): Promise<MallResponse<T>>
function http<T = any>(url: string, opts?: NitroFetchOptions<string>): Promise<Response<T>>
function http<T = any>(url: string, opts?: NitroFetchOptions<string>): Promise<MallResponse<T> | Response<T>> {
  return useNuxtApp().$_fetch(url, opts) as any
}

interface HttpMethod {
  <T = any>(url: `/mall${string}`, opts?: obj): Promise<MallResponse<T>>
  <T = any>(url: string, opts?: obj): Promise<Response<T>>
}

const post: HttpMethod = <T = any>(url: string, body: obj = {}) => {
  return http<T>(url, {
    method: 'post',
    body,
  }) as any
}

const get: HttpMethod = <T = any>(url: string, params: obj = {}) => {
  return http<T>(url, {
    method: 'get',
    params,
  }) as any
}
const put: HttpMethod = <T = any>(url: string, body: obj = {}) => {
  return http<T>(url, {
    method: 'put',
    body,
  }) as any
}
const del: HttpMethod = <T = any>(url: string, body: obj = {}) => {
  return http<T>(url, {
    method: 'delete',
    body,
  }) as any
}

http.post = post
http.delete = del
http.put = put
http.get = get

http.upload = <T = any>(url: string, file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  return useNuxtApp().$_fetch(url, {
    method: 'post',
    body: formData,
  }) as unknown as Response<T>
}

type TryResult<T> = [null, T] | [Error, null]

interface TryHttp {
  <T = any>(url: `/mall${string}`, opts?: obj): Promise<TryResult<MallResponse<T>>>
  <T = any>(url: string, opts?: obj): Promise<TryResult<Response<T>>>
}

export const try_http: TryHttp = async <T = any>(url: string, opts: obj = {}) => {
  try {
    const res = await http<T>(url, opts)
    return [null, res] as any
  } catch (err) {
    return [err, null] as any
  }
}

export { http }
export const Http = http
