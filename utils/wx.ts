import wx from '~/assets/wx.css?raw'
import bind from '~/assets/bind.css?raw'
import { encode } from 'js-base64'

const map = {
  wx,
  bind,
}

export const wxutils = (el, css, opts: obj = {}) => {
  // @ts-ignore
  new WxLogin(
    Object.assign(
      {
        self_redirect: false,
        id: el,
        appid: 'wx4b83c1f5da5e05d6',
        scope: 'snsapi_login',
        redirect_uri: encodeURIComponent(`https://www.yanxuan.cloud/mall/wx/callback`),
        fast_login: 0,
        state: Math.ceil(Math.random() * 1000),
        style: 'white',
        href:
          'data:text/css;base64,' +
          // '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',
          encode(map[css]),
      },
      opts,
    ),
  )
}
