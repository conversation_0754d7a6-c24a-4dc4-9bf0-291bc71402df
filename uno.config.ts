import {
  defineConfig,
  presetUno,
  presetAttributify,
  presetIcons,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'
import { FileSystemIconLoader } from '@iconify/utils/lib/loader/node-loaders'

export default defineConfig({
  presets: [
    presetUno(),
    presetAttributify(),
    presetIcons({
      collections: {
        a: () => import('@iconify-json/ant-design/icons.json').then((i) => i.default),
        custom: FileSystemIconLoader('./assets/icons', (svg) => svg.replace(/^<svg /, '<svg fill="currentColor" ')),
        fa: () => import('@iconify-json/fa-solid/icons.json').then((i) => i.default),
        fab: () => import('@iconify-json/fa-brands/icons.json').then((i) => i.default),
      },
      extraProperties: {
        display: 'inline-block',
        width: '1em',
        height: '1em',
      },
    }),
  ],
  transformers: [transformerDirectives(), transformerVariantGroup()],
  shortcuts: {
    'flex-center': 'flex justify-center items-center',
    link: 'cursor-pointer hover:text-primary',
    'menu-item':
      'h-12.5 flex items-center justify-between px-3 bg-primaryBg text-primaryText transition-colors duration-300 hover:color-primary cursor-pointer',
    'button-link': 'color-primary hover:text-primaryText transition-colors cursor-pointer',
    'card-shaodw': 'shadow-[0_3px_25px_#1c344f1a]',
  },
  theme: {
    colors: {
      primary: '#F94C30',
      primaryBg: '#2A2A35',
      primaryBgTransparent: '#2A2A35CC',
      darkBg: '#1A1A22',
      textBg: '#353541',
      primaryText: 'rgba(255,255,255,.85)',
      subText: '#7F7F7F',
      success: '#52c41a',
    },
  },
})
